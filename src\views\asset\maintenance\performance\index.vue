<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import * as echarts from 'echarts'
import { Download } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import request from '@/config/axios'

// 图表实例
let repairTypeChart: echarts.ECharts | null = null
let costTrendChart: echarts.ECharts | null = null
let technicianChart: echarts.ECharts | null = null
let assetRepairChart: echarts.ECharts | null = null

// 统计数据
const statistics = reactive({
  totalCount: 0,
  avgResponseTime: 0,
  avgRepairTime: 0,
  totalCost: 0,
  completionRate: 0,
  repeatRepairRate: 0
})

// 筛选条件
const filterParams = reactive({
  timeRange: 'month',
  dateRange: [] as string[],
  assetType: '',
  department: ''
})

// 维修人员列表
interface Technician {
  id: string
  name: string
  repairCount: number
  avgTime: number
  totalCost: number
  satisfaction: number
}

const technicianList = ref<Technician[]>([])

// 资产类型选项
const assetTypeOptions = [
  { value: '', label: '全部类型' },
  { value: 'production', label: '生产设备' },
  { value: 'office', label: '办公设备' },
  { value: 'vehicle', label: '运输设备' },
  { value: 'building', label: '房屋建筑' }
]

// 部门选项
const departmentOptions = [
  { value: '', label: '全部部门' },
  { value: 'production', label: '生产部' },
  { value: 'admin', label: '行政部' },
  { value: 'tech', label: '技术部' }
]

// 时间范围选项
const timeRangeOptions = [
  { value: 'week', label: '最近一周' },
  { value: 'month', label: '最近一个月' },
  { value: 'quarter', label: '最近一季度' },
  { value: 'year', label: '最近一年' },
  { value: 'custom', label: '自定义范围' }
]

// 获取统计数据
const fetchStatistics = async () => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 300))
    
    // 模拟数据
    statistics.totalCount = 287
    statistics.avgResponseTime = 3.5
    statistics.avgRepairTime = 12.8
    statistics.totalCost = 156800
    statistics.completionRate = 94.5
    statistics.repeatRepairRate = 6.2
    
    // 模拟技术员数据
    technicianList.value = [
      {
        id: '1',
        name: '李工',
        repairCount: 87,
        avgTime: 10.5,
        totalCost: 48500,
        satisfaction: 4.8
      },
      {
        id: '2',
        name: '王工',
        repairCount: 65,
        avgTime: 14.2,
        totalCost: 37200,
        satisfaction: 4.5
      },
      {
        id: '3',
        name: '张工',
        repairCount: 73,
        avgTime: 12.7,
        totalCost: 42100,
        satisfaction: 4.7
      },
      {
        id: '4',
        name: '赵工',
        repairCount: 62,
        avgTime: 13.9,
        totalCost: 29000,
        satisfaction: 4.3
      }
    ]
  } catch (error) {
    console.error('获取统计数据失败:', error)
    ElMessage.error('获取统计数据失败')
  }
}

// 初始化维修类型分布图表
const initRepairTypeChart = () => {
  const chartDom = document.getElementById('repairTypeChart')
  if (!chartDom) return
  
  repairTypeChart = echarts.init(chartDom)
  const option = {
    title: {
      text: '维修类型分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: ['硬件故障', '软件故障', '网络故障', '电力故障', '其他故障']
    },
    series: [
      {
        name: '维修类型',
        type: 'pie',
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 135, name: '硬件故障' },
          { value: 75, name: '软件故障' },
          { value: 42, name: '网络故障' },
          { value: 23, name: '电力故障' },
          { value: 12, name: '其他故障' }
        ]
      }
    ]
  }
  
  repairTypeChart.setOption(option)
}

// 初始化维修成本趋势图表
const initCostTrendChart = () => {
  const chartDom = document.getElementById('costTrendChart')
  if (!chartDom) return
  
  costTrendChart = echarts.init(chartDom)
  const option = {
    title: {
      text: '维修成本趋势',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['人工成本', '配件成本', '总成本'],
      bottom: 10
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: {
      type: 'value',
      name: '成本(元)'
    },
    series: [
      {
        name: '人工成本',
        type: 'line',
        stack: 'Total',
        areaStyle: {},
        emphasis: { focus: 'series' },
        data: [12000, 15000, 14000, 16000, 13000, 12800]
      },
      {
        name: '配件成本',
        type: 'line',
        stack: 'Total',
        areaStyle: {},
        emphasis: { focus: 'series' },
        data: [8000, 10000, 9500, 11000, 9000, 8500]
      },
      {
        name: '总成本',
        type: 'line',
        emphasis: { focus: 'series' },
        data: [20000, 25000, 23500, 27000, 22000, 21300]
      }
    ]
  }
  
  costTrendChart.setOption(option)
}

// 初始化技术员绩效图表
const initTechnicianChart = () => {
  const chartDom = document.getElementById('technicianChart')
  if (!chartDom) return
  
  technicianChart = echarts.init(chartDom)
  const option = {
    title: {
      text: '技术员维修绩效',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['维修单数', '平均用时'],
      bottom: 10
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: technicianList.value.map(tech => tech.name)
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '维修单数',
        position: 'left'
      },
      {
        type: 'value',
        name: '平均用时(小时)',
        position: 'right'
      }
    ],
    series: [
      {
        name: '维修单数',
        type: 'bar',
        data: technicianList.value.map(tech => tech.repairCount)
      },
      {
        name: '平均用时',
        type: 'line',
        yAxisIndex: 1,
        data: technicianList.value.map(tech => tech.avgTime)
      }
    ]
  }
  
  technicianChart.setOption(option)
}

// 初始化资产故障频率图表
const initAssetRepairChart = () => {
  const chartDom = document.getElementById('assetRepairChart')
  if (!chartDom) return
  
  assetRepairChart = echarts.init(chartDom)
  const option = {
    title: {
      text: '资产故障频率TOP10',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '8%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      name: '维修次数'
    },
    yAxis: {
      type: 'category',
      data: ['资产A', '资产B', '资产C', '资产D', '资产E', '资产F', '资产G', '资产H', '资产I', '资产J'].reverse()
    },
    series: [
      {
        name: '维修次数',
        type: 'bar',
        data: [12, 10, 9, 8, 7, 6, 5, 5, 4, 4].reverse(),
        label: {
          show: true,
          position: 'right'
        }
      }
    ]
  }
  
  assetRepairChart.setOption(option)
}

// 时间范围变化
const handleTimeRangeChange = () => {
  if (filterParams.timeRange !== 'custom') {
    // 根据选择的时间范围设置日期
    const now = new Date()
    const end = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    let start
    
    switch (filterParams.timeRange) {
      case 'week':
        start = new Date(end)
        start.setDate(end.getDate() - 7)
        break
      case 'month':
        start = new Date(end)
        start.setMonth(end.getMonth() - 1)
        break
      case 'quarter':
        start = new Date(end)
        start.setMonth(end.getMonth() - 3)
        break
      case 'year':
        start = new Date(end)
        start.setFullYear(end.getFullYear() - 1)
        break
    }
    
    if (start) {
      filterParams.dateRange = [
        start.toISOString().split('T')[0],
        end.toISOString().split('T')[0]
      ]
    }
  }
  
  refreshData()
}

// 筛选条件变化
const handleFilterChange = () => {
  refreshData()
}

// 刷新数据
const refreshData = async () => {
  await fetchStatistics()
  
  // 重新初始化图表
  if (repairTypeChart) repairTypeChart.dispose()
  if (costTrendChart) costTrendChart.dispose()
  if (technicianChart) technicianChart.dispose()
  if (assetRepairChart) assetRepairChart.dispose()
  
  initRepairTypeChart()
  initCostTrendChart()
  initTechnicianChart()
  initAssetRepairChart()
}

// 导出报表
const handleExport = () => {
  ElMessage.success('报表导出成功')
}

// 页面加载时初始化
onMounted(async () => {
  await fetchStatistics()
  initRepairTypeChart()
  initCostTrendChart()
  initTechnicianChart()
  initAssetRepairChart()
  
  // 监听窗口大小变化，重绘图表
  window.addEventListener('resize', () => {
    repairTypeChart?.resize()
    costTrendChart?.resize()
    technicianChart?.resize()
    assetRepairChart?.resize()
  })
})
</script>

<template>
  <div class="maintenance-performance-container">
    <!-- 筛选条件 -->
    <el-card class="filter-card">
      <div class="filter-container">
        <div class="filter-item">
          <span class="filter-label">时间范围：</span>
          <el-select v-model="filterParams.timeRange" @change="handleTimeRangeChange">
            <el-option
              v-for="item in timeRangeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        
        <div class="filter-item" v-if="filterParams.timeRange === 'custom'">
          <el-date-picker
            v-model="filterParams.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            @change="handleFilterChange"
          />
        </div>
        
        <div class="filter-item">
          <span class="filter-label">资产类型：</span>
          <el-select v-model="filterParams.assetType" @change="handleFilterChange">
            <el-option
              v-for="item in assetTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        
        <div class="filter-item">
          <span class="filter-label">部门：</span>
          <el-select v-model="filterParams.department" @change="handleFilterChange">
            <el-option
              v-for="item in departmentOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        
        <div class="filter-item">
          <el-button type="primary" icon="Download" @click="handleExport">导出报表</el-button>
        </div>
      </div>
    </el-card>

    <!-- 统计卡片 -->
    <div class="stat-cards">
      <el-row :gutter="20">
        <el-col :span="4">
          <el-card shadow="hover" class="stat-card">
            <template #header>
              <div class="card-header">维修总数</div>
            </template>
            <div class="card-value">{{ statistics.totalCount }}</div>
            <div class="card-footer">单</div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card shadow="hover" class="stat-card">
            <template #header>
              <div class="card-header">平均响应时间</div>
            </template>
            <div class="card-value">{{ statistics.avgResponseTime }}</div>
            <div class="card-footer">小时</div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card shadow="hover" class="stat-card">
            <template #header>
              <div class="card-header">平均维修时间</div>
            </template>
            <div class="card-value">{{ statistics.avgRepairTime }}</div>
            <div class="card-footer">小时</div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card shadow="hover" class="stat-card">
            <template #header>
              <div class="card-header">维修总成本</div>
            </template>
            <div class="card-value">{{ (statistics.totalCost / 10000).toFixed(2) }}</div>
            <div class="card-footer">万元</div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card shadow="hover" class="stat-card">
            <template #header>
              <div class="card-header">按时完成率</div>
            </template>
            <div class="card-value">{{ statistics.completionRate }}</div>
            <div class="card-footer">%</div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card shadow="hover" class="stat-card">
            <template #header>
              <div class="card-header">返修率</div>
            </template>
            <div class="card-value">{{ statistics.repeatRepairRate }}</div>
            <div class="card-footer">%</div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <div class="chart-container">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card class="chart-card">
            <div id="repairTypeChart" class="chart"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card class="chart-card">
            <div id="costTrendChart" class="chart"></div>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="20" style="margin-top: 20px">
        <el-col :span="12">
          <el-card class="chart-card">
            <div id="technicianChart" class="chart"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card class="chart-card">
            <div id="assetRepairChart" class="chart"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 技术员绩效表格 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>技术员绩效排名</span>
        </div>
      </template>
      
      <el-table :data="technicianList" border>
        <el-table-column prop="name" label="技术员" width="100" />
        <el-table-column prop="repairCount" label="维修单数" width="100" sortable />
        <el-table-column label="平均维修时间" width="120" sortable>
          <template #default="{ row }">
            {{ row.avgTime }} 小时
          </template>
        </el-table-column>
        <el-table-column label="维修成本" width="150" sortable>
          <template #default="{ row }">
            {{ row.totalCost.toLocaleString() }} 元
          </template>
        </el-table-column>
        <el-table-column label="单均成本" width="120" sortable>
          <template #default="{ row }">
            {{ Math.round(row.totalCost / row.repairCount).toLocaleString() }} 元
          </template>
        </el-table-column>
        <el-table-column label="完成率" width="100">
          <!-- <template #default="{ row }"> -->
            {{ (90 + Math.random() * 10).toFixed(1) }}%
          <!-- </template> -->
        </el-table-column>
        <el-table-column label="满意度评分" width="120" sortable>
          <template #default="{ row }">
            <el-rate
              v-model="row.satisfaction"
              disabled
              text-color="#ff9900"
              :max="5"
              :allow-half="true"
            />
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<style lang="scss" scoped>
.maintenance-performance-container {
  padding: 20px;
  
  .filter-card {
    margin-bottom: 20px;
    
    .filter-container {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      gap: 15px;
      
      .filter-item {
        display: flex;
        align-items: center;
        
        .filter-label {
          margin-right: 8px;
          white-space: nowrap;
        }
      }
    }
  }
  
  .stat-cards {
    margin-bottom: 20px;
    
    .stat-card {
      .card-header {
        text-align: center;
        font-size: 14px;
        color: #606266;
      }
      
      .card-value {
        font-size: 24px;
        font-weight: bold;
        color: #303133;
        text-align: center;
        margin: 10px 0;
      }
      
      .card-footer {
        text-align: center;
        font-size: 12px;
        color: #909399;
      }
    }
  }
  
  .chart-container {
    margin-bottom: 20px;
    
    .chart-card {
      .chart {
        height: 350px;
      }
    }
  }
  
  .table-card {
    .card-header {
      font-weight: bold;
      font-size: 16px;
    }
  }
}
</style> 