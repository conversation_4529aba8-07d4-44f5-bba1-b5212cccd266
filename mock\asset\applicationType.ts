import { MockMethod } from 'vite-plugin-mock'

// 申请场景分类
const categories = [
  { value: 'ASSET_NEW', label: '资产新增申请' },
  { value: 'ASSET_SCRAP', label: '资产报废申请' },
  { value: 'ASSET_TRANSFER', label: '资产调拨申请' },
  { value: 'ASSET_MAINTENANCE', label: '资产维修申请' }
]

// 资产类型
const assetTypes = [
  { value: 'PRODUCTION', label: '生产类资产' },
  { value: 'ADMIN', label: '行政类资产' },
  { value: 'FRANCHISE', label: '特许资产' }
]

// 生成随机数据
const generateData = () => {
  const data = []
  for (let i = 1; i <= 12; i++) {
    const category = categories[Math.floor(Math.random() * categories.length)]
    const assetType = assetTypes[Math.floor(Math.random() * assetTypes.length)]
    data.push({
      id: `AT${String(i).padStart(4, '0')}`,
      name: `${category.label}类型${i}`,
      code: `AT${String(i).padStart(4, '0')}`,
      assetType: assetType.value,
      assetTypeName: assetType.label,
      category: category.value,
      categoryName: category.label,
      enabled: Math.random() > 0.3,
      description: `这是${category.label}类型${i}的详细说明，用于描述该申请类型的具体用途和使用场景。`,
      createTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString()
    })
  }
  return data
}

const mockData = generateData()

export default [
  {
    url: '/api/asset/application-type/list',
    method: 'get',
    response: ({ query }) => {
      const { pageNum = 1, pageSize = 10, category, assetType, enabled, keyword } = query
      let filteredData = [...mockData]
      
      // 按分类过滤
      if (category) {
        filteredData = filteredData.filter(item => item.category === category)
      }
      
      // 按资产类型过滤
      if (assetType) {
        filteredData = filteredData.filter(item => item.assetType === assetType)
      }
      
      // 按启用状态过滤
      if (enabled !== undefined) {
        filteredData = filteredData.filter(item => item.enabled === (enabled === 'true'))
      }
      
      // 关键词搜索
      if (keyword) {
        const lowerKeyword = keyword.toLowerCase()
        filteredData = filteredData.filter(item => 
          item.name.toLowerCase().includes(lowerKeyword) || 
          item.code.toLowerCase().includes(lowerKeyword)
        )
      }
      
      // 分页
      const start = (pageNum - 1) * pageSize
      const end = start + pageSize
      const pageData = filteredData.slice(start, end)
      
      return {
        code: 200,
        msg: 'success',
        data: {
          total: filteredData.length,
          rows: pageData
        }
      }
    }
  },
  {
    url: '/api/asset/application-type/:id',
    method: 'get',
    response: ({ query }) => {
      const { id } = query
      const data = mockData.find(item => item.id === id)
      return {
        code: 200,
        msg: 'success',
        data
      }
    }
  },
  {
    url: '/api/asset/application-type',
    method: 'post',
    response: ({ body }) => {
      const newData = {
        ...body,
        id: `AT${String(mockData.length + 1).padStart(4, '0')}`,
        createTime: new Date().toISOString()
      }
      mockData.push(newData)
      return {
        code: 200,
        msg: 'success',
        data: newData
      }
    }
  },
  {
    url: '/api/asset/application-type',
    method: 'put',
    response: ({ body }) => {
      const index = mockData.findIndex(item => item.id === body.id)
      if (index !== -1) {
        mockData[index] = { ...mockData[index], ...body }
      }
      return {
        code: 200,
        msg: 'success'
      }
    }
  },
  {
    url: '/api/asset/application-type/:id',
    method: 'delete',
    response: ({ query }) => {
      const { id } = query
      const index = mockData.findIndex(item => item.id === id)
      if (index !== -1) {
        mockData.splice(index, 1)
      }
      return {
        code: 200,
        msg: 'success'
      }
    }
  },
  {
    url: '/api/asset/application-type/status',
    method: 'put',
    response: ({ body }) => {
      const { id, enabled } = body
      const index = mockData.findIndex(item => item.id === id)
      if (index !== -1) {
        mockData[index].enabled = enabled
      }
      return {
        code: 200,
        msg: 'success'
      }
    }
  },
  {
    url: '/api/asset/application-type/stats',
    method: 'get',
    response: () => {
      const stats = {
        total: mockData.length,
        enabled: mockData.filter(item => item.enabled).length,
        disabled: mockData.filter(item => !item.enabled).length,
        byCategory: categories.map(category => ({
          name: category.label,
          value: mockData.filter(item => item.category === category.value).length
        })),
        byAssetType: assetTypes.map(type => ({
          name: type.label,
          value: mockData.filter(item => item.assetType === type.value).length
        }))
      }
      return {
        code: 200,
        msg: 'success',
        data: stats
      }
    }
  }
] as MockMethod[] 