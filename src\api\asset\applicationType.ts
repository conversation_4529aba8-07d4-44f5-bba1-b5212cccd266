import request from '@/config/axios'

// 查询申请类型列表
export const listApplicationTypes = (params: any) => {
  return request.get({ url: '/api/asset/application-type/list', params })
}

// 查询申请类型详细
export const getApplicationType = (id: string) => {
  return request.get({ url: `/api/asset/application-type/${id}` })
}

// 新增申请类型
export const addApplicationType = (data: any) => {
  return request.post({ url: '/api/asset/application-type', data })
}

// 修改申请类型
export const updateApplicationType = (data: any) => {
  return request.put({ url: '/api/asset/application-type', data })
}

// 删除申请类型
export const deleteApplicationType = (id: string) => {
  return request.delete({ url: `/api/asset/application-type/${id}` })
}

// 修改申请类型状态
export const changeApplicationTypeStatus = (id: string, enabled: boolean) => {
  return request.put({ url: `/api/asset/application-type/status`, data: { id, enabled } })
}

// 获取申请类型统计
export const getApplicationTypeStats = () => {
  return request.get({ url: '/api/asset/application-type/stats' })
} 