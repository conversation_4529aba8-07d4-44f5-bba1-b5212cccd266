<template>
  <div class="asset-import-container">
    <!-- 页面标题和描述 -->
    <div class="page-header">
      <h2>资产数据导入</h2>
      <p>通过Excel文件批量导入资产数据，支持基础资产、特殊资产、设备资产等多种类型导入</p>
    </div>
    
    <!-- 主要功能Tab -->
    <el-tabs v-model="activeTab" type="card">
      <el-tab-pane label="导入资产" name="import">
        <!-- 导入流程或功能入口列表 -->
        <div v-if="currentStep === 0">
          <el-card class="action-card" shadow="hover">
            <div class="action-item" @click="currentStep = 1">
              <div class="icon-wrapper">
                <i class="el-icon-upload"></i>
              </div>
              <div class="content">
                <div class="title">导入资产数据</div>
                <div class="desc">通过Excel表格批量导入资产数据，支持校验和预览</div>
              </div>
            </div>
            <div class="action-item" @click="downloadTemplate('asset')">
              <div class="icon-wrapper download">
                <i class="el-icon-download"></i>
              </div>
              <div class="content">
                <div class="title">下载导入模板</div>
                <div class="desc">下载标准的Excel导入模板，按照模板填写资产信息</div>
              </div>
            </div>
            <div class="action-item" @click="viewHistory">
              <div class="icon-wrapper history">
                <i class="el-icon-time"></i>
              </div>
              <div class="content">
                <div class="title">查看导入历史</div>
                <div class="desc">查看历史导入记录和资产导入结果详情</div>
              </div>
            </div>
            <div class="action-item" @click="exportData">
              <div class="icon-wrapper export">
                <i class="el-icon-document"></i>
              </div>
              <div class="content">
                <div class="title">导出资产数据</div>
                <div class="desc">导出当前系统中的资产数据，支持条件筛选</div>
              </div>
            </div>
          </el-card>
        </div>
        
        <!-- 导入流程步骤 -->
        <div v-if="currentStep > 0" class="import-process">
          <el-steps :active="currentStep" finish-status="success" align-center>
            <el-step title="上传文件"></el-step>
            <el-step title="数据预览"></el-step>
            <el-step title="导入结果"></el-step>
          </el-steps>
          
          <!-- 步骤1：上传文件 -->
          <div v-if="currentStep === 1">
            <div v-if="!uploadFile.file" class="upload-area" @click="triggerFileSelect">
              <i class="el-icon-upload upload-icon"></i>
              <div class="upload-text">点击或拖拽文件到此处上传</div>
              <div class="upload-tip">支持 .xlsx, .xls 格式文件，文件大小不超过10MB</div>
              <input
                ref="fileInput"
                type="file"
                accept=".xlsx,.xls"
                style="display: none"
                @change="handleFileUpload"
              />
            </div>
            
            <div v-else class="file-info">
              <i class="el-icon-document file-icon"></i>
              <div class="file-detail">
                <div class="file-name">{{ uploadFile.file.name }}</div>
                <div class="file-meta">
                  {{ formatFileSize(uploadFile.file.size) }} | {{ uploadFile.file.type }}
                </div>
              </div>
            </div>
            
            <div class="step-action">
              <el-button @click="resetImport">返回</el-button>
              <el-button
                type="primary"
                :disabled="!uploadFile.file"
                :loading="uploadFile.loading"
                @click="previewImportData"
              >
                下一步
              </el-button>
            </div>
          </div>
          
          <!-- 步骤2：数据预览 -->
          <div v-if="currentStep === 2">
            <el-alert
              v-if="hasPreviewErrors"
              title="数据存在错误，请修正后再导入"
              type="error"
              :closable="false"
              show-icon
              style="margin-bottom: 15px"
            />
            
            <el-table
              :data="previewData.data"
              border
              class="data-table"
              max-height="500"
              v-loading="previewData.loading"
            >
              <el-table-column type="index" label="序号" width="50" align="center" />
              
              <el-table-column prop="assetCode" label="资产编号" min-width="120">
                <template #default="scope">
                  <div
                    :class="{ 'error-cell': isFieldError(scope.row, 'assetCode') }"
                    v-el-tooltip="{
                      content: getErrorMessage(scope.row, 'assetCode'),
                      disabled: !isFieldError(scope.row, 'assetCode')
                    }"
                  >
                    {{ scope.row.assetCode }}
                  </div>
                </template>
              </el-table-column>
              
              <el-table-column prop="assetName" label="资产名称" min-width="150">
                <template #default="scope">
                  <div
                    :class="{ 'error-cell': isFieldError(scope.row, 'assetName') }"
                    v-el-tooltip="{
                      content: getErrorMessage(scope.row, 'assetName'),
                      disabled: !isFieldError(scope.row, 'assetName')
                    }"
                  >
                    {{ scope.row.assetName }}
                  </div>
                </template>
              </el-table-column>
              
              <el-table-column prop="assetType" label="资产类别" min-width="120">
                <template #default="scope">
                  <div
                    :class="{ 'error-cell': isFieldError(scope.row, 'assetType') }"
                    v-el-tooltip="{
                      content: getErrorMessage(scope.row, 'assetType'),
                      disabled: !isFieldError(scope.row, 'assetType')
                    }"
                  >
                    {{ scope.row.assetType }}
                  </div>
                </template>
              </el-table-column>
              
              <el-table-column prop="department" label="所属部门" min-width="120">
                <template #default="scope">
                  <div
                    :class="{ 'error-cell': isFieldError(scope.row, 'department') }"
                    v-el-tooltip="{
                      content: getErrorMessage(scope.row, 'department'),
                      disabled: !isFieldError(scope.row, 'department')
                    }"
                  >
                    {{ scope.row.department }}
                  </div>
                </template>
              </el-table-column>
              
              <el-table-column prop="purchaseDate" label="购置日期" min-width="120">
                <template #default="scope">
                  <div
                    :class="{ 'error-cell': isFieldError(scope.row, 'purchaseDate') }"
                    v-el-tooltip="{
                      content: getErrorMessage(scope.row, 'purchaseDate'),
                      disabled: !isFieldError(scope.row, 'purchaseDate')
                    }"
                  >
                    {{ scope.row.purchaseDate }}
                  </div>
                </template>
              </el-table-column>
              
              <el-table-column prop="amount" label="金额(元)" min-width="100">
                <template #default="scope">
                  <div
                    :class="{ 'error-cell': isFieldError(scope.row, 'amount') }"
                    v-el-tooltip="{
                      content: getErrorMessage(scope.row, 'amount'),
                      disabled: !isFieldError(scope.row, 'amount')
                    }"
                  >
                    {{ scope.row.amount }}
                  </div>
                </template>
              </el-table-column>
              
              <el-table-column prop="status" label="状态" min-width="80">
                <template #default="scope">
                  <div
                    :class="{ 'error-cell': isFieldError(scope.row, 'status') }"
                    v-el-tooltip="{
                      content: getErrorMessage(scope.row, 'status'),
                      disabled: !isFieldError(scope.row, 'status')
                    }"
                  >
                    {{ scope.row.status }}
                  </div>
                </template>
              </el-table-column>
            </el-table>
            
            <div class="step-action">
              <el-button @click="currentStep = 1">上一步</el-button>
              <el-button
                type="primary"
                :disabled="hasPreviewErrors"
                :loading="previewData.confirmLoading"
                @click="confirmImport"
              >
                确认导入
              </el-button>
            </div>
          </div>
          
          <!-- 步骤3：导入结果 -->
          <div v-if="currentStep === 3" class="import-result">
            <i 
              class="el-icon-success result-icon" 
              :class="importResult.success ? 'success' : 'warning'"
            ></i>
            <div class="result-title">
              {{ importResult.success ? '导入成功' : '部分导入成功' }}
            </div>
            <div class="result-desc">
              {{ importResult.success
                ? '所有资产数据已成功导入系统'
                : '部分资产数据导入失败，请查看详情' }}
            </div>
            
            <div class="detail-panel">
              <div class="panel-header">导入结果详情</div>
              <div class="panel-body">
                <div class="result-item">
                  <span class="label">导入总数：</span>
                  <span class="value">{{ importResult.total }}</span>
                </div>
                <div class="result-item">
                  <span class="label">成功数量：</span>
                  <span class="value success">{{ importResult.successCount }}</span>
                </div>
                <div class="result-item">
                  <span class="label">失败数量：</span>
                  <span class="value error">{{ importResult.errorCount }}</span>
                </div>
                <div class="result-item">
                  <span class="label">导入时间：</span>
                  <span class="value">{{ importResult.importTime }}</span>
                </div>
              </div>
            </div>
            
            <div class="step-action">
              <el-button type="primary" @click="resetImport">完成</el-button>
              <el-button v-if="!importResult.success" type="warning" @click="downloadErrorData">
                下载错误数据
              </el-button>
            </div>
          </div>
        </div>
      </el-tab-pane>
      
      <!-- 导入历史记录 -->
      <el-tab-pane label="导入历史" name="history">
        <el-table
          :data="importHistory.list"
          border
          class="history-list"
          v-loading="importHistory.loading"
          :row-class-name="getHistoryRowClass"
        >
          <el-table-column type="index" label="序号" width="50" align="center" />
          <el-table-column prop="fileName" label="文件名称" min-width="180" />
          <el-table-column prop="importTime" label="导入时间" width="160" sortable />
          <el-table-column prop="operator" label="操作人" width="120" />
          <el-table-column prop="total" label="总数量" width="80" align="center" />
          <el-table-column prop="successCount" label="成功" width="80" align="center">
            <template #default="scope">
              <span class="success">{{ scope.row.successCount }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="errorCount" label="失败" width="80" align="center">
            <template #default="scope">
              <span :class="scope.row.errorCount > 0 ? 'error' : ''">{{ scope.row.errorCount }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag
                :type="scope.row.status === '成功' ? 'success' : scope.row.status === '部分成功' ? 'warning' : 'danger'"
                class="status-tag"
              >
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="scope">
              <el-button
                type="text"
                class="history-action-button"
                @click="viewImportDetail(scope.row)"
              >
                详情
              </el-button>
              <el-button
                v-if="scope.row.errorCount > 0"
                type="text"
                class="history-action-button"
                @click="downloadHistoryErrorData(scope.row)"
              >
                下载错误
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <el-pagination
          v-if="importHistory.list.length > 0"
          layout="total, prev, pager, next, sizes"
          :total="importHistory.total"
          :page-size="importHistory.pageSize"
          :current-page="importHistory.currentPage"
          @size-change="val => handleHistoryPageSizeChange(val)"
          @current-change="val => handleHistoryPageChange(val)"
          style="margin-top: 20px; text-align: right"
        />
      </el-tab-pane>
    </el-tabs>
    
    <!-- 导出数据对话框 -->
    <el-dialog
      title="导出资产数据"
      v-model="exportDialog.visible"
      width="600px"
      destroy-on-close
    >
      <el-form
        ref="exportForm"
        :model="exportDialog.form"
        label-width="100px"
        class="export-form"
      >
        <el-form-item label="资产类型">
          <el-select
            v-model="exportDialog.form.assetType"
            placeholder="请选择资产类型"
            clearable
            style="width: 100%"
          >
            <el-option label="生产设备" value="production" />
            <el-option label="办公设备" value="office" />
            <el-option label="车辆" value="vehicle" />
            <el-option label="IT设备" value="it" />
          </el-select>
        </el-form-item>
        <el-form-item label="所属部门">
          <el-select
            v-model="exportDialog.form.department"
            placeholder="请选择部门"
            clearable
            style="width: 100%"
          >
            <el-option label="生产部" value="production" />
            <el-option label="行政部" value="admin" />
            <el-option label="财务部" value="finance" />
            <el-option label="IT部" value="it" />
          </el-select>
        </el-form-item>
        <el-form-item label="购置日期">
          <el-date-picker
            v-model="exportDialog.form.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="资产状态">
          <el-select
            v-model="exportDialog.form.status"
            placeholder="请选择资产状态"
            clearable
            style="width: 100%"
          >
            <el-option label="在用" value="in_use" />
            <el-option label="闲置" value="idle" />
            <el-option label="维修" value="repairing" />
            <el-option label="报废" value="scrapped" />
          </el-select>
        </el-form-item>
        
        <div class="form-footer">
          <el-button @click="exportDialog.visible = false">取消</el-button>
          <el-button
            type="primary"
            :loading="exportDialog.loading"
            @click="handleExportData"
          >
            确认导出
          </el-button>
        </div>
      </el-form>
    </el-dialog>
    
    <!-- 导入详情抽屉 -->
    <el-drawer
      v-model="importDetail.visible"
      title="导入详情"
      direction="rtl"
      size="50%"
      destroy-on-close
      class="detail-drawer"
    >
      <div v-loading="importDetail.loading">
        <div class="drawer-header">
          <div class="drawer-title">{{ importDetail.data.fileName }}</div>
        </div>
        
        <div class="info-section">
          <div class="section-title">基本信息</div>
          <div class="info-item">
            <span class="label">导入时间：</span>
            <span class="value">{{ importDetail.data.importTime }}</span>
          </div>
          <div class="info-item">
            <span class="label">导入人：</span>
            <span class="value">{{ importDetail.data.operator }}</span>
          </div>
          <div class="info-item">
            <span class="label">导入状态：</span>
            <span class="value">
              <el-tag
                :type="importDetail.data.status === '成功' ? 'success' : importDetail.data.status === '部分成功' ? 'warning' : 'danger'"
                size="small"
              >
                {{ importDetail.data.status }}
              </el-tag>
            </span>
          </div>
        </div>
        
        <div class="info-section">
          <div class="section-title">导入结果</div>
          <div class="info-item">
            <span class="label">总数量：</span>
            <span class="value">{{ importDetail.data.total }}</span>
          </div>
          <div class="info-item">
            <span class="label">成功数量：</span>
            <span class="value success">{{ importDetail.data.successCount }}</span>
          </div>
          <div class="info-item">
            <span class="label">失败数量：</span>
            <span class="value error">{{ importDetail.data.errorCount }}</span>
          </div>
        </div>
        
        <!-- 错误详情列表 -->
        <div v-if="importDetail.data.errors && importDetail.data.errors.length > 0">
          <div class="section-title">错误详情</div>
          <div class="error-list">
            <div
              v-for="(error, index) in importDetail.data.errors"
              :key="index"
              class="error-item"
            >
              <div class="error-row">第 {{ error.row }} 行</div>
              <div class="error-field">{{ error.field || '资产字段' }}</div>
              <div class="error-message">{{ error.message }}</div>
            </div>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { UploadFile } from 'element-plus'
import { importAsset, exportAsset, getImportHistory, getImportDetail } from '@/api/asset/import'

// 标签页控制
const activeTab = ref('import')

// 导入步骤控制
const currentStep = ref(0)

// 文件上传相关
const fileInput = ref<HTMLInputElement | null>(null)
const uploadFile = reactive({
  file: null as File | null,
  loading: false
})

// 预览数据相关
const previewData = reactive({
  data: [] as any[],
  errors: [] as any[],
  loading: false,
  confirmLoading: false
})

// 导入结果相关
const importResult = reactive({
  success: true,
  total: 0,
  successCount: 0,
  errorCount: 0,
  importTime: '',
  importId: '',
  errorData: [] as any[]
})

// 导入历史相关
const importHistory = reactive({
  list: [] as any[],
  total: 0,
  pageSize: 10,
  currentPage: 1,
  loading: false
})

// 导入详情相关
const importDetail = reactive({
  visible: false,
  loading: false,
  data: {
    id: '',
    fileName: '',
    importTime: '',
    operator: '',
    status: '',
    total: 0,
    successCount: 0,
    errorCount: 0,
    errors: [] as any[]
  }
})

// 导出对话框相关
const exportDialog = reactive({
  visible: false,
  loading: false,
  form: {
    assetType: '',
    department: '',
    dateRange: [] as [Date, Date] | [],
    status: ''
  }
})

// 计算属性：是否存在预览错误
const hasPreviewErrors = computed(() => {
  return previewData.errors.length > 0
})

// 初始化页面
onMounted(() => {
  if (activeTab.value === 'history') {
    loadImportHistory()
  }
})

// 点击上传区域触发文件选择
const triggerFileSelect = () => {
  fileInput.value?.click()
}

// 处理文件上传
const handleFileUpload = (event: Event) => {
  const target = event.target as HTMLInputElement
  const files = target.files
  
  if (files && files.length > 0) {
    const file = files[0]
    
    // 检查文件类型
    if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
      ElMessage.error('请上传Excel格式的文件(.xlsx或.xls)')
      return
    }
    
    // 检查文件大小（10MB限制）
    if (file.size > 10 * 1024 * 1024) {
      ElMessage.error('文件大小不能超过10MB')
      return
    }
    
    uploadFile.file = file
  }
  
  // 重置文件输入框，以便可以重新选择同一文件
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

// 下载导入模板
const downloadTemplate = async (type: string) => {
  try {
    const res = await exportAsset(type)
    
    // 创建下载链接
    const blob = new Blob([res.data])
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = `资产导入模板_${new Date().getTime()}.xlsx`
    link.click()
    URL.revokeObjectURL(link.href)
    
    ElMessage.success('模板下载成功')
  } catch (error: any) {
    ElMessage.error(error.message || '模板下载失败')
  }
}

// 查看导入历史
const viewHistory = () => {
  activeTab.value = 'history'
  loadImportHistory()
}

// 导出数据
const exportData = () => {
  exportDialog.visible = true
}

// 处理导出数据
const handleExportData = async () => {
  exportDialog.loading = true
  
  try {
    // 构建导出参数
    const params: any = {}
    
    if (exportDialog.form.assetType) {
      params.assetType = exportDialog.form.assetType
    }
    
    if (exportDialog.form.department) {
      params.department = exportDialog.form.department
    }
    
    if (exportDialog.form.dateRange && exportDialog.form.dateRange.length === 2) {
      params.startDate = exportDialog.form.dateRange[0]
      params.endDate = exportDialog.form.dateRange[1]
    }
    
    if (exportDialog.form.status) {
      params.status = exportDialog.form.status
    }
    
    // 调用导出API
    const response = await exportAsset(params)
    
    // 处理文件下载
    if (response && response.data) {
      const blob = new Blob([response.data], { type: 'application/vnd.ms-excel' })
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      
      link.href = url
      link.download = `资产数据导出_${new Date().getTime()}.xlsx`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
      
      ElMessage.success('导出成功')
      exportDialog.visible = false
    }
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  } finally {
    exportDialog.loading = false
  }
}

// 预览导入数据
const previewImportData = async () => {
  if (!uploadFile.file) {
    return
  }
  
  uploadFile.loading = true
  previewData.loading = true
  
  try {
    // 创建FormData对象
    const formData = new FormData()
    formData.append('file', uploadFile.file)
    
    // 调用接口预览数据
    const response = await importAsset(formData, true) // 第二个参数表示预览模式
    
    // 处理响应
    if (response && response.data) {
      previewData.data = response.data.list || []
      previewData.errors = response.data.errors || []
      
      // 更新到下一步
      currentStep.value = 2
    } else {
      ElMessage.error('预览数据失败，请重试')
    }
  } catch (error) {
    console.error('预览失败:', error)
    ElMessage.error('预览失败，请检查文件格式或网络连接')
  } finally {
    uploadFile.loading = false
    previewData.loading = false
  }
}

// 确认导入
const confirmImport = async () => {
  if (hasPreviewErrors.value) {
    ElMessage.warning('数据存在错误，请修正后再导入')
    return
  }
  
  previewData.confirmLoading = true
  
  try {
    // 创建FormData对象
    const formData = new FormData()
    formData.append('file', uploadFile.file as File)
    
    // 调用接口确认导入
    const response = await importAsset(formData, false) // 第二个参数为false表示正式导入
    
    // 处理响应
    if (response && response.data) {
      importResult.success = response.data.errorCount === 0
      importResult.total = response.data.total || 0
      importResult.successCount = response.data.successCount || 0
      importResult.errorCount = response.data.errorCount || 0
      importResult.importTime = response.data.importTime || new Date().toLocaleString()
      importResult.importId = response.data.importId || ''
      importResult.errorData = response.data.errorData || []
      
      // 更新到下一步
      currentStep.value = 3
      
      // 如果在历史标签页，刷新历史记录
      if (activeTab.value === 'history') {
        loadImportHistory()
      }
    } else {
      ElMessage.error('导入失败，请重试')
    }
  } catch (error) {
    console.error('导入失败:', error)
    ElMessage.error('导入失败，请检查网络连接')
  } finally {
    previewData.confirmLoading = false
  }
}

// 下载错误数据
const downloadErrorData = () => {
  if (importResult.errorData.length === 0) {
    ElMessage.warning('没有错误数据可下载')
    return
  }
  
  // 实际项目中，这里可能需要调用后端API生成错误数据文件
  ElMessage.success('错误数据下载中，请稍候...')
  
  // 模拟下载过程
  setTimeout(() => {
    const a = document.createElement('a')
    a.href = `/api/asset/import/error-data?importId=${importResult.importId}`
    a.download = `导入错误数据_${new Date().getTime()}.xlsx`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
  }, 1000)
}

// 重置导入流程
const resetImport = () => {
  currentStep.value = 0
  uploadFile.file = null
  uploadFile.loading = false
  previewData.data = []
  previewData.errors = []
  previewData.loading = false
  previewData.confirmLoading = false
}

// 加载导入历史记录
const loadImportHistory = async () => {
  importHistory.loading = true
  
  try {
    const params = {
      page: importHistory.currentPage,
      pageSize: importHistory.pageSize
    }
    
    const response = await getImportHistory(params)
    
    if (response && response.data) {
      importHistory.list = response.data.list || []
      importHistory.total = response.data.total || 0
    }
  } catch (error) {
    console.error('加载历史记录失败:', error)
    ElMessage.error('加载历史记录失败，请重试')
  } finally {
    importHistory.loading = false
  }
}

// 处理历史页码变化
const handleHistoryPageChange = (page: number) => {
  importHistory.currentPage = page
  loadImportHistory()
}

// 处理历史页大小变化
const handleHistoryPageSizeChange = (size: number) => {
  importHistory.pageSize = size
  importHistory.currentPage = 1
  loadImportHistory()
}

// 获取历史记录行样式
const getHistoryRowClass = ({ row }: { row: any }) => {
  if (row.status === '失败') {
    return 'error-row'
  }
  return ''
}

// 查看导入详情
const viewImportDetail = async (row: any) => {
  importDetail.visible = true
  importDetail.loading = true
  
  try {
    const response = await getImportDetail(row.id)
    
    if (response && response.data) {
      importDetail.data = response.data
    }
  } catch (error) {
    console.error('加载导入详情失败:', error)
    ElMessage.error('加载导入详情失败，请重试')
  } finally {
    importDetail.loading = false
  }
}

// 下载历史错误数据
const downloadHistoryErrorData = (row: any) => {
  if (row.errorCount === 0) {
    ElMessage.warning('没有错误数据可下载')
    return
  }
  
  ElMessage.success('错误数据下载中，请稍候...')
  
  // 模拟下载过程
  setTimeout(() => {
    const a = document.createElement('a')
    a.href = `/api/asset/import/error-data?importId=${row.id}`
    a.download = `导入错误数据_${row.importTime.replace(/[/:]/g, '')}.xlsx`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
  }, 1000)
}

// 判断字段是否有错误
const isFieldError = (row: any, field: string): boolean => {
  if (!previewData.errors || previewData.errors.length === 0) {
    return false
  }
  
  const rowError = previewData.errors.find(
    (error) => error.row === row.__rowIndex && error.field === field
  )
  
  return !!rowError
}

// 获取错误消息
const getErrorMessage = (row: any, field: string): string => {
  if (!previewData.errors || previewData.errors.length === 0) {
    return ''
  }
  
  const rowError = previewData.errors.find(
    (error) => error.row === row.__rowIndex && error.field === field
  )
  
  return rowError ? rowError.message : ''
}

// 格式化文件大小
const formatFileSize = (size: number): string => {
  if (size < 1024) {
    return size + ' B'
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + ' KB'
  } else {
    return (size / (1024 * 1024)).toFixed(2) + ' MB'
  }
}
</script>

<style scoped lang="scss">
.asset-import-container {
  padding: 20px;
  
  .page-header {
    margin-bottom: 20px;
    
    h2 {
      margin: 0 0 10px;
      font-size: 20px;
      font-weight: 600;
      color: #303133;
    }
    
    p {
      margin: 0;
      font-size: 14px;
      color: #606266;
    }
  }
  
  .action-card {
    margin-bottom: 20px;
    
    .action-item {
      display: flex;
      align-items: center;
      padding: 15px;
      margin-bottom: 10px;
      border-radius: 4px;
      border: 1px solid #EBEEF5;
      transition: all 0.3s;
      cursor: pointer;
      
      &:hover {
        border-color: #409EFF;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      }
      
      .icon-wrapper {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        background-color: #ecf5ff;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        
        i {
          font-size: 24px;
          color: #409EFF;
        }
        
        &.download {
          background-color: #f0f9eb;
          i {
            color: #67C23A;
          }
        }
        
        &.history {
          background-color: #f5f7fa;
          i {
            color: #909399;
          }
        }
        
        &.export {
          background-color: #fdf6ec;
          i {
            color: #E6A23C;
          }
        }
      }
      
      .content {
        flex: 1;
        
        .title {
          font-size: 16px;
          font-weight: bold;
          margin-bottom: 5px;
        }
        
        .desc {
          font-size: 13px;
          color: #666;
        }
      }
    }
  }
  
  .import-process {
    padding: 20px;
    
    .step-action {
      display: flex;
      justify-content: center;
      margin-top: 30px;
      
      .el-button {
        margin: 0 10px;
      }
    }
    
    .file-info {
      display: flex;
      align-items: center;
      padding: 15px;
      border: 1px dashed #dcdfe6;
      border-radius: 4px;
      background-color: #f5f7fa;
      margin: 20px 0;
      
      .file-icon {
        font-size: 32px;
        color: #409EFF;
        margin-right: 15px;
      }
      
      .file-detail {
        .file-name {
          font-weight: bold;
          margin-bottom: 5px;
        }
        
        .file-meta {
          font-size: 13px;
          color: #666;
        }
      }
    }
    
    .upload-area {
      padding: 40px 20px;
      text-align: center;
      border: 1px dashed #dcdfe6;
      border-radius: 6px;
      background-color: #f5f7fa;
      cursor: pointer;
      transition: all 0.3s;
      
      &:hover {
        border-color: #409EFF;
      }
      
      .upload-icon {
        font-size: 48px;
        color: #909399;
        margin-bottom: 15px;
      }
      
      .upload-text {
        font-size: 16px;
        margin-bottom: 10px;
      }
      
      .upload-tip {
        font-size: 13px;
        color: #888;
      }
    }
    
    .import-result {
      text-align: center;
      padding: 30px 0;
      
      .result-icon {
        font-size: 64px;
        margin-bottom: 20px;
        
        &.success {
          color: #67C23A;
        }
        
        &.warning {
          color: #E6A23C;
        }
      }
      
      .result-title {
        font-size: 20px;
        font-weight: bold;
        margin-bottom: 10px;
      }
      
      .result-desc {
        font-size: 14px;
        color: #666;
        margin-bottom: 25px;
      }
      
      .detail-panel {
        margin: 0 auto;
        max-width: 500px;
        text-align: left;
        border: 1px solid #EBEEF5;
        border-radius: 4px;
        overflow: hidden;
        
        .panel-header {
          padding: 10px 15px;
          background-color: #f5f7fa;
          border-bottom: 1px solid #EBEEF5;
          font-weight: bold;
        }
        
        .panel-body {
          padding: 15px;
          
          .result-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            
            .label {
              color: #666;
            }
            
            .value {
              font-weight: bold;
              
              &.success {
                color: #67C23A;
              }
              
              &.error {
                color: #F56C6C;
              }
            }
          }
        }
      }
    }
  }
  
  .data-table {
    .error-cell {
      background-color: #ffedee;
      border: 1px solid #fac8ca;
      
      &:hover {
        background-color: #ffe2e3;
      }
    }
    
    .editable-cell {
      .cell-edit-input {
        width: 100%;
      }
    }
  }
  
  .history-list {
    .error-row {
      background-color: #ffedee;
    }
    
    .warning-row {
      background-color: #fdf6ec;
    }
  }
  
  .history-action-button {
    margin-right: 5px;
  }
  
  .error-tooltip {
    font-size: 12px;
    line-height: 1.4;
    
    .error-item {
      margin-bottom: 5px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  
  .export-form {
    .form-footer {
      text-align: right;
      margin-top: 20px;
    }
  }
  
  :deep(.el-step__title) {
    font-size: 14px;
  }
  
  :deep(.el-table .status-tag) {
    margin-right: 0;
  }
  
  // 详情抽屉样式
  .detail-drawer {
    .drawer-header {
      padding: 10px 0;
      margin-bottom: 15px;
      
      .drawer-title {
        font-size: 18px;
        font-weight: bold;
      }
    }
    
    .info-section {
      margin-bottom: 20px;
      
      .section-title {
        font-weight: bold;
        margin-bottom: 10px;
        padding-bottom: 6px;
        border-bottom: 1px solid #EBEEF5;
      }
      
      .info-item {
        display: flex;
        margin-bottom: 12px;
        
        .label {
          width: 80px;
          color: #666;
          flex-shrink: 0;
        }
        
        .value {
          font-weight: bold;
        }
      }
    }
    
    .error-list {
      margin-top: 20px;
      
      .error-item {
        background-color: #ffedee;
        padding: 10px 15px;
        border-radius: 4px;
        margin-bottom: 10px;
        font-size: 13px;
        
        .error-row {
          margin-bottom: 5px;
          font-weight: bold;
        }
        
        .error-field {
          margin-bottom: 5px;
          color: #666;
        }
        
        .error-message {
          color: #F56C6C;
        }
      }
    }
  }
}
</style> 