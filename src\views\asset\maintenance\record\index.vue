<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh, Download, View, Document } from '@element-plus/icons-vue'
import request from '@/config/axios'

// 类型定义
interface MaintenanceRecord {
  id: string
  orderNo: string
  assetId: string
  assetCode: string
  assetName: string
  faultType: string
  repairType: string
  faultDesc: string
  solution: string
  reportBy: string
  reportTime: string
  assignee: string
  startTime: string
  endTime: string
  duration: number
  status: string
  cost: number
  parts: RepairPart[]
}

interface RepairPart {
  id: string
  name: string
  code: string
  quantity: number
  price: number
  totalPrice: number
}

// 状态变量
const loading = ref(false)
const drawerVisible = ref(false)
const currentRecord = ref<MaintenanceRecord | null>(null)

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  status: '',
  assetName: '',
  dateRange: [] as string[],
  repairType: ''
})

// 表格数据
const tableData = ref<MaintenanceRecord[]>([])
const total = ref(0)

// 状态映射
const statusMap = {
  completed: { text: '已完成', type: 'success' },
  inProgress: { text: '维修中', type: 'warning' },
  cancelled: { text: '已取消', type: 'info' }
}

// 故障类型映射
const faultTypeMap = {
  hardware: '硬件故障',
  software: '软件故障',
  network: '网络故障',
  power: '电力故障',
  other: '其他故障'
}

// 维修类型选项
const repairTypeOptions = [
  { value: 'preventive', label: '预防性维修' },
  { value: 'corrective', label: '纠正性维修' },
  { value: 'emergency', label: '紧急维修' },
  { value: 'inspection', label: '常规检查' }
]

// 获取维修记录列表
const getList = async () => {
  loading.value = true
  try {
    // TODO: 调用API
    // 模拟API响应
    await new Promise(resolve => setTimeout(resolve, 300))
    
    // 生成模拟数据
    const mockData: MaintenanceRecord[] = Array.from({ length: 30 }, (_, index) => {
      const startTime = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)
      const endTime = new Date(startTime.getTime() + Math.random() * 3 * 24 * 60 * 60 * 1000)
      const duration = Math.floor((endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60))
      
      return {
        id: `${index + 1}`,
        orderNo: `WO-${String(1000 + index).padStart(6, '0')}`,
        assetId: `${2000 + index}`,
        assetCode: `ZC${String(2000 + index).padStart(6, '0')}`,
        assetName: `测试设备${index % 10 + 1}`,
        faultType: Object.keys(faultTypeMap)[index % 5],
        repairType: repairTypeOptions[index % 4].value,
        faultDesc: `设备出现${Object.values(faultTypeMap)[index % 5]}，需要维修处理`,
        solution: `更换了${index % 2 ? '部件' : '配件'}，调整了${index % 2 ? '参数' : '设置'}，问题已解决`,
        reportBy: ['张三', '李四', '王五'][index % 3],
        reportTime: new Date(startTime.getTime() - 24 * 60 * 60 * 1000).toLocaleString(),
        assignee: ['技术员A', '技术员B', '技术员C'][index % 3],
        startTime: startTime.toLocaleString(),
        endTime: index % 10 === 0 ? '' : endTime.toLocaleString(),
        duration: index % 10 === 0 ? 0 : duration,
        status: index % 10 === 0 ? 'inProgress' : (index % 15 === 0 ? 'cancelled' : 'completed'),
        cost: Math.floor(Math.random() * 5000),
        parts: Array.from({ length: Math.floor(Math.random() * 3) + 1 }, (_, pIndex) => ({
          id: `${index}-${pIndex}`,
          name: ['螺丝', '轴承', '电路板', '传感器', '开关'][pIndex % 5],
          code: `P${String(pIndex + 1).padStart(4, '0')}`,
          quantity: Math.floor(Math.random() * 5) + 1,
          price: Math.floor(Math.random() * 500) + 100,
          totalPrice: (Math.floor(Math.random() * 5) + 1) * (Math.floor(Math.random() * 500) + 100)
        }))
      }
    })

    // 根据查询条件过滤
    let filteredData = [...mockData]
    
    if (queryParams.assetName) {
      filteredData = filteredData.filter(item => 
        item.assetName.includes(queryParams.assetName) || 
        item.assetCode.includes(queryParams.assetName)
      )
    }
    
    if (queryParams.status) {
      filteredData = filteredData.filter(item => item.status === queryParams.status)
    }
    
    if (queryParams.repairType) {
      filteredData = filteredData.filter(item => item.repairType === queryParams.repairType)
    }
    
    if (queryParams.dateRange && queryParams.dateRange.length === 2) {
      const startDate = new Date(queryParams.dateRange[0]).getTime()
      const endDate = new Date(queryParams.dateRange[1]).getTime() + 24 * 60 * 60 * 1000 - 1
      
      filteredData = filteredData.filter(item => {
        const itemDate = new Date(item.startTime).getTime()
        return itemDate >= startDate && itemDate <= endDate
      })
    }

    // 分页处理
    const start = (queryParams.pageNum - 1) * queryParams.pageSize
    const end = Math.min(start + queryParams.pageSize, filteredData.length)
    tableData.value = filteredData.slice(start, end)
    total.value = filteredData.length
  } catch (error) {
    console.error('获取维修记录失败:', error)
    ElMessage.error('获取维修记录失败')
  } finally {
    loading.value = false
  }
}

// 查看详情
const handleView = (row: MaintenanceRecord) => {
  currentRecord.value = row
  drawerVisible.value = true
}

// 导出记录
const handleExport = () => {
  ElMessage.success('维修记录导出成功')
}

// 打印工单
const handlePrint = (row: MaintenanceRecord) => {
  ElMessage.success(`工单 ${row.orderNo} 打印成功`)
}

// 搜索
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置搜索
const handleReset = () => {
  queryParams.assetName = ''
  queryParams.status = ''
  queryParams.repairType = ''
  queryParams.dateRange = []
  handleQuery()
}

// 计算总维修成本
const getTotalCost = (record: MaintenanceRecord) => {
  const partsCost = record.parts.reduce((sum, part) => sum + part.totalPrice, 0)
  return partsCost + record.cost
}

// 页面加载时获取数据
onMounted(() => {
  getList()
})
</script>

<template>
  <div class="maintenance-record-container">
    <!-- 搜索表单 -->
    <el-card class="search-card">
      <el-form :model="queryParams" ref="queryFormRef" :inline="true">
        <el-form-item label="资产信息" prop="assetName">
          <el-input
            v-model="queryParams.assetName"
            placeholder="资产编号/名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="维修状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择" clearable style="width: 200px">
            <el-option label="已完成" value="completed" />
            <el-option label="维修中" value="inProgress" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
        <el-form-item label="维修类型" prop="repairType">
          <el-select v-model="queryParams.repairType" placeholder="请选择" clearable style="width: 200px">
            <el-option
              v-for="item in repairTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="维修日期" prop="dateRange">
          <el-date-picker
            v-model="queryParams.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>维修记录列表</span>
          <el-button type="primary" icon="Download" @click="handleExport">导出记录</el-button>
        </div>
      </template>

      <!-- 表格 -->
      <el-table :data="tableData" v-loading="loading" border>
        <el-table-column prop="orderNo" label="工单号" width="120" />
        <el-table-column prop="assetCode" label="资产编号" width="120" />
        <el-table-column prop="assetName" label="资产名称" width="120" />
        <el-table-column label="故障类型" width="120">
          <template #default="{ row }">
            {{ faultTypeMap[row.faultType] }}
          </template>
        </el-table-column>
        <el-table-column label="维修类型" width="120">
          <template #default="{ row }">
            {{ repairTypeOptions.find(item => item.value === row.repairType)?.label }}
          </template>
        </el-table-column>
        <el-table-column prop="assignee" label="维修人员" width="100" />
        <el-table-column prop="startTime" label="开始时间" width="150" />
        <el-table-column prop="endTime" label="完成时间" width="150" />
        <el-table-column label="用时(小时)" width="100" align="right">
          <template #default="{ row }">
            {{ row.duration || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="费用(元)" width="100" align="right">
          <template #default="{ row }">
            {{ getTotalCost(row).toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="statusMap[row.status].type">
              {{ statusMap[row.status].text }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link icon="View" @click="handleView(row)">详情</el-button>
            <el-button type="info" link icon="Document" @click="handlePrint(row)">打印</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页器 -->
      <el-pagination
        v-model:current-page="queryParams.pageNum"
        v-model:page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        class="pagination"
      />
    </el-card>

    <!-- 详情抽屉 -->
    <el-drawer
      v-model="drawerVisible"
      title="维修记录详情"
      size="60%"
      destroy-on-close
    >
      <template v-if="currentRecord">
        <div class="record-header">
          <h3>工单号：{{ currentRecord.orderNo }}</h3>
          <el-tag :type="statusMap[currentRecord.status].type">
            {{ statusMap[currentRecord.status].text }}
          </el-tag>
        </div>

        <el-divider content-position="left">资产信息</el-divider>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="资产编号">{{ currentRecord.assetCode }}</el-descriptions-item>
          <el-descriptions-item label="资产名称">{{ currentRecord.assetName }}</el-descriptions-item>
        </el-descriptions>

        <el-divider content-position="left">故障信息</el-divider>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="故障类型">{{ faultTypeMap[currentRecord.faultType] }}</el-descriptions-item>
          <el-descriptions-item label="维修类型">
            {{ repairTypeOptions.find(item => item.value === currentRecord.repairType)?.label }}
          </el-descriptions-item>
          <el-descriptions-item label="故障描述" :span="2">{{ currentRecord.faultDesc }}</el-descriptions-item>
          <el-descriptions-item label="报修人">{{ currentRecord.reportBy }}</el-descriptions-item>
          <el-descriptions-item label="报修时间">{{ currentRecord.reportTime }}</el-descriptions-item>
        </el-descriptions>

        <el-divider content-position="left">维修信息</el-divider>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="维修人员">{{ currentRecord.assignee }}</el-descriptions-item>
          <el-descriptions-item label="维修用时">
            {{ currentRecord.duration || '-' }} 小时
          </el-descriptions-item>
          <el-descriptions-item label="开始时间">{{ currentRecord.startTime }}</el-descriptions-item>
          <el-descriptions-item label="完成时间">{{ currentRecord.endTime || '-' }}</el-descriptions-item>
          <el-descriptions-item label="维修方案" :span="2">{{ currentRecord.solution }}</el-descriptions-item>
          <el-descriptions-item label="人工费用">¥{{ currentRecord.cost.toLocaleString() }}</el-descriptions-item>
          <el-descriptions-item label="配件费用">
            ¥{{ currentRecord.parts.reduce((sum, part) => sum + part.totalPrice, 0).toLocaleString() }}
          </el-descriptions-item>
          <el-descriptions-item label="总费用">
            ¥{{ getTotalCost(currentRecord).toLocaleString() }}
          </el-descriptions-item>
        </el-descriptions>

        <el-divider content-position="left">使用配件</el-divider>
        <el-table :data="currentRecord.parts" border>
          <el-table-column prop="code" label="配件编号" width="120" />
          <el-table-column prop="name" label="配件名称" min-width="150" />
          <el-table-column prop="quantity" label="数量" width="80" align="right" />
          <el-table-column prop="price" label="单价(元)" width="100" align="right">
            <template #default="{ row }">
              ¥{{ row.price.toLocaleString() }}
            </template>
          </el-table-column>
          <el-table-column prop="totalPrice" label="总价(元)" width="100" align="right">
            <template #default="{ row }">
              ¥{{ row.totalPrice.toLocaleString() }}
            </template>
          </el-table-column>
        </el-table>

        <div class="drawer-footer">
          <el-button type="primary" icon="Document" @click="handlePrint(currentRecord)">
            打印工单
          </el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<style lang="scss" scoped>
.maintenance-record-container {
  padding: 20px;
  
  .search-card {
    margin-bottom: 20px;
  }
  
  .table-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .pagination {
      margin-top: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
  
  .record-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h3 {
      margin: 0;
    }
  }
  
  .drawer-footer {
    margin-top: 30px;
    display: flex;
    justify-content: center;
  }
}
</style> 