// 资产生命周期管理相关类型定义

// 审批流程状态枚举
export enum ApprovalStatus {
  PENDING = 'pending', // 待审批
  APPROVED = 'approved', // 已批准
  REJECTED = 'rejected', // 已拒绝
  CANCELED = 'canceled', // 已取消
  PROCESSING = 'processing' // 处理中
}

// 基础流程信息接口
export interface BaseProcessInfo {
  id: string
  processNo: string // 流程编号
  title: string
  applyBy: string
  applyTime: string
  status: ApprovalStatus
  currentNode?: string
  currentHandler?: string
  remark?: string
  attachments?: AttachmentInfo[]
  createTime: string
  updateTime?: string
}

// 附件信息
export interface AttachmentInfo {
  id: string
  name: string
  size: number
  url: string
  uploadTime: string
  uploadBy: string
}

// 审批记录
export interface ApprovalRecord {
  id: string
  processId: string
  nodeId: string
  nodeName: string
  approvalBy: string
  approvalTime: string
  approvalType: 'approve' | 'reject' | 'transfer' | 'comment'
  approvalResult?: ApprovalStatus
  approvalOpinion?: string
}

// 审批节点
export interface ApprovalNode {
  id: string
  name: string
  type: 'start' | 'approve' | 'end'
  handlers?: string[]
  handlerNames?: string[]
}

// 审批详情
export interface ApprovalDetail {
  processInfo: BaseProcessInfo
  nodes: ApprovalNode[]
  records: ApprovalRecord[]
}

// 资产项
export interface AssetItem {
  id: string
  assetCode: string
  assetName: string
  assetType: {
    id: number
    name: string
  }
  statusId: number
  statusName: string
  unitId: number
  unitName: string
  departmentId?: number
  departmentName?: string
}

// ========== 资产验收 ==========
export interface AcceptanceForm {
  id?: string
  title: string
  assetList: AcceptanceAssetItem[]
  remark?: string
  attachments?: AttachmentInfo[]
}

export interface AcceptanceAssetItem extends AssetItem {
  model?: string
  specification?: string
  purchaseDate?: string
  purchasePrice?: number
  warrantyPeriod?: string
  manufacturer?: string
  supplier?: string
  location?: string
  responsiblePerson?: string
  serialNumber?: string
  acceptanceResult?: 'pass' | 'fail'
  acceptanceOpinion?: string
}

export interface AcceptanceProcess extends BaseProcessInfo {
  assetCount: number
  assetList: AcceptanceAssetItem[]
}

// ========== 资产调拨 ==========
export interface TransferForm {
  id?: string
  title: string
  sourceUnitId: number
  sourceUnitName: string
  sourceDepartmentId?: number
  sourceDepartmentName?: string
  targetUnitId: number
  targetUnitName: string
  targetDepartmentId?: number
  targetDepartmentName?: string
  transferReason: string
  planTransferDate: string
  assetList: AssetItem[]
  remark?: string
  attachments?: AttachmentInfo[]
}

export interface TransferProcess extends BaseProcessInfo {
  sourceUnitId: number
  sourceUnitName: string
  sourceDepartmentId?: number
  sourceDepartmentName?: string
  targetUnitId: number
  targetUnitName: string
  targetDepartmentId?: number
  targetDepartmentName?: string
  transferReason: string
  planTransferDate: string
  actualTransferDate?: string
  assetCount: number
  assetList: AssetItem[]
}

// ========== 资产闲置与回转 ==========
export interface IdleForm {
  id?: string
  title: string
  idleReason: string
  planIdleDate: string
  expectedReturnDate?: string
  assetList: AssetItem[]
  remark?: string
  attachments?: AttachmentInfo[]
}

export interface IdleProcess extends BaseProcessInfo {
  idleReason: string
  planIdleDate: string
  actualIdleDate?: string
  expectedReturnDate?: string
  assetCount: number
  assetList: AssetItem[]
}

export interface ReturnForm {
  id?: string
  title: string
  returnReason: string
  planReturnDate: string
  idleProcessId: string
  assetList: AssetItem[]
  remark?: string
  attachments?: AttachmentInfo[]
}

export interface ReturnProcess extends BaseProcessInfo {
  returnReason: string
  planReturnDate: string
  actualReturnDate?: string
  idleProcessId: string
  assetCount: number
  assetList: AssetItem[]
}

// ========== 资产报废与处置 ==========
export interface ScrapForm {
  id?: string
  title: string
  scrapReason: string
  planScrapDate: string
  assetList: AssetItem[]
  remark?: string
  attachments?: AttachmentInfo[]
}

export interface ScrapProcess extends BaseProcessInfo {
  scrapReason: string
  planScrapDate: string
  actualScrapDate?: string
  assetCount: number
  assetList: AssetItem[]
}

export interface DisposalForm {
  id?: string
  scrapProcessId: string
  disposalMethod: 'sell' | 'donate' | 'recycle' | 'destroy' | 'other'
  disposalDate: string
  disposalAmount?: number
  disposalTarget?: string
  disposalContact?: string
  assetList: AssetItem[]
  remark?: string
  attachments?: AttachmentInfo[]
}

export interface DisposalRecord {
  id: string
  scrapProcessId: string
  disposalMethod: 'sell' | 'donate' | 'recycle' | 'destroy' | 'other'
  disposalDate: string
  disposalAmount?: number
  disposalTarget?: string
  disposalContact?: string
  assetCount: number
  assetList: AssetItem[]
  createBy: string
  createTime: string
}

// ========== 资产变更 ==========
export interface ChangeForm {
  id?: string
  title: string
  changeType: 'responsible_person' | 'location' | 'department' | 'attribute' | 'other'
  changeReason: string
  assetId: string
  assetCode: string
  assetName: string
  changeItems: ChangeItem[]
  remark?: string
  attachments?: AttachmentInfo[]
}

export interface ChangeItem {
  fieldName: string
  fieldLabel: string
  oldValue: any
  newValue: any
}

export interface ChangeProcess extends BaseProcessInfo {
  changeType: 'responsible_person' | 'location' | 'department' | 'attribute' | 'other'
  changeReason: string
  assetId: string
  assetCode: string
  assetName: string
  changeItems: ChangeItem[]
}

// 变更日志
export interface ChangeLog {
  id: string
  assetId: string
  assetCode: string
  assetName: string
  changeType: 'responsible_person' | 'location' | 'department' | 'attribute' | 'other'
  fieldName: string
  fieldLabel: string
  oldValue: any
  newValue: any
  changeTime: string
  changeBy: string
  processId?: string
}

// 查询参数接口
export interface LifecycleQueryParams {
  pageNum: number
  pageSize: number
  keyword?: string
  status?: ApprovalStatus
  startTime?: string
  endTime?: string
  assetId?: string
  assetCode?: string
  applyBy?: string
  [key: string]: any
}

// ========== 资产变更类型枚举 ==========
export enum ChangeType {
  USER_CHANGE = 'responsible_person',     // 使用人变更
  LOCATION_CHANGE = 'location',           // 位置变更
  DEPARTMENT_CHANGE = 'department',       // 部门变更
  STATUS_CHANGE = 'status',               // 状态变更
  INFO_CHANGE = 'attribute'               // 信息变更
} 