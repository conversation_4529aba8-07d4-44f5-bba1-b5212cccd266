<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { ChangeForm as IChangeForm, AttachmentInfo, ChangeType } from '@/types/asset/lifecycle'
import { Asset } from '@/types/asset'
import { createChange } from '@/api/asset/lifecycle'
import AssetSelector from '@/components/AssetSelector/index.vue'
import UnitTreeSelect from '@/components/UnitTreeSelect/index.vue'
import FileUpload from '@/components/FileUpload/index.vue'

// 表单引用
const formRef = ref<FormInstance>()

// 表单数据 - 使用扩展类型来处理表单和API类型的兼容性
interface ExtendedChangeForm extends Omit<IChangeForm, 'changeType'> {
  type: string;
  beforeValue: string;
  afterValue: string;
  assetList: any[];
  planChangeDate: string;
  changeType?: string; // 添加可选的changeType
}

const formData = reactive<ExtendedChangeForm>({
  title: '',
  type: ChangeType.INFO_CHANGE,
  changeReason: '',
  planChangeDate: '',
  assetList: [],
  beforeValue: '',
  afterValue: '',
  remark: '',
  attachments: [],
  // 这些字段是IChangeForm需要的，但我们会在提交前动态填充
  assetId: '',
  assetCode: '',
  assetName: '',
  changeItems: []
})

// 表单验证规则
const formRules = reactive<FormRules>({
  title: [
    { required: true, message: '请输入变更标题', trigger: 'blur' },
    { min: 3, max: 100, message: '长度在 3 到 100 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择变更类型', trigger: 'change' }
  ],
  changeReason: [
    { required: true, message: '请输入变更原因', trigger: 'blur' },
    { min: 2, max: 200, message: '长度在 2 到 200 个字符', trigger: 'blur' }
  ],
  planChangeDate: [
    { required: true, message: '请选择计划变更日期', trigger: 'change' }
  ],
  assetList: [
    { required: true, message: '请选择需要变更的资产', trigger: 'change' },
    { type: 'array', min: 1, message: '至少选择一项资产', trigger: 'change' }
  ],
  afterValue: [
    { required: true, message: '请输入变更后的值', trigger: 'blur' }
  ]
})

// 变更类型选项
const changeTypeOptions = [
  {
    label: '使用人变更',
    value: ChangeType.USER_CHANGE
  },
  {
    label: '位置变更',
    value: ChangeType.LOCATION_CHANGE
  },
  {
    label: '部门变更',
    value: ChangeType.DEPARTMENT_CHANGE
  },
  {
    label: '状态变更',
    value: ChangeType.STATUS_CHANGE
  },
  {
    label: '信息变更',
    value: ChangeType.INFO_CHANGE
  }
]

// 状态选项（用于状态变更）
const statusOptions = [
  { label: '在用', value: 1 },
  { label: '闲置', value: 2 },
  { label: '维修中', value: 3 },
  { label: '已报废', value: 4 }
]

// 计算当前变更类型对应的标签
const changeTypeFieldLabel = computed(() => {
  switch (formData.type) {
    case ChangeType.USER_CHANGE:
      return '使用人'
    case ChangeType.LOCATION_CHANGE:
      return '位置'
    case ChangeType.DEPARTMENT_CHANGE:
      return '部门'
    case ChangeType.STATUS_CHANGE:
      return '状态'
    case ChangeType.INFO_CHANGE:
      return '信息项'
    default:
      return '变更项'
  }
})

// 监听变更类型变化
watch(() => formData.type, (newType) => {
  // 清空值
  formData.beforeValue = ''
  formData.afterValue = ''
  
  // 更新标题，如果标题为空或包含旧变更类型
  if (!formData.title || formData.title.includes('变更')) {
    const typeText = changeTypeOptions.find(option => option.value === newType)?.label || '信息变更'
    formData.title = `资产${typeText}申请`
  }
})

// 资产选择器引用
const assetSelectorRef = ref()

// 移除资产
const handleRemoveAsset = (index: number) => {
  formData.assetList.splice(index, 1)
}

// 重置表单
const resetForm = () => {
  if (!formRef.value) return
  formRef.value.resetFields()
  formData.assetList = []
  formData.type = ChangeType.INFO_CHANGE
  formData.beforeValue = ''
  formData.afterValue = ''
  formData.remark = ''
  formData.attachments = []
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid, fields) => {
    if (valid) {
      try {
        // 获取字段标签
        const fieldLabel = changeTypeFieldLabel.value

        // 构建符合API的数据结构
        const apiData: IChangeForm = {
          title: formData.title,
          changeType: formData.type as any,
          changeReason: formData.changeReason,
          assetId: formData.assetList[0]?.id || '',
          assetCode: formData.assetList[0]?.assetCode || '',
          assetName: formData.assetList[0]?.assetName || '',
          changeItems: [
            {
              fieldName: formData.type,
              fieldLabel: fieldLabel,
              oldValue: formData.beforeValue,
              newValue: formData.afterValue
            }
          ],
          remark: formData.remark,
          attachments: formData.attachments
        }

        // 模拟API调用
        setTimeout(() => {
          // 实际调用接口时使用下面的代码
          // await createChange(apiData)
          ElMessage.success('变更申请提交成功')
          emit('submit')
        }, 500)
      } catch (error) {
        console.error('提交变更申请失败', error)
        ElMessage.error('提交变更申请失败')
      }
    } else {
      console.log('验证失败', fields)
    }
  })
}

// 事件发射
const emit = defineEmits(['submit'])

// 提供重置方法给父组件
defineExpose({
  resetForm
})
</script>

<template>
  <div class="change-form">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      class="form-container"
    >
      <el-form-item label="变更标题" prop="title">
        <el-input v-model="formData.title" placeholder="请输入变更标题" />
      </el-form-item>

      <el-form-item label="变更类型" prop="type">
        <el-select v-model="formData.type" placeholder="请选择变更类型" style="width: 100%">
          <el-option
            v-for="item in changeTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="变更原因" prop="changeReason">
        <el-input
          v-model="formData.changeReason"
          type="textarea"
          :rows="3"
          placeholder="请输入变更原因"
        />
      </el-form-item>

      <el-form-item label="计划变更日期" prop="planChangeDate">
        <el-date-picker
          v-model="formData.planChangeDate"
          type="date"
          placeholder="请选择计划变更日期"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item :label="`当前${changeTypeFieldLabel}`" prop="beforeValue">
        <template v-if="formData.type === ChangeType.USER_CHANGE">
          <el-input v-model="formData.beforeValue" placeholder="请输入当前使用人" />
        </template>
        <template v-else-if="formData.type === ChangeType.LOCATION_CHANGE">
          <el-input v-model="formData.beforeValue" placeholder="请输入当前位置" />
        </template>
        <template v-else-if="formData.type === ChangeType.DEPARTMENT_CHANGE">
          <el-input v-model="formData.beforeValue" placeholder="请输入当前部门" disabled />
        </template>
        <template v-else-if="formData.type === ChangeType.STATUS_CHANGE">
          <el-select v-model="formData.beforeValue" placeholder="请选择当前状态" disabled style="width: 100%">
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </template>
        <template v-else>
          <el-input v-model="formData.beforeValue" placeholder="请输入当前值" />
        </template>
      </el-form-item>

      <el-form-item :label="`变更后${changeTypeFieldLabel}`" prop="afterValue">
        <template v-if="formData.type === ChangeType.USER_CHANGE">
          <el-input v-model="formData.afterValue" placeholder="请输入变更后使用人" />
        </template>
        <template v-else-if="formData.type === ChangeType.LOCATION_CHANGE">
          <el-input v-model="formData.afterValue" placeholder="请输入变更后位置" />
        </template>
        <template v-else-if="formData.type === ChangeType.DEPARTMENT_CHANGE">
          <UnitTreeSelect v-model="formData.afterValue" placeholder="请选择变更后部门" show-department />
        </template>
        <template v-else-if="formData.type === ChangeType.STATUS_CHANGE">
          <el-select v-model="formData.afterValue" placeholder="请选择变更后状态" style="width: 100%">
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </template>
        <template v-else>
          <el-input v-model="formData.afterValue" placeholder="请输入变更后值" />
        </template>
      </el-form-item>

      <el-form-item label="变更资产" prop="assetList">
        <div class="asset-selector-wrapper">
          <!-- 假设状态 1 表示"在用" -->
          <AssetSelector
            ref="assetSelectorRef"
            v-model="formData.assetList"
            :filter-by-status="[1]" 
            title="选择变更资产"
          />
        </div>
      </el-form-item>

      <!-- 已选资产列表 -->
      <el-form-item v-if="formData.assetList.length > 0" label="变更清单">
        <el-table :data="formData.assetList" border style="width: 100%" max-height="400">
          <el-table-column type="index" width="50" />
          <el-table-column prop="assetCode" label="资产编号" width="120" />
          <el-table-column prop="assetName" label="资产名称" min-width="150" />
          <el-table-column prop="assetType.name" label="资产类型" width="120" />
          <el-table-column prop="model" label="型号" width="120" />
          <el-table-column prop="specification" label="规格" width="120" />
          <el-table-column prop="unitName" label="所属单位" width="120" />
          <el-table-column prop="departmentName" label="所属部门" width="120" />
          <el-table-column prop="statusName" label="状态" width="100">
            <template #default="{ row }">
              <el-tag
                :type="
                  row.statusId === 1 
                    ? 'success' 
                    : row.statusId === 2 
                    ? 'warning' 
                    : row.statusId === 3 
                    ? 'info' 
                    : row.statusId === 4 
                    ? 'danger' 
                    : 'primary'
                "
              >
                {{ row.statusName }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="90" fixed="right">
            <template #default="{ $index }">
              <el-button link type="danger" @click="handleRemoveAsset($index)">移除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>

      <el-form-item label="备注说明">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注说明（选填）"
        />
      </el-form-item>

      <el-form-item label="附件">
        <FileUpload v-model="formData.attachments" />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<style lang="scss" scoped>
.change-form {
  padding: 20px;

  .form-container {
    max-width: 1200px;
  }

  .asset-selector-wrapper {
    display: flex;
    align-items: center;
    gap: 10px;
  }
}
</style> 