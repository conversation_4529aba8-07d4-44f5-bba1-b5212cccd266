<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getMaintenanceDetail } from '@/api/asset/maintenance'
import { MaintenanceStatus, MaintenancePriority, MaintenanceType } from '@/types/asset/maintenance'

// 路由
const router = useRouter()
const route = useRoute()

// 页面状态
const loading = ref(false)
const activeTab = ref('basic')
const maintenanceId = route.params.id as string

// 维修详情
const maintenanceDetail = reactive({
  id: '',
  requestNo: '',
  assetName: '',
  assetCode: '',
  assetType: '',
  maintenanceType: '',
  department: '',
  location: '',
  faultDescription: '',
  priority: '',
  requestUser: '',
  requestTime: '',
  status: '',
  reviewUser: '',
  reviewTime: '',
  reviewComments: '',
  assignedTo: '',
  assignedTime: '',
  plannedStartTime: '',
  plannedEndTime: '',
  actualStartTime: '',
  actualEndTime: '',
  maintenanceResult: '',
  acceptanceUser: '',
  acceptanceTime: '',
  acceptanceComments: '',
  acceptanceResult: false,
  totalCost: 0,
  // 维修记录
  maintenanceRecords: [] as Array<{
    id: string;
    operateUser: string;
    operateTime: string;
    content: string;
    attachments: string[];
  }>,
  // 费用明细
  maintenanceCosts: [] as Array<{
    id: string;
    costType: string;
    costName: string;
    amount: number;
    remarks: string;
  }>,
  // 配件使用
  maintenanceParts: [] as Array<{
    id: string;
    partName: string;
    partCode: string;
    specification: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
    supplier: string;
    remarks: string;
  }>,
  // 状态变更日志
  statusLogs: [] as Array<{
    id: string;
    oldStatus: string;
    newStatus: string;
    operateUser: string;
    operateTime: string;
    comments: string;
  }>
})

// 附件列表
const attachments = ref([
  { name: '故障图片1.jpg', url: '#' },
  { name: '维修报告.pdf', url: '#' }
])

// 获取维修详情
const fetchMaintenanceDetail = async () => {
  if (!maintenanceId) return
  
  loading.value = true
  try {
    // 模拟数据
    setTimeout(() => {
      // 基本信息
      maintenanceDetail.id = maintenanceId
      maintenanceDetail.requestNo = 'WX20230001'
      maintenanceDetail.assetName = '水泵电机'
      maintenanceDetail.assetCode = 'ZC202301001'
      maintenanceDetail.assetType = '生产设备'
      maintenanceDetail.maintenanceType = '故障维修'
      maintenanceDetail.department = '生产部'
      maintenanceDetail.location = '一号水厂'
      maintenanceDetail.faultDescription = '设备运行时有异常噪音，且振动较大'
      maintenanceDetail.priority = '高'
      maintenanceDetail.requestUser = '张三'
      maintenanceDetail.requestTime = '2023-06-15 10:30:00'
      maintenanceDetail.status = '维修完成'
      maintenanceDetail.reviewUser = '李四'
      maintenanceDetail.reviewTime = '2023-06-15 14:20:00'
      maintenanceDetail.reviewComments = '属实，需要及时处理'
      maintenanceDetail.assignedTo = '王维修'
      maintenanceDetail.assignedTime = '2023-06-15 15:00:00'
      maintenanceDetail.plannedStartTime = '2023-06-16 09:00:00'
      maintenanceDetail.plannedEndTime = '2023-06-16 17:00:00'
      maintenanceDetail.actualStartTime = '2023-06-16 09:30:00'
      maintenanceDetail.actualEndTime = '2023-06-16 16:45:00'
      maintenanceDetail.maintenanceResult = '更换轴承，清理机械内部，调整平衡，已恢复正常'
      maintenanceDetail.totalCost = 3560
      
      // 维修记录
      maintenanceDetail.maintenanceRecords = [
        {
          id: 'record1',
          operateUser: '王维修',
          operateTime: '2023-06-16 09:30:00',
          content: '初步检查发现轴承磨损严重，需要更换',
          attachments: ['检查照片.jpg']
        },
        {
          id: 'record2',
          operateUser: '王维修',
          operateTime: '2023-06-16 11:20:00',
          content: '拆卸完成，清理机械内部',
          attachments: []
        },
        {
          id: 'record3',
          operateUser: '王维修',
          operateTime: '2023-06-16 14:30:00',
          content: '安装新轴承，调整平衡',
          attachments: ['更换照片.jpg']
        },
        {
          id: 'record4',
          operateUser: '王维修',
          operateTime: '2023-06-16 16:45:00',
          content: '测试运行正常，维修完成',
          attachments: ['测试记录.pdf']
        }
      ]
      
      // 费用明细
      maintenanceDetail.maintenanceCosts = [
        {
          id: 'cost1',
          costType: '材料费',
          costName: '轴承',
          amount: 1800,
          remarks: '型号: SKF-6305'
        },
        {
          id: 'cost2',
          costType: '材料费',
          costName: '润滑油',
          amount: 320,
          remarks: ''
        },
        {
          id: 'cost3',
          costType: '人工费',
          costName: '维修工时',
          amount: 1200,
          remarks: '8小时 x 150元/小时'
        },
        {
          id: 'cost4',
          costType: '其他费用',
          costName: '交通费',
          amount: 240,
          remarks: ''
        }
      ]
      
      // 配件使用
      maintenanceDetail.maintenanceParts = [
        {
          id: 'part1',
          partName: '轴承',
          partCode: 'SKF-6305',
          specification: '规格型号: 25*62*17mm',
          quantity: 2,
          unitPrice: 900,
          totalPrice: 1800,
          supplier: 'SKF供应商',
          remarks: ''
        },
        {
          id: 'part2',
          partName: '密封圈',
          partCode: 'SL-220',
          specification: '规格型号: 内径22mm',
          quantity: 4,
          unitPrice: 35,
          totalPrice: 140,
          supplier: '本地供应商',
          remarks: ''
        }
      ]
      
      // 状态变更日志
      maintenanceDetail.statusLogs = [
        {
          id: 'log1',
          oldStatus: '',
          newStatus: '维修申请',
          operateUser: '张三',
          operateTime: '2023-06-15 10:30:00',
          comments: '创建维修单'
        },
        {
          id: 'log2',
          oldStatus: '维修申请',
          newStatus: '已批准',
          operateUser: '李四',
          operateTime: '2023-06-15 14:20:00',
          comments: '审批通过'
        },
        {
          id: 'log3',
          oldStatus: '已批准',
          newStatus: '已派工',
          operateUser: '李四',
          operateTime: '2023-06-15 15:00:00',
          comments: '指派给王维修'
        },
        {
          id: 'log4',
          oldStatus: '已派工',
          newStatus: '维修中',
          operateUser: '王维修',
          operateTime: '2023-06-16 09:30:00',
          comments: '开始维修'
        },
        {
          id: 'log5',
          oldStatus: '维修中',
          newStatus: '维修完成',
          operateUser: '王维修',
          operateTime: '2023-06-16 16:45:00',
          comments: '维修完成，等待验收'
        }
      ]
      
      loading.value = false
    }, 500)
    
    // 实际API调用
    // const { data } = await getMaintenanceDetail(maintenanceId)
    // Object.assign(maintenanceDetail, data)
  } catch (error) {
    console.error('获取维修详情失败', error)
    ElMessage.error('获取维修详情失败')
  } finally {
    loading.value = false
  }
}

// 返回列表
const goBack = () => {
  router.back()
}

// 验收维修
const acceptMaintenance = () => {
  ElMessageBox.confirm('确认验收通过该维修工单?', '验收确认', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'success'
  }).then(() => {
    ElMessage.success('验收成功')
    maintenanceDetail.status = '已验收'
    maintenanceDetail.acceptanceUser = '当前用户'
    maintenanceDetail.acceptanceTime = new Date().toLocaleString()
    maintenanceDetail.acceptanceResult = true
    
    // 添加状态变更日志
    maintenanceDetail.statusLogs.push({
      id: `log${maintenanceDetail.statusLogs.length + 1}`,
      oldStatus: '维修完成',
      newStatus: '已验收',
      operateUser: '当前用户',
      operateTime: new Date().toLocaleString(),
      comments: '验收通过'
    })
  }).catch(() => {})
}

// 驳回维修
const rejectMaintenance = () => {
  ElMessageBox.confirm('确认驳回该维修工单?', '驳回确认', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.warning('已驳回')
    maintenanceDetail.status = '维修中'
    
    // 添加状态变更日志
    maintenanceDetail.statusLogs.push({
      id: `log${maintenanceDetail.statusLogs.length + 1}`,
      oldStatus: '维修完成',
      newStatus: '维修中',
      operateUser: '当前用户',
      operateTime: new Date().toLocaleString(),
      comments: '验收不通过，需重新维修'
    })
  }).catch(() => {})
}

// 打印工单
const printOrder = () => {
  const printWindow = window.open('', '_blank')
  if (!printWindow) {
    ElMessage.error('打印窗口被浏览器阻止，请允许弹出窗口')
    return
  }
  
  // 构建打印内容
  const printContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>维修工单 - ${maintenanceDetail.requestNo}</title>
      <meta charset="utf-8" />
      <style>
        body {
          font-family: 'Microsoft YaHei', Arial, sans-serif;
          font-size: 14px;
          color: #333;
          line-height: 1.5;
          margin: 0;
          padding: 20px;
        }
        .print-container {
          max-width: 800px;
          margin: 0 auto;
        }
        .print-header {
          text-align: center;
          border-bottom: 2px solid #333;
          padding-bottom: 10px;
          margin-bottom: 20px;
        }
        .print-header h1 {
          font-size: 24px;
          margin: 0;
        }
        .print-info {
          margin-bottom: 20px;
        }
        .info-row {
          display: flex;
          margin-bottom: 10px;
        }
        .info-label {
          font-weight: bold;
          width: 150px;
        }
        .info-value {
          flex: 1;
        }
        .print-section {
          margin-bottom: 20px;
        }
        .print-section h2 {
          font-size: 18px;
          border-bottom: 1px solid #ccc;
          padding-bottom: 5px;
        }
        table {
          width: 100%;
          border-collapse: collapse;
        }
        th, td {
          border: 1px solid #ccc;
          padding: 8px;
          text-align: left;
        }
        th {
          background-color: #f0f0f0;
        }
        .print-footer {
          margin-top: 40px;
          display: flex;
          justify-content: space-between;
        }
        .signature {
          width: 45%;
        }
        @media print {
          body {
            padding: 0;
          }
          .no-print {
            display: none;
          }
        }
      </style>
    </head>
    <body>
      <div class="print-container">
        <div class="print-header">
          <h1>资产维修工单</h1>
          <p>工单编号: ${maintenanceDetail.requestNo}</p>
        </div>
        
        <div class="print-info">
          <div class="info-row">
            <div class="info-label">资产名称:</div>
            <div class="info-value">${maintenanceDetail.assetName}</div>
            <div class="info-label">资产编码:</div>
            <div class="info-value">${maintenanceDetail.assetCode}</div>
          </div>
          <div class="info-row">
            <div class="info-label">资产类型:</div>
            <div class="info-value">${maintenanceDetail.assetType}</div>
            <div class="info-label">所属部门:</div>
            <div class="info-value">${maintenanceDetail.department}</div>
          </div>
          <div class="info-row">
            <div class="info-label">位置:</div>
            <div class="info-value">${maintenanceDetail.location}</div>
            <div class="info-label">维修类型:</div>
            <div class="info-value">${maintenanceDetail.maintenanceType}</div>
          </div>
          <div class="info-row">
            <div class="info-label">申请人:</div>
            <div class="info-value">${maintenanceDetail.requestUser}</div>
            <div class="info-label">申请时间:</div>
            <div class="info-value">${maintenanceDetail.requestTime}</div>
          </div>
          <div class="info-row">
            <div class="info-label">维修状态:</div>
            <div class="info-value">${maintenanceDetail.status}</div>
            <div class="info-label">优先级:</div>
            <div class="info-value">${maintenanceDetail.priority}</div>
          </div>
        </div>
        
        <div class="print-section">
          <h2>故障信息</h2>
          <div>${maintenanceDetail.faultDescription}</div>
        </div>
        
        ${maintenanceDetail.maintenanceResult ? `
        <div class="print-section">
          <h2>维修结果</h2>
          <div>${maintenanceDetail.maintenanceResult}</div>
        </div>
        ` : ''}
        
        ${maintenanceDetail.maintenanceRecords.length > 0 ? `
        <div class="print-section">
          <h2>维修记录</h2>
          <table>
            <thead>
              <tr>
                <th>时间</th>
                <th>维修人员</th>
                <th>维修内容</th>
              </tr>
            </thead>
            <tbody>
              ${maintenanceDetail.maintenanceRecords.map(record => `
              <tr>
                <td>${record.operateTime}</td>
                <td>${record.operateUser}</td>
                <td>${record.content}</td>
              </tr>
              `).join('')}
            </tbody>
          </table>
        </div>
        ` : ''}
        
        ${maintenanceDetail.maintenanceParts.length > 0 ? `
        <div class="print-section">
          <h2>配件使用</h2>
          <table>
            <thead>
              <tr>
                <th>配件名称</th>
                <th>规格型号</th>
                <th>数量</th>
                <th>单价</th>
                <th>总价</th>
              </tr>
            </thead>
            <tbody>
              ${maintenanceDetail.maintenanceParts.map(part => `
              <tr>
                <td>${part.partName}</td>
                <td>${part.specification}</td>
                <td>${part.quantity}</td>
                <td>¥${part.unitPrice.toLocaleString()}</td>
                <td>¥${part.totalPrice.toLocaleString()}</td>
              </tr>
              `).join('')}
            </tbody>
          </table>
        </div>
        ` : ''}
        
        <div class="print-section">
          <h2>费用信息</h2>
          <table>
            <thead>
              <tr>
                <th>费用类型</th>
                <th>费用名称</th>
                <th>金额</th>
                <th>备注</th>
              </tr>
            </thead>
            <tbody>
              ${maintenanceDetail.maintenanceCosts.map(cost => `
              <tr>
                <td>${cost.costType}</td>
                <td>${cost.costName}</td>
                <td>¥${cost.amount.toLocaleString()}</td>
                <td>${cost.remarks || ''}</td>
              </tr>
              `).join('')}
              <tr>
                <td colspan="2" style="text-align: right; font-weight: bold;">总计:</td>
                <td colspan="2">¥${maintenanceDetail.totalCost.toLocaleString()}</td>
              </tr>
            </tbody>
          </table>
        </div>
        
        <div class="print-footer">
          <div class="signature">
            <div>维修人员签名: ________________</div>
            <div>日期: ________________</div>
          </div>
          <div class="signature">
            <div>验收人员签名: ________________</div>
            <div>日期: ________________</div>
          </div>
        </div>
        
        <div class="no-print" style="margin-top: 30px; text-align: center;">
          <button onclick="window.print();" style="padding: 8px 20px; font-size: 16px; cursor: pointer;">打印工单</button>
          <button onclick="window.close();" style="padding: 8px 20px; font-size: 16px; margin-left: 20px; cursor: pointer;">关闭窗口</button>
        </div>
      </div>
    </body>
    </html>
  `
  
  printWindow.document.open()
  printWindow.document.write(printContent)
  printWindow.document.close()
  
  // 等待图像加载完成后打印
  setTimeout(() => {
    printWindow.focus()
    // 自动打印 (取消注释下面一行代码可启用自动打印)
    // printWindow.print()
  }, 1000)
  
  ElMessage.success('打印窗口已打开')
}

// 导出维修报告
const exportReport = () => {
  // 创建一个HTML字符串，作为Word文档的内容
  const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <title>维修报告 - ${maintenanceDetail.requestNo}</title>
      <style>
        body {
          font-family: 'Microsoft YaHei', Arial, sans-serif;
          font-size: 12pt;
          line-height: 1.5;
          color: #333;
        }
        .header {
          text-align: center;
          margin-bottom: 20px;
        }
        h1 {
          font-size: 18pt;
          color: #2c3e50;
          text-align: center;
          margin-bottom: 20px;
        }
        h2 {
          font-size: 14pt;
          color: #2980b9;
          border-bottom: 1px solid #eee;
          padding-bottom: 5px;
          margin-top: 20px;
        }
        table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 20px;
        }
        table, th, td {
          border: 1px solid #ddd;
        }
        th {
          background-color: #f2f2f2;
          text-align: left;
          padding: 8px;
          font-weight: bold;
        }
        td {
          padding: 8px;
        }
        .info-table td:first-child {
          width: 20%;
          font-weight: bold;
          background-color: #f9f9f9;
        }
        .footer {
          margin-top: 30px;
          font-size: 10pt;
          color: #777;
          text-align: center;
        }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>维修报告</h1>
        <p>工单编号: ${maintenanceDetail.requestNo}</p>
      </div>

      <h2>基本信息</h2>
      <table class="info-table">
        <tr>
          <td>资产名称</td>
          <td>${maintenanceDetail.assetName}</td>
        </tr>
        <tr>
          <td>资产编码</td>
          <td>${maintenanceDetail.assetCode}</td>
        </tr>
        <tr>
          <td>资产类型</td>
          <td>${maintenanceDetail.assetType}</td>
        </tr>
        <tr>
          <td>位置</td>
          <td>${maintenanceDetail.location}</td>
        </tr>
        <tr>
          <td>所属部门</td>
          <td>${maintenanceDetail.department}</td>
        </tr>
      </table>

      <h2>维修信息</h2>
      <table class="info-table">
        <tr>
          <td>维修类型</td>
          <td>${maintenanceDetail.maintenanceType}</td>
        </tr>
        <tr>
          <td>优先级</td>
          <td>${maintenanceDetail.priority}</td>
        </tr>
        <tr>
          <td>申请人</td>
          <td>${maintenanceDetail.requestUser}</td>
        </tr>
        <tr>
          <td>申请时间</td>
          <td>${maintenanceDetail.requestTime}</td>
        </tr>
        <tr>
          <td>维修人员</td>
          <td>${maintenanceDetail.assignedTo || '未指派'}</td>
        </tr>
        <tr>
          <td>计划时间</td>
          <td>${maintenanceDetail.plannedStartTime || '未设置'} 至 ${maintenanceDetail.plannedEndTime || '未设置'}</td>
        </tr>
        <tr>
          <td>实际时间</td>
          <td>${maintenanceDetail.actualStartTime || '未开始'} 至 ${maintenanceDetail.actualEndTime || '未完成'}</td>
        </tr>
        <tr>
          <td>当前状态</td>
          <td>${maintenanceDetail.status}</td>
        </tr>
      </table>

      <h2>故障描述</h2>
      <p>${maintenanceDetail.faultDescription}</p>

      ${maintenanceDetail.maintenanceResult ? `
      <h2>维修结果</h2>
      <p>${maintenanceDetail.maintenanceResult}</p>
      ` : ''}

      ${maintenanceDetail.maintenanceRecords.length > 0 ? `
      <h2>维修记录</h2>
      <table>
        <thead>
          <tr>
            <th>时间</th>
            <th>维修人员</th>
            <th>维修内容</th>
          </tr>
        </thead>
        <tbody>
          ${maintenanceDetail.maintenanceRecords.map(record => `
          <tr>
            <td>${record.operateTime}</td>
            <td>${record.operateUser}</td>
            <td>${record.content}</td>
          </tr>
          `).join('')}
        </tbody>
      </table>
      ` : ''}

      ${maintenanceDetail.maintenanceParts.length > 0 ? `
      <h2>配件使用</h2>
      <table>
        <thead>
          <tr>
            <th>配件名称</th>
            <th>规格型号</th>
            <th>数量</th>
            <th>单价</th>
            <th>总价</th>
          </tr>
        </thead>
        <tbody>
          ${maintenanceDetail.maintenanceParts.map(part => `
          <tr>
            <td>${part.partName}</td>
            <td>${part.specification}</td>
            <td>${part.quantity}</td>
            <td>¥${part.unitPrice.toLocaleString()}</td>
            <td>¥${part.totalPrice.toLocaleString()}</td>
          </tr>
          `).join('')}
        </tbody>
      </table>
      ` : ''}

      <h2>费用明细</h2>
      <table>
        <thead>
          <tr>
            <th>费用类型</th>
            <th>费用名称</th>
            <th>金额</th>
            <th>备注</th>
          </tr>
        </thead>
        <tbody>
          ${maintenanceDetail.maintenanceCosts.map(cost => `
          <tr>
            <td>${cost.costType}</td>
            <td>${cost.costName}</td>
            <td>¥${cost.amount.toLocaleString()}</td>
            <td>${cost.remarks || ''}</td>
          </tr>
          `).join('')}
          <tr>
            <td colspan="2" style="text-align: right; font-weight: bold;">总计:</td>
            <td colspan="2">¥${maintenanceDetail.totalCost.toLocaleString()}</td>
          </tr>
        </tbody>
      </table>

      ${maintenanceDetail.acceptanceUser ? `
      <h2>验收信息</h2>
      <table class="info-table">
        <tr>
          <td>验收人</td>
          <td>${maintenanceDetail.acceptanceUser}</td>
        </tr>
        <tr>
          <td>验收时间</td>
          <td>${maintenanceDetail.acceptanceTime}</td>
        </tr>
        <tr>
          <td>验收结果</td>
          <td>${maintenanceDetail.acceptanceResult ? '通过' : '未通过'}</td>
        </tr>
        <tr>
          <td>验收意见</td>
          <td>${maintenanceDetail.acceptanceComments || '无'}</td>
        </tr>
      </table>
      ` : ''}

      <div class="footer">
        <p>报告生成时间: ${new Date().toLocaleString()}</p>
        <p>水投集团资产管理系统</p>
      </div>
    </body>
    </html>
  `

  // 使用 Blob 转换 HTML 为 Word 文档
  const blob = new Blob(['\ufeff', htmlContent], {
    type: 'application/msword;charset=utf-8'
  })

  // 创建下载链接
  const link = document.createElement('a')
  // 使用 msSaveOrOpenBlob 方法解决 IE 兼容性问题
  if (window.navigator && window.navigator.msSaveOrOpenBlob) {
    window.navigator.msSaveOrOpenBlob(blob, `维修报告-${maintenanceDetail.requestNo}.doc`)
  } else {
    // 为其他浏览器创建下载链接
    link.href = URL.createObjectURL(blob)
    link.download = `维修报告-${maintenanceDetail.requestNo}.doc`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
  
  ElMessage.success('维修报告已导出为Word文档')
}

// 页面加载时获取详情
onMounted(() => {
  fetchMaintenanceDetail()
})
</script>

<template>
  <div class="maintenance-detail-container">
    <el-card v-loading="loading">
      <template #header>
        <div class="card-header">
          <div class="header-title">
            <el-button @click="goBack" link>
              <el-icon><arrow-left /></el-icon> 返回
            </el-button>
            <h3>维修工单详情</h3>
          </div>
          <div class="header-actions">
            <el-button type="primary" v-if="maintenanceDetail.status === '维修完成'" @click="acceptMaintenance">验收</el-button>
            <el-button type="warning" v-if="maintenanceDetail.status === '维修完成'" @click="rejectMaintenance">驳回</el-button>
            <el-button @click="printOrder">打印工单</el-button>
            <el-button type="success" @click="exportReport">导出报告</el-button>
          </div>
        </div>
      </template>

      <!-- 工单概要信息 -->
      <div class="order-summary">
        <el-descriptions :column="4" border>
          <el-descriptions-item label="工单编号">{{ maintenanceDetail.requestNo }}</el-descriptions-item>
          <el-descriptions-item label="工单状态">
            <el-tag 
              :type="
                maintenanceDetail.status === '维修申请' ? 'warning' : 
                maintenanceDetail.status === '已派工' || maintenanceDetail.status === '维修中' ? 'primary' : 
                maintenanceDetail.status === '维修完成' || maintenanceDetail.status === '已验收' ? 'success' : 
                'danger'
              "
            >
              {{ maintenanceDetail.status }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="优先级">
            <el-tag 
              :type="
                maintenanceDetail.priority === '紧急' ? 'danger' : 
                maintenanceDetail.priority === '高' ? 'warning' : 
                maintenanceDetail.priority === '中' ? 'success' : 
                'info'
              "
            >
              {{ maintenanceDetail.priority }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="维修类型">{{ maintenanceDetail.maintenanceType }}</el-descriptions-item>
          
          <el-descriptions-item label="申请人">{{ maintenanceDetail.requestUser }}</el-descriptions-item>
          <el-descriptions-item label="申请时间">{{ maintenanceDetail.requestTime }}</el-descriptions-item>
          <el-descriptions-item label="维修人员">{{ maintenanceDetail.assignedTo }}</el-descriptions-item>
          <el-descriptions-item label="维修费用">¥{{ maintenanceDetail.totalCost.toLocaleString() }}</el-descriptions-item>
        </el-descriptions>
      </div>
      
      <!-- 详细信息标签页 -->
      <el-tabs v-model="activeTab" class="detail-tabs">
        <el-tab-pane label="基本信息" name="basic">
          <el-descriptions :column="3" border>
            <el-descriptions-item label="资产名称" :span="3">{{ maintenanceDetail.assetName }}</el-descriptions-item>
            <el-descriptions-item label="资产编码">{{ maintenanceDetail.assetCode }}</el-descriptions-item>
            <el-descriptions-item label="资产类型">{{ maintenanceDetail.assetType }}</el-descriptions-item>
            <el-descriptions-item label="所属部门">{{ maintenanceDetail.department }}</el-descriptions-item>
            
            <el-descriptions-item label="位置">{{ maintenanceDetail.location }}</el-descriptions-item>
            <el-descriptions-item label="计划开始时间">{{ maintenanceDetail.plannedStartTime }}</el-descriptions-item>
            <el-descriptions-item label="计划结束时间">{{ maintenanceDetail.plannedEndTime }}</el-descriptions-item>
            
            <el-descriptions-item label="实际开始时间">{{ maintenanceDetail.actualStartTime }}</el-descriptions-item>
            <el-descriptions-item label="实际结束时间">{{ maintenanceDetail.actualEndTime }}</el-descriptions-item>
            <el-descriptions-item label="审批人">{{ maintenanceDetail.reviewUser }}</el-descriptions-item>
            
            <el-descriptions-item label="故障描述" :span="3">
              {{ maintenanceDetail.faultDescription }}
            </el-descriptions-item>
            <el-descriptions-item label="维修结果" :span="3">
              {{ maintenanceDetail.maintenanceResult }}
            </el-descriptions-item>
            <el-descriptions-item label="审批意见" :span="3">
              {{ maintenanceDetail.reviewComments }}
            </el-descriptions-item>
          </el-descriptions>
          
          <!-- 附件 -->
          <div class="attachments-section">
            <h4>附件资料</h4>
            <el-table :data="attachments" style="width: 100%">
              <el-table-column prop="name" label="文件名" />
              <el-table-column label="操作" width="100">
                <template #default>
                  <el-button type="primary" link>预览</el-button>
                  <el-button type="success" link>下载</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
        
        <!-- 维修记录 -->
        <el-tab-pane label="维修记录" name="records">
          <el-timeline>
            <el-timeline-item
              v-for="item in maintenanceDetail.maintenanceRecords"
              :key="item.id"
              :timestamp="item.operateTime"
              placement="top"
            >
              <el-card>
                <h4>{{ item.operateUser }}</h4>
                <p>{{ item.content }}</p>
                <div v-if="item.attachments && item.attachments.length > 0" class="record-attachments">
                  <p>附件: </p>
                  <el-tag v-for="(attach, idx) in item.attachments" :key="idx" type="info" effect="plain">
                    {{ attach }}
                  </el-tag>
                </div>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </el-tab-pane>
        
        <!-- 费用明细 -->
        <el-tab-pane label="费用明细" name="costs">
          <div class="costs-summary">
            <h4>总费用: ¥{{ maintenanceDetail.totalCost.toLocaleString() }}</h4>
          </div>
          
          <el-table :data="maintenanceDetail.maintenanceCosts" style="width: 100%" border>
            <el-table-column prop="costType" label="费用类型" width="120" />
            <el-table-column prop="costName" label="费用名称" width="180" />
            <el-table-column prop="amount" label="金额(元)" width="120">
              <template #default="{ row }">
                ¥{{ row.amount.toLocaleString() }}
              </template>
            </el-table-column>
            <el-table-column prop="remarks" label="备注" />
          </el-table>
          
          <h4 class="parts-title">配件使用</h4>
          <el-table :data="maintenanceDetail.maintenanceParts" style="width: 100%" border>
            <el-table-column prop="partName" label="配件名称" width="120" />
            <el-table-column prop="partCode" label="配件编码" width="120" />
            <el-table-column prop="specification" label="规格型号" width="180" />
            <el-table-column prop="quantity" label="数量" width="80" />
            <el-table-column prop="unitPrice" label="单价(元)" width="100">
              <template #default="{ row }">
                ¥{{ row.unitPrice.toLocaleString() }}
              </template>
            </el-table-column>
            <el-table-column prop="totalPrice" label="总价(元)" width="100">
              <template #default="{ row }">
                ¥{{ row.totalPrice.toLocaleString() }}
              </template>
            </el-table-column>
            <el-table-column prop="supplier" label="供应商" width="150" />
            <el-table-column prop="remarks" label="备注" />
          </el-table>
        </el-tab-pane>
        
        <!-- 状态日志 -->
        <el-tab-pane label="状态日志" name="logs">
          <el-table :data="maintenanceDetail.statusLogs" style="width: 100%" border>
            <el-table-column prop="operateTime" label="时间" width="180" />
            <el-table-column prop="operateUser" label="操作人" width="120" />
            <el-table-column label="状态变更" width="200">
              <template #default="{ row }">
                <div class="status-change">
                  <span v-if="row.oldStatus">{{ row.oldStatus }}</span>
                  <span v-else>-</span>
                  <el-icon><arrow-right /></el-icon>
                  <span>{{ row.newStatus }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="comments" label="备注" />
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<style lang="scss" scoped>
.maintenance-detail-container {
  padding: 20px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-title {
      display: flex;
      align-items: center;
      gap: 10px;

      h3 {
        margin: 0;
        font-size: 18px;
      }
    }

    .header-actions {
      display: flex;
      gap: 10px;
    }
  }

  .order-summary {
    margin-bottom: 20px;
  }

  .detail-tabs {
    margin-top: 20px;
  }

  .attachments-section {
    margin-top: 20px;
    
    h4 {
      margin-bottom: 10px;
    }
  }

  .costs-summary {
    margin-bottom: 20px;
  }

  .parts-title {
    margin-top: 30px;
    margin-bottom: 10px;
  }

  .status-change {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .record-attachments {
    margin-top: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
    
    p {
      margin: 0;
    }
    
    .el-tag {
      margin-right: 5px;
    }
  }
}
</style> 