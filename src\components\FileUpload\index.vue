<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { UploadFile, UploadUserFile, UploadProps, UploadRawFile } from 'element-plus'
import { AttachmentInfo } from '@/types/asset/lifecycle'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Array as () => AttachmentInfo[],
    default: () => []
  },
  disabled: {
    type: Boolean,
    default: false
  },
  limit: {
    type: Number,
    default: 5
  },
  fileTypes: {
    type: Array as () => string[],
    default: () => ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx', 'xls', 'xlsx']
  },
  maxSize: {
    type: Number,
    default: 10 // MB
  }
})

const emit = defineEmits(['update:modelValue', 'delete'])

// 文件列表
const fileList = computed<UploadUserFile[]>(() => {
  return props.modelValue.map(item => ({
    name: item.name,
    url: item.url,
    size: item.size,
    uid: item.id,
    status: 'success'
  }))
})

// 文件上传前的验证
const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  // 检查文件类型
  const extension = file.name.split('.').pop()?.toLowerCase() || ''
  if (!props.fileTypes.includes(extension)) {
    ElMessage.error(`只能上传 ${props.fileTypes.join(', ')} 格式的文件!`)
    return false
  }
  
  // 检查文件大小
  const isLessThanLimit = file.size / 1024 / 1024 < props.maxSize
  if (!isLessThanLimit) {
    ElMessage.error(`文件大小不能超过 ${props.maxSize}MB!`)
    return false
  }
  
  return true
}

// 上传成功处理
const handleSuccess = (response: any, uploadFile: UploadFile, uploadFiles: UploadFile[]) => {
  // 这里假设后端返回的数据格式为 { code: 200, data: { id: 'xxx', url: 'xxx', name: 'xxx', size: xxx } }
  // 实际项目中应当根据后端接口返回格式进行调整
  const mockResponse = {
    code: 200,
    data: {
      id: `file-${Date.now()}`,
      url: URL.createObjectURL(uploadFile.raw as Blob),
      name: uploadFile.name,
      size: uploadFile.size || 0,
      uploadTime: new Date().toISOString(),
      uploadBy: 'current_user'
    }
  }
  
  // 更新文件列表
  const newFileList = [...props.modelValue, mockResponse.data]
  emit('update:modelValue', newFileList)
}

// 上传失败处理
const handleError = (error: any) => {
  ElMessage.error('文件上传失败')
  console.error('文件上传失败', error)
}

// 删除文件处理
const handleRemove = (file: UploadFile) => {
  const newFileList = props.modelValue.filter(item => item.id !== file.uid)
  emit('update:modelValue', newFileList)
  emit('delete', file.uid)
}

// 上传请求钩子
const httpRequest: UploadProps['httpRequest'] = (options) => {
  // 这里模拟上传过程
  return new Promise((resolve) => {
    setTimeout(() => {
      const file = options.file as UploadRawFile
      const mockResponse = {
        code: 200,
        data: {
          id: `file-${Date.now()}`,
          url: URL.createObjectURL(file),
          name: file.name,
          size: file.size,
          uploadTime: new Date().toISOString(),
          uploadBy: 'current_user'
        }
      }
      handleSuccess(mockResponse, {
        name: file.name,
        size: file.size,
        raw: file,
        uid: `file-${Date.now()}`
      }, [])
      resolve(mockResponse)
    }, 1000)
  })
}

// 预览文件
const handlePreview = (file: UploadFile) => {
  if (file.url) {
    window.open(file.url)
  }
}
</script>

<template>
  <div class="file-upload">
    <el-upload
      v-model:file-list="fileList"
      action="#"
      :before-upload="beforeUpload"
      :on-error="handleError"
      :on-remove="handleRemove"
      :on-preview="handlePreview"
      :http-request="httpRequest"
      :disabled="disabled"
      :limit="limit"
      :multiple="true"
      :file-list="fileList"
      class="upload-component"
    >
      <el-button type="primary" :disabled="disabled || fileList.length >= limit">
        <el-icon><el-icon-upload /></el-icon>
        上传附件
      </el-button>
      <template #tip>
        <div class="el-upload__tip">
          支持 {{ props.fileTypes.join('、') }} 格式，单个文件不超过 {{ props.maxSize }}MB，最多上传 {{ props.limit }} 个文件
        </div>
      </template>
    </el-upload>
  </div>
</template>

<style lang="scss" scoped>
.file-upload {
  .upload-component {
    width: 100%;
  }
  
  .el-upload__tip {
    line-height: 1.5;
    color: #909399;
    font-size: 12px;
    margin-top: 8px;
  }
}
</style> 