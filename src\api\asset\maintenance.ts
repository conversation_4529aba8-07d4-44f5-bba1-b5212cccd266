import request from '@/config/axios'
import { MaintenanceQueryParams, MaintenanceRequest, MaintenanceOrderDetail, MaintenanceStatistics, MaintenancePlan } from '@/types/asset/maintenance'

// API URL前缀
const API_PREFIX = '/api/asset/maintenance'

// ==================== 维修申请 ====================

// 获取维修列表（分页查询）
export const getMaintenanceList = (params: MaintenanceQueryParams) => {
  return request.get({
    url: `${API_PREFIX}/list`,
    params
  })
}

// 获取维修详情
export const getMaintenanceDetail = (id: string) => {
  return request.get({
    url: `${API_PREFIX}/detail/${id}`
  })
}

// 创建维修申请
export const createMaintenance = (data: Partial<MaintenanceRequest>) => {
  return request.post({
    url: `${API_PREFIX}/create`,
    data
  })
}

// 更新维修申请
export const updateMaintenance = (data: Partial<MaintenanceRequest>) => {
  return request.put({
    url: `${API_PREFIX}/update`,
    data
  })
}

// 删除维修申请
export const deleteMaintenance = (id: string) => {
  return request.delete({
    url: `${API_PREFIX}/delete/${id}`
  })
}

// ==================== 维修审批 ====================

// 审批维修申请
export const approveMaintenance = (id: string, approved: boolean, comments: string) => {
  return request.post({
    url: `${API_PREFIX}/approve/${id}`,
    data: {
      approved,
      comments
    }
  })
}

// 派工
export const assignMaintenance = (id: string, assignedTo: string, plannedStartTime: string, plannedEndTime: string) => {
  return request.post({
    url: `${API_PREFIX}/assign/${id}`,
    data: {
      assignedTo,
      plannedStartTime,
      plannedEndTime
    }
  })
}

// ==================== 维修执行 ====================

// 开始维修
export const startMaintenance = (id: string) => {
  return request.post({
    url: `${API_PREFIX}/start/${id}`
  })
}

// 完成维修
export const completeMaintenance = (id: string, maintenanceResult: string, attachments: string[]) => {
  return request.post({
    url: `${API_PREFIX}/complete/${id}`,
    data: {
      maintenanceResult,
      attachments
    }
  })
}

// 添加维修记录
export const addMaintenanceRecord = (maintenanceId: string, content: string, attachments: string[] = []) => {
  return request.post({
    url: `${API_PREFIX}/record/add`,
    data: {
      maintenanceId,
      content,
      attachments
    }
  })
}

// ==================== 维修验收 ====================

// 验收维修
export const acceptMaintenance = (id: string, accepted: boolean, comments: string) => {
  return request.post({
    url: `${API_PREFIX}/accept/${id}`,
    data: {
      accepted,
      comments
    }
  })
}

// ==================== 维修费用 ====================

// 添加维修费用
export const addMaintenanceCost = (maintenanceId: string, costType: string, costName: string, amount: number, remarks?: string) => {
  return request.post({
    url: `${API_PREFIX}/cost/add`,
    data: {
      maintenanceId,
      costType,
      costName,
      amount,
      remarks
    }
  })
}

// 删除维修费用
export const deleteMaintenanceCost = (id: string) => {
  return request.delete({
    url: `${API_PREFIX}/cost/delete/${id}`
  })
}

// 添加维修配件
export const addMaintenancePart = (maintenanceId: string, partData: any) => {
  return request.post({
    url: `${API_PREFIX}/part/add`,
    data: {
      maintenanceId,
      ...partData
    }
  })
}

// 删除维修配件
export const deleteMaintenancePart = (id: string) => {
  return request.delete({
    url: `${API_PREFIX}/part/delete/${id}`
  })
}

// ==================== 维修统计 ====================

// 获取维修统计数据
export const getMaintenanceStatistics = (params: { timeRange?: [string, string], departmentId?: string }) => {
  return request.get({
    url: `${API_PREFIX}/statistics`,
    params
  })
}

// 导出维修报表
export const exportMaintenanceReport = (params: any) => {
  return request.get({
    url: `${API_PREFIX}/export`,
    params,
    responseType: 'blob'
  })
}

// ==================== 维保计划 ====================

// 获取维保计划列表
export const getMaintenancePlanList = (params: any) => {
  return request.get({
    url: `${API_PREFIX}/plan/list`,
    params
  })
}

// 获取维保计划详情
export const getMaintenancePlanDetail = (id: string) => {
  return request.get({
    url: `${API_PREFIX}/plan/detail/${id}`
  })
}

// 创建维保计划
export const createMaintenancePlan = (data: Partial<MaintenancePlan>) => {
  return request.post({
    url: `${API_PREFIX}/plan/create`,
    data
  })
}

// 更新维保计划
export const updateMaintenancePlan = (data: Partial<MaintenancePlan>) => {
  return request.put({
    url: `${API_PREFIX}/plan/update`,
    data
  })
}

// 删除维保计划
export const deleteMaintenancePlan = (id: string) => {
  return request.delete({
    url: `${API_PREFIX}/plan/delete/${id}`
  })
}

// 启用/禁用维保计划
export const toggleMaintenancePlanStatus = (id: string, status: 'active' | 'inactive') => {
  return request.put({
    url: `${API_PREFIX}/plan/toggle/${id}`,
    data: { status }
  })
}

// 获取维保任务列表
export const getMaintenancePlanTasks = (params: any) => {
  return request.get({
    url: `${API_PREFIX}/plan/tasks`,
    params
  })
}

// 更新维保任务状态
export const updateMaintenancePlanTaskStatus = (id: string, status: string, remarks?: string) => {
  return request.put({
    url: `${API_PREFIX}/plan/task/${id}`,
    data: {
      status,
      remarks
    }
  })
}

// 手动生成维保任务
export const generateMaintenanceTasks = (planId: string) => {
  return request.post({
    url: `${API_PREFIX}/plan/generateTasks/${planId}`
  })
} 