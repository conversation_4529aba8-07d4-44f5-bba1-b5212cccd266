import request from '@/config/axios'

// 查询资产属性模板列表
export const getAttributeTemplateList = (params: any) => {
  return request.get({ url: '/api/asset/attribute-template/list', params })
}

// 获取指定资产类型的属性模板字段
export const getAttributeTemplateByTypeId = (typeId: number) => {
  return request.get({ url: `/api/asset/attribute-template/${typeId}` })
}

// 新增资产属性模板字段
export const addAttributeTemplate = (data: any) => {
  return request.post({ url: '/api/asset/attribute-template', data })
}

// 修改资产属性模板字段
export const updateAttributeTemplate = (data: any) => {
  return request.put({ url: '/api/asset/attribute-template', data })
}

// 删除资产属性模板字段
export const delAttributeTemplate = (id: number) => {
  return request.delete({ url: `/api/asset/attribute-template/${id}` })
} 