import request from '@/config/axios'

// 查询资产类型列表
export const getAssetTypeList = (params: any) => {
  return request.get({ url: '/api/asset/type/list', params })
}

// 新增资产类型
export const addAssetType = (data: any) => {
  return request.post({ url: '/api/asset/type', data })
}

// 修改资产类型
export const updateAssetType = (data: any) => {
  return request.put({ url: '/api/asset/type', data })
}

// 删除资产类型
export const delAssetType = (id: number) => {
  return request.delete({ url: `/api/asset/type/${id}` })
} 