import request from '@/config/axios'

// 查询使用单位列表
export const getUnitList = (params: any) => {
  return request.get({ url: '/api/asset/unit/list', params })
}

// 获取使用单位树结构
export const getUnitTree = () => {
  return request.get({ url: '/api/asset/unit/tree' })
}

// 新增使用单位
export const addUnit = (data: any) => {
  return request.post({ url: '/api/asset/unit', data })
}

// 修改使用单位
export const updateUnit = (data: any) => {
  return request.put({ url: '/api/asset/unit', data })
}

// 删除使用单位
export const delUnit = (id: number) => {
  return request.delete({ url: `/api/asset/unit/${id}` })
}

// 获取单位详情
export const getUnitDetail = (id: number) => {
  return request.get({ url: `/api/asset/unit/${id}` })
} 