import request from '@/config/axios'
import type { AssetStatus, AssetStatusStatistics, AssetStatusChangeLog, QueryParams, PageResult } from '@/types/asset'

// 查询资产状态列表
export const getAssetStatusList = (params: QueryParams) => {
  return request.get<PageResult<AssetStatus>>({ 
    url: '/api/asset/status/list', 
    params 
  })
}

// 获取资产状态详情
export const getAssetStatusDetail = (id: number) => {
  return request.get<AssetStatus>({ 
    url: `/api/asset/status/${id}` 
  })
}

// 新增资产状态
export const addAssetStatus = (data: Omit<AssetStatus, 'id' | 'createTime' | 'createBy'>) => {
  return request.post<AssetStatus>({ 
    url: '/api/asset/status', 
    data 
  })
}

// 修改资产状态
export const updateAssetStatus = (data: Partial<AssetStatus> & { id: number }) => {
  return request.put<AssetStatus>({ 
    url: '/api/asset/status', 
    data 
  })
}

// 删除资产状态
export const deleteAssetStatus = (id: number) => {
  return request.delete({ 
    url: `/api/asset/status/${id}` 
  })
}

// 批量删除资产状态
export const batchDeleteAssetStatus = (ids: number[]) => {
  return request.delete({ 
    url: '/api/asset/status/batch', 
    data: ids 
  })
}

// 获取资产状态统计数据
export const getAssetStatusStatistics = () => {
  return request.get<AssetStatusStatistics>({ 
    url: '/api/asset/status/statistics' 
  })
}

// 获取资产状态变更记录
export const getAssetStatusChangeLogs = (statusId: number) => {
  return request.get<AssetStatusChangeLog[]>({ 
    url: '/api/asset/status/logs', 
    params: { statusId } 
  })
} 