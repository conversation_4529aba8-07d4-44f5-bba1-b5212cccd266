<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { Search, Refresh, Plus, Edit, Delete, Calendar, Document } from '@element-plus/icons-vue'
import request from '@/config/axios'

// 类型定义
interface MaintenancePlan {
  id: string
  planNo: string
  name: string
  description: string
  assetType: string
  cycle: number
  cycleUnit: string
  status: string
  startDate: string
  endDate: string
  notifyDays: number
  notifyRoles: string[]
  createBy: string
  createTime: string
  lastExecuteTime: string | null
  nextExecuteTime: string | null
  assetCount: number
}

interface PlanAsset {
  id: string
  assetId: string
  assetCode: string
  assetName: string
  assetType: string
  department: string
  location: string
  lastExecuteTime: string | null
  nextExecuteTime: string | null
  status: string
}

// 状态变量
const loading = ref(false)
const drawerVisible = ref(false)
const drawerType = ref('add')
const drawerTitle = computed(() => drawerType.value === 'add' ? '新增维保计划' : '编辑维保计划')

// 表单相关
const formRef = ref<FormInstance>()
const form = reactive({
  id: '',
  name: '',
  description: '',
  assetType: '',
  cycle: 30,
  cycleUnit: 'day',
  status: 'active',
  startDate: '',
  endDate: '',
  notifyDays: 3,
  notifyRoles: [] as string[]
})

// 表单验证规则
const rules = reactive<FormRules>({
  name: [
    { required: true, message: '请输入计划名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  assetType: [
    { required: true, message: '请选择资产类型', trigger: 'change' }
  ],
  cycle: [
    { required: true, message: '请输入周期', trigger: 'blur' },
    { type: 'number', min: 1, message: '周期必须大于0', trigger: 'blur' }
  ],
  cycleUnit: [
    { required: true, message: '请选择周期单位', trigger: 'change' }
  ],
  startDate: [
    { required: true, message: '请选择开始日期', trigger: 'change' }
  ]
})

// 资产类型选项
const assetTypeOptions = [
  { value: 'production', label: '生产设备' },
  { value: 'office', label: '办公设备' },
  { value: 'vehicle', label: '运输设备' },
  { value: 'building', label: '房屋建筑' }
]

// 周期单位选项
const cycleUnitOptions = [
  { value: 'day', label: '天' },
  { value: 'week', label: '周' },
  { value: 'month', label: '月' },
  { value: 'quarter', label: '季度' },
  { value: 'year', label: '年' }
]

// 角色选项
const roleOptions = [
  { value: 'manager', label: '管理员' },
  { value: 'technician', label: '技术员' },
  { value: 'user', label: '使用人' },
  { value: 'department', label: '部门负责人' }
]

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  keyword: '',
  status: '',
  dateRange: [] as string[]
})

// 表格数据
const tableData = ref<MaintenancePlan[]>([])
const total = ref(0)

// 资产列表
const assetSelectVisible = ref(false)
const currentPlan = ref<MaintenancePlan | null>(null)
const planAssets = ref<PlanAsset[]>([])
const assetLoading = ref(false)

// 状态映射
const statusMap = {
  active: { text: '启用', type: 'success' },
  inactive: { text: '停用', type: 'info' },
  completed: { text: '已完成', type: 'warning' },
  expired: { text: '已过期', type: 'danger' }
}

// 获取维保计划列表
const getList = async () => {
  loading.value = true
  try {
    // TODO: 调用API
    // 模拟API响应
    await new Promise(resolve => setTimeout(resolve, 300))
    
    // 生成模拟数据
    const mockData: MaintenancePlan[] = Array.from({ length: 20 }, (_, index) => {
      const startDate = new Date(Date.now() - Math.random() * 180 * 24 * 60 * 60 * 1000)
      const endDate = new Date(startDate.getTime() + 365 * 24 * 60 * 60 * 1000)
      const lastExecuteTime = index % 5 === 0 ? null : new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleString()
      
      const cycle = [30, 90, 180, 365][index % 4]
      const cycleUnit = ['day', 'day', 'day', 'day'][index % 4]
      
      let nextExecuteTime = null
      if (lastExecuteTime) {
        const lastDate = new Date(lastExecuteTime)
        nextExecuteTime = new Date(lastDate.getTime() + cycle * 24 * 60 * 60 * 1000).toLocaleString()
      } else {
        nextExecuteTime = new Date(startDate.getTime() + cycle * 24 * 60 * 60 * 1000).toLocaleString()
      }
      
      return {
        id: `${index + 1}`,
        planNo: `MP-${String(1000 + index).padStart(6, '0')}`,
        name: `${['日常', '季度', '半年', '年度'][index % 4]}维保计划-${index + 1}`,
        description: `对${assetTypeOptions[index % 4].label}进行${['日常', '季度', '半年', '年度'][index % 4]}维护保养`,
        assetType: assetTypeOptions[index % 4].value,
        cycle,
        cycleUnit,
        status: ['active', 'inactive', 'completed', 'expired'][index % 4],
        startDate: startDate.toLocaleDateString(),
        endDate: endDate.toLocaleDateString(),
        notifyDays: [1, 3, 5, 7][index % 4],
        notifyRoles: index % 2 === 0 ? ['manager', 'technician'] : ['technician', 'user'],
        createBy: '管理员',
        createTime: new Date(startDate.getTime() - 7 * 24 * 60 * 60 * 1000).toLocaleString(),
        lastExecuteTime,
        nextExecuteTime,
        assetCount: Math.floor(Math.random() * 20) + 1
      }
    })

    // 根据查询条件过滤
    let filteredData = [...mockData]
    
    if (queryParams.keyword) {
      filteredData = filteredData.filter(item => 
        item.name.includes(queryParams.keyword) || 
        item.planNo.includes(queryParams.keyword)
      )
    }
    
    if (queryParams.status) {
      filteredData = filteredData.filter(item => item.status === queryParams.status)
    }
    
    if (queryParams.dateRange && queryParams.dateRange.length === 2) {
      const startDate = new Date(queryParams.dateRange[0]).getTime()
      const endDate = new Date(queryParams.dateRange[1]).getTime() + 24 * 60 * 60 * 1000 - 1
      
      filteredData = filteredData.filter(item => {
        const itemStartDate = new Date(item.startDate).getTime()
        return itemStartDate >= startDate && itemStartDate <= endDate
      })
    }

    // 分页处理
    const start = (queryParams.pageNum - 1) * queryParams.pageSize
    const end = Math.min(start + queryParams.pageSize, filteredData.length)
    tableData.value = filteredData.slice(start, end)
    total.value = filteredData.length
  } catch (error) {
    console.error('获取维保计划失败:', error)
    ElMessage.error('获取维保计划失败')
  } finally {
    loading.value = false
  }
}

// 获取计划关联的资产列表
const getPlanAssets = async (plan: MaintenancePlan) => {
  assetLoading.value = true
  currentPlan.value = plan
  try {
    // 模拟API响应
    await new Promise(resolve => setTimeout(resolve, 300))
    
    // 生成模拟数据
    planAssets.value = Array.from({ length: plan.assetCount }, (_, index) => {
      const lastExecuteTime = index % 3 === 0 ? null : new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleString()
      
      let nextExecuteTime = null
      if (lastExecuteTime) {
        const lastDate = new Date(lastExecuteTime)
        nextExecuteTime = new Date(lastDate.getTime() + plan.cycle * 24 * 60 * 60 * 1000).toLocaleString()
      } else {
        nextExecuteTime = new Date(Date.now() + Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleString()
      }
      
      return {
        id: `${plan.id}-${index + 1}`,
        assetId: `${3000 + index}`,
        assetCode: `ZC${String(3000 + index).padStart(6, '0')}`,
        assetName: `${assetTypeOptions.find(item => item.value === plan.assetType)?.label}${index + 1}`,
        assetType: plan.assetType,
        department: ['生产部', '行政部', '技术部'][index % 3],
        location: ['一号车间', '二号车间', '办公区'][index % 3],
        lastExecuteTime,
        nextExecuteTime,
        status: index % 5 === 0 ? 'overdue' : (index % 3 === 0 ? 'upcoming' : 'normal')
      }
    })
    
    assetSelectVisible.value = true
  } catch (error) {
    console.error('获取计划资产列表失败:', error)
    ElMessage.error('获取计划资产列表失败')
  } finally {
    assetLoading.value = false
  }
}

// 打开新增对话框
const handleAdd = () => {
  drawerType.value = 'add'
  resetForm()
  drawerVisible.value = true
}

// 打开编辑对话框
const handleEdit = (row: MaintenancePlan) => {
  drawerType.value = 'edit'
  resetForm()
  
  form.id = row.id
  form.name = row.name
  form.description = row.description
  form.assetType = row.assetType
  form.cycle = row.cycle
  form.cycleUnit = row.cycleUnit
  form.status = row.status
  form.startDate = row.startDate
  form.endDate = row.endDate
  form.notifyDays = row.notifyDays
  form.notifyRoles = [...row.notifyRoles]
  
  drawerVisible.value = true
}

// 删除计划
const handleDelete = (row: MaintenancePlan) => {
  ElMessageBox.confirm(
    `确定要删除维保计划"${row.name}"吗？关联的任务也将被删除。`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      // TODO: 调用删除API
      await new Promise(resolve => setTimeout(resolve, 300))
      ElMessage.success('删除成功')
      getList()
    } catch (error) {
      console.error('删除计划失败:', error)
      ElMessage.error('删除计划失败')
    }
  }).catch(() => {})
}

// 生成任务
const handleGenerateTask = (row: MaintenancePlan) => {
  ElMessageBox.confirm(
    `确定要为"${row.name}"生成维保任务吗？`,
    '确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      // TODO: 调用生成任务API
      await new Promise(resolve => setTimeout(resolve, 500))
      ElMessage.success('任务生成成功')
    } catch (error) {
      console.error('生成任务失败:', error)
      ElMessage.error('生成任务失败')
    }
  }).catch(() => {})
}

// 查看计划日历
const handleViewCalendar = (row: MaintenancePlan) => {
  ElMessage.info('计划日历功能开发中')
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  
  form.id = ''
  form.name = ''
  form.description = ''
  form.assetType = ''
  form.cycle = 30
  form.cycleUnit = 'day'
  form.status = 'active'
  form.startDate = ''
  form.endDate = ''
  form.notifyDays = 3
  form.notifyRoles = []
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        // TODO: 调用保存API
        await new Promise(resolve => setTimeout(resolve, 500))
        ElMessage.success(drawerType.value === 'add' ? '新增成功' : '编辑成功')
        drawerVisible.value = false
        getList()
      } catch (error) {
        console.error('保存计划失败:', error)
        ElMessage.error('保存计划失败')
      }
    }
  })
}

// 搜索
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置搜索
const handleReset = () => {
  queryParams.keyword = ''
  queryParams.status = ''
  queryParams.dateRange = []
  handleQuery()
}

// 格式化下次执行时间
const formatNextTime = (row: PlanAsset) => {
  if (!row.nextExecuteTime) return '-'
  
  const nextTime = new Date(row.nextExecuteTime).getTime()
  const now = Date.now()
  const diffDays = Math.floor((nextTime - now) / (24 * 60 * 60 * 1000))
  
  if (diffDays < 0) {
    return `<span class="text-danger">已逾期 ${Math.abs(diffDays)} 天</span>`
  } else if (diffDays === 0) {
    return `<span class="text-warning">今天</span>`
  } else if (diffDays < 7) {
    return `<span class="text-warning">${diffDays} 天后</span>`
  } else {
    return `<span>${diffDays} 天后</span>`
  }
}

// 页面加载时获取数据
onMounted(() => {
  getList()
})
</script>

<template>
  <div class="maintenance-plan-container">
    <!-- 搜索表单 -->
    <el-card class="search-card">
      <el-form :model="queryParams" ref="queryFormRef" :inline="true">
        <el-form-item label="关键词" prop="keyword">
          <el-input
            v-model="queryParams.keyword"
            placeholder="计划名称/编号"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择" clearable style="width: 200px">
            <el-option label="启用" value="active" />
            <el-option label="停用" value="inactive" />
            <el-option label="已完成" value="completed" />
            <el-option label="已过期" value="expired" />
          </el-select>
        </el-form-item>
        <el-form-item label="开始日期" prop="dateRange">
          <el-date-picker
            v-model="queryParams.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>维保计划列表</span>
          <el-button type="primary" icon="Plus" @click="handleAdd">新增维保计划</el-button>
        </div>
      </template>

      <!-- 表格 -->
      <el-table :data="tableData" v-loading="loading" border>
        <el-table-column prop="planNo" label="计划编号" width="120" />
        <el-table-column prop="name" label="计划名称" min-width="150" show-overflow-tooltip />
        <el-table-column label="资产类型" width="120">
          <template #default="{ row }">
            {{ assetTypeOptions.find(item => item.value === row.assetType)?.label }}
          </template>
        </el-table-column>
        <el-table-column label="计划周期" width="120">
          <template #default="{ row }">
            {{ row.cycle }} {{ cycleUnitOptions.find(item => item.value === row.cycleUnit)?.label }}
          </template>
        </el-table-column>
        <el-table-column prop="startDate" label="开始日期" width="120" />
        <el-table-column prop="endDate" label="结束日期" width="120" />
        <el-table-column label="资产数量" width="100" align="center">
          <template #default="{ row }">
            <el-button type="primary" link @click="getPlanAssets(row)">
              {{ row.assetCount }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="最近执行" width="150">
          <template #default="{ row }">
            {{ row.lastExecuteTime || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="下次执行" width="150">
          <template #default="{ row }">
            {{ row.nextExecuteTime || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="statusMap[row.status].type">
              {{ statusMap[row.status].text }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="240" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link icon="Edit" @click="handleEdit(row)">编辑</el-button>
            <el-button type="primary" link icon="Document" @click="handleGenerateTask(row)">生成任务</el-button>
            <el-button type="primary" link icon="Calendar" @click="handleViewCalendar(row)">查看日历</el-button>
            <el-button type="danger" link icon="Delete" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页器 -->
      <el-pagination
        v-model:current-page="queryParams.pageNum"
        v-model:page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        class="pagination"
      />
    </el-card>

    <!-- 新增/编辑抽屉 -->
    <el-drawer
      v-model="drawerVisible"
      :title="drawerTitle"
      direction="rtl"
      size="50%"
      destroy-on-close
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        class="plan-form"
      >
        <el-form-item label="计划名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入计划名称" />
        </el-form-item>

        <el-form-item label="资产类型" prop="assetType">
          <el-select v-model="form.assetType" placeholder="请选择资产类型" style="width: 100%">
            <el-option
              v-for="item in assetTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="计划周期" required>
          <el-input-number v-model="form.cycle" :min="1" :max="3650" style="width: 180px" />
          <el-select v-model="form.cycleUnit" style="width: 180px; margin-left: 20px">
            <el-option
              v-for="item in cycleUnitOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="有效期" required>
          <el-date-picker
            v-model="form.startDate"
            type="date"
            placeholder="开始日期"
            style="width: 180px"
          />
          <span style="margin: 0 10px;">至</span>
          <el-date-picker
            v-model="form.endDate"
            type="date"
            placeholder="结束日期"
            style="width: 180px"
          />
        </el-form-item>

        <el-form-item label="计划状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="active">启用</el-radio>
            <el-radio label="inactive">停用</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="提前通知" prop="notifyDays">
          <el-input-number v-model="form.notifyDays" :min="0" :max="30" />
          <span class="form-help">天（设为0则不提前通知）</span>
        </el-form-item>

        <el-form-item label="通知角色" prop="notifyRoles">
          <el-checkbox-group v-model="form.notifyRoles">
            <el-checkbox
              v-for="item in roleOptions"
              :key="item.value"
              :label="item.value"
            >{{ item.label }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="计划描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请输入计划描述"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitForm">保存</el-button>
          <el-button @click="drawerVisible = false">取消</el-button>
        </el-form-item>
      </el-form>
    </el-drawer>

    <!-- 资产列表对话框 -->
    <el-dialog
      v-model="assetSelectVisible"
      :title="`计划关联资产 - ${currentPlan?.name}`"
      width="900px"
    >
      <el-table :data="planAssets" v-loading="assetLoading" border>
        <el-table-column prop="assetCode" label="资产编号" width="120" />
        <el-table-column prop="assetName" label="资产名称" min-width="120" />
        <el-table-column prop="department" label="所属部门" width="120" />
        <el-table-column prop="location" label="位置" width="120" />
        <el-table-column label="上次执行" width="150">
          <template #default="{ row }">
            {{ row.lastExecuteTime || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="下次执行" width="120">
          <template #default="{ row }">
            <span v-html="formatNextTime(row)"></span>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag
              :type="row.status === 'overdue' ? 'danger' : (row.status === 'upcoming' ? 'warning' : 'success')"
            >
              {{ row.status === 'overdue' ? '已逾期' : (row.status === 'upcoming' ? '待执行' : '正常') }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.maintenance-plan-container {
  padding: 20px;
  
  .search-card {
    margin-bottom: 20px;
  }
  
  .table-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .pagination {
      margin-top: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
  
  .plan-form {
    padding: 20px;
    
    .form-help {
      color: #909399;
      font-size: 14px;
      margin-left: 10px;
    }
  }
  
  :deep(.text-danger) {
    color: #F56C6C;
  }
  
  :deep(.text-warning) {
    color: #E6A23C;
  }
}
</style> 