// 资产导入记录
export interface ImportRecord {
  id: string
  importTime: string
  importBy: string
  totalCount: number
  successCount: number
  failCount: number
  status: 'success' | 'partial' | 'failed'
  fileName: string
}

// 资产导入预览结果
export interface ImportPreviewResult {
  total: number
  headers: string[]
  data: ImportAssetItem[]
  errors: ImportError[]
}

// 导入的资产项
export interface ImportAssetItem {
  id?: string
  rowIndex: number
  [key: string]: any
  hasError?: boolean
  errors?: ImportItemError[]
}

// 导入项错误
export interface ImportItemError {
  field: string
  message: string
}

// 导入错误
export interface ImportError {
  rowIndex: number
  field: string
  message: string
}

// 资产导入结果
export interface ImportResult {
  total: number
  success: number
  fail: number
  errors: ImportError[]
  data: ImportAssetItem[]
}

// 资产状态相关类型定义
export interface AssetStatus {
  id: number
  name: string
  code: string
  description: string
  isDefault: boolean
  createBy: string
  createTime: string
  updateBy?: string
  updateTime?: string
}

// 资产状态统计
export interface AssetStatusStatistics {
  total: number
  inUse: number
  transferring: number
  idle: number
  scrapped: number
  pending: number
  maintenance: number
  other: number
}

// 资产状态变更记录
export interface AssetStatusChangeLog {
  id: number
  statusId: number
  statusName: string
  operationType: string
  operationDesc: string
  oldValue: string
  newValue: string
  operateBy: string
  operateTime: string
}

// 分页查询参数
export interface QueryParams {
  pageNum: number
  pageSize: number
  keyword?: string
  isDefault?: boolean
  [key: string]: any
}

// 分页结果
export interface PageResult<T> {
  rows: T[]
  total: number
}

// 资产基本信息
export interface Asset {
  id: string
  assetCode: string
  assetName: string
  assetType: {
    id: number
    name: string
  }
  model: string
  specification: string
  unitId: number
  unitName: string
  departmentId?: number
  departmentName?: string
  statusId: number
  statusName: string
  purchaseDate: string
  purchasePrice: number
  warrantyPeriod?: string
  manufacturer?: string
  supplier?: string
  location?: string
  responsiblePerson?: string
  serialNumber?: string
  useStatus: number
  useStatusName: string
  remark?: string
  createBy: string
  createTime: string
  updateBy?: string
  updateTime?: string
  // 资产附加属性（不同资产类型可能有不同的附加属性）
  attributes?: Record<string, any>
}

// 资产列表项（用于表格展示，包含部分信息）
export interface AssetListItem extends Omit<Asset, 'attributes'> {
  attributesSummary?: string // 附加属性摘要，用于列表展示
}

// 资产导出字段选项
export interface ExportField {
  label: string
  value: string
  checked: boolean
}

// 资产二维码信息
export interface AssetQrCode {
  id: string
  assetId: string
  assetCode: string
  assetName: string
  unitName: string
  responsiblePerson?: string
  qrCodeUrl: string
  createTime: string
}

// 打印模板类型
export type PrintTemplateType = 'A4' | 'thermal' | 'custom'

// 资产打印设置
export interface AssetPrintSetting {
  templateType: PrintTemplateType
  showFields: string[]
  labelWidth: number
  labelHeight: number
  columns: number
  rows: number
  customCss?: string
} 