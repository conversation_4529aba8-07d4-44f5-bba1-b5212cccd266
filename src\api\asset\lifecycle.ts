import request from '@/config/axios'
import { PageResult } from '@/types/asset'
import {
  AcceptanceForm,
  AcceptanceProcess,
  ApprovalDetail,
  TransferForm,
  TransferProcess,
  IdleForm,
  IdleProcess,
  ReturnForm,
  ReturnProcess,
  ScrapForm,
  ScrapProcess,
  DisposalForm,
  DisposalRecord,
  ChangeForm,
  ChangeProcess,
  ChangeLog,
  LifecycleQueryParams
} from '@/types/asset/lifecycle'

// 接口前缀
const API_PREFIX = '/asset/lifecycle'

// ========== 资产验收 ==========
export const createAcceptance = (data: AcceptanceForm) => {
  return request.post<string>(`${API_PREFIX}/acceptance/create`, data)
}

export const getAcceptanceList = (params: LifecycleQueryParams) => {
  return request.get<PageResult<AcceptanceProcess>>(`${API_PREFIX}/acceptance/list`, { params })
}

export const getAcceptanceDetail = (id: string) => {
  return request.get<ApprovalDetail>(`${API_PREFIX}/acceptance/detail/${id}`)
}

// ========== 资产调拨 ==========
export const createTransfer = (data: TransferForm) => {
  return request.post<string>(`${API_PREFIX}/transfer/create`, data)
}

export const getTransferList = (params: LifecycleQueryParams) => {
  return request.get<PageResult<TransferProcess>>(`${API_PREFIX}/transfer/list`, { params })
}

export const getTransferDetail = (id: string) => {
  return request.get<ApprovalDetail>(`${API_PREFIX}/transfer/detail/${id}`)
}

// ========== 资产闲置与回转 ==========
export const createIdle = (data: IdleForm) => {
  return request.post<string>(`${API_PREFIX}/idle/create`, data)
}

export const getIdleList = (params: LifecycleQueryParams) => {
  return request.get<PageResult<IdleProcess>>(`${API_PREFIX}/idle/list`, { params })
}

export const getIdleDetail = (id: string) => {
  return request.get<ApprovalDetail>(`${API_PREFIX}/idle/detail/${id}`)
}

export const createReturn = (data: ReturnForm) => {
  return request.post<string>(`${API_PREFIX}/return/create`, data)
}

export const getReturnList = (params: LifecycleQueryParams) => {
  return request.get<PageResult<ReturnProcess>>(`${API_PREFIX}/return/list`, { params })
}

export const getReturnDetail = (id: string) => {
  return request.get<ApprovalDetail>(`${API_PREFIX}/return/detail/${id}`)
}

// ========== 资产报废与处置 ==========
export const createScrap = (data: ScrapForm) => {
  return request.post<string>(`${API_PREFIX}/scrap/create`, data)
}

export const getScrapList = (params: LifecycleQueryParams) => {
  return request.get<PageResult<ScrapProcess>>(`${API_PREFIX}/scrap/list`, { params })
}

export const getScrapDetail = (id: string) => {
  return request.get<ApprovalDetail>(`${API_PREFIX}/scrap/detail/${id}`)
}

export const createDisposal = (data: DisposalForm) => {
  return request.post<string>(`${API_PREFIX}/disposal/create`, data)
}

export const getDisposalList = (params: LifecycleQueryParams) => {
  return request.get<PageResult<DisposalRecord>>(`${API_PREFIX}/disposal/list`, { params })
}

export const getDisposalDetail = (id: string) => {
  return request.get<DisposalRecord>(`${API_PREFIX}/disposal/detail/${id}`)
}

// ========== 资产变更 ==========
export const createChange = (data: ChangeForm) => {
  return request.post<string>(`${API_PREFIX}/change/create`, data)
}

export const getChangeList = (params: LifecycleQueryParams) => {
  return request.get<PageResult<ChangeProcess>>(`${API_PREFIX}/change/list`, { params })
}

export const getChangeDetail = (id: string) => {
  return request.get<ApprovalDetail>(`${API_PREFIX}/change/detail/${id}`)
}

export const getChangeLogList = (params: LifecycleQueryParams) => {
  return request.get<PageResult<ChangeLog>>(`${API_PREFIX}/change/log/list`, { params })
}

// ========== 流程通用操作 ==========
export const approveProcess = (processId: string, nodeId: string, opinion: string) => {
  return request.post(`${API_PREFIX}/approval/approve`, { processId, nodeId, opinion })
}

export const rejectProcess = (processId: string, nodeId: string, opinion: string) => {
  return request.post(`${API_PREFIX}/approval/reject`, { processId, nodeId, opinion })
}

export const cancelProcess = (processId: string, reason: string) => {
  return request.post(`${API_PREFIX}/cancel`, { processId, reason })
}

export const getMyApprovalList = (params: LifecycleQueryParams) => {
  return request.get<PageResult<any>>(`${API_PREFIX}/approval/my`, { params })
}

export const getMyProcessList = (params: LifecycleQueryParams) => {
  return request.get<PageResult<any>>(`${API_PREFIX}/my`, { params })
} 