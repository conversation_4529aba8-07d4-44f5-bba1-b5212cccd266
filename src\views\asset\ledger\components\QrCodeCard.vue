<template>
  <div class="qr-code-card" :class="{ 'preview-mode': isPreview }" ref="cardRef">
    <div class="card-header">
      <h2 class="card-title">{{ assetData.assetName }}</h2>
      <div class="card-company">水投集团</div>
    </div>
    <div class="card-content">
      <div class="qr-code-wrapper">
        <img :src="assetData.qrCodeUrl" alt="资产二维码" class="qr-code-image" />
      </div>
      <div class="asset-info">
        <div class="info-item">
          <span class="label">资产编号：</span>
          <span class="value">{{ assetData.assetCode }}</span>
        </div>
        <div class="info-item">
          <span class="label">使用单位：</span>
          <span class="value">{{ assetData.unitName }}</span>
        </div>
        <div class="info-item" v-if="assetData.responsiblePerson">
          <span class="label">责任人：</span>
          <span class="value">{{ assetData.responsiblePerson }}</span>
        </div>
        <div class="info-item" v-if="showMoreInfo && (assetData as any).purchaseDate">
          <span class="label">采购日期：</span>
          <span class="value">{{ (assetData as any).purchaseDate }}</span>
        </div>
        <div class="info-item" v-if="showMoreInfo && (assetData as any).model">
          <span class="label">型号规格：</span>
          <span class="value">{{ (assetData as any).model }} {{ (assetData as any).specification }}</span>
        </div>
      </div>
    </div>
    <div class="card-footer">
      <span>扫码查看资产详情</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineExpose, defineProps } from 'vue'
import type { AssetQrCode } from '@/types/asset'

// 定义属性
const props = defineProps<{
  assetData: AssetQrCode
  isPreview?: boolean
  showMoreInfo?: boolean
  templateType?: 'A4' | 'thermal' | 'custom'
}>()

// 卡片元素引用
const cardRef = ref<HTMLElement | null>(null)

// 打印卡片方法
const printCard = () => {
  if (!cardRef.value) return
  
  // 创建打印窗口
  const printWindow = window.open('', '_blank')
  if (!printWindow) {
    alert('请允许打开弹窗以打印标签')
    return
  }
  
  // 构建打印内容
  printWindow.document.write(`
    <html>
      <head>
        <title>资产标签 - ${props.assetData.assetCode}</title>
        <style>
          @media print {
            body {
              margin: 0;
              padding: 0;
            }
            .print-container {
              width: ${props.templateType === 'thermal' ? '80mm' : '100%'};
              padding: 0;
              box-sizing: border-box;
            }
          }
          
          ${cardStyle.value}
        </style>
      </head>
      <body>
        <div class="print-container">
          ${cardRef.value.outerHTML}
        </div>
        <script>
          window.onload = function() {
            window.print();
            setTimeout(function() { window.close(); }, 100);
          };
        <\/script>
      <\/body>
    <\/html>
  `)
  
  printWindow.document.close()
}

// 卡片样式
const cardStyle = computed(() => {
  const baseStyle = `
    .qr-code-card {
      border: 1px solid #ddd;
      border-radius: 8px;
      padding: 12px;
      width: ${props.templateType === 'thermal' ? '76mm' : '85mm'};
      background-color: white;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      box-sizing: border-box;
      font-family: Arial, sans-serif;
    }
    
    .card-header {
      border-bottom: 1px solid #eee;
      padding-bottom: 8px;
      margin-bottom: 10px;
      text-align: center;
    }
    
    .card-title {
      margin: 0;
      font-size: 16px;
      font-weight: bold;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    
    .card-company {
      font-size: 12px;
      color: #666;
      margin-top: 2px;
    }
    
    .card-content {
      display: flex;
      align-items: center;
    }
    
    .qr-code-wrapper {
      flex: 0 0 auto;
      margin-right: 10px;
    }
    
    .qr-code-image {
      width: ${props.templateType === 'thermal' ? '90px' : '100px'};
      height: ${props.templateType === 'thermal' ? '90px' : '100px'};
    }
    
    .asset-info {
      flex: 1;
      font-size: 12px;
    }
    
    .info-item {
      margin-bottom: 3px;
    }
    
    .label {
      color: #666;
    }
    
    .value {
      font-weight: bold;
      word-break: break-all;
    }
    
    .card-footer {
      text-align: center;
      margin-top: 10px;
      font-size: 10px;
      color: #999;
    }
    
    .preview-mode {
      display: inline-block;
      margin: 10px;
    }
  `
  
  return baseStyle
})

// 暴露方法
defineExpose({
  printCard
})
</script>

<script lang="ts">
export default {
  name: 'QrCodeCard'
}
</script>

<style lang="scss" scoped>
.qr-code-card {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 12px;
  width: v-bind("templateType === 'thermal' ? '76mm' : '85mm'");
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
  
  &.preview-mode {
    display: inline-block;
    margin: 10px;
  }
}

.card-header {
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
  margin-bottom: 10px;
  text-align: center;
  
  .card-title {
    margin: 0;
    font-size: 16px;
    font-weight: bold;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .card-company {
    font-size: 12px;
    color: #666;
    margin-top: 2px;
  }
}

.card-content {
  display: flex;
  align-items: center;
  
  .qr-code-wrapper {
    flex: 0 0 auto;
    margin-right: 10px;
    
    .qr-code-image {
      width: v-bind("templateType === 'thermal' ? '90px' : '100px'");
      height: v-bind("templateType === 'thermal' ? '90px' : '100px'");
    }
  }
  
  .asset-info {
    flex: 1;
    font-size: 12px;
    
    .info-item {
      margin-bottom: 3px;
      
      .label {
        color: #666;
      }
      
      .value {
        font-weight: bold;
        word-break: break-all;
      }
    }
  }
}

.card-footer {
  text-align: center;
  margin-top: 10px;
  font-size: 10px;
  color: #999;
}
</style> 