import { Mock<PERSON>ethod } from 'vite-plugin-mock'
import { Random } from 'mockjs'

const approvalModeOptions = [
  { value: '1', label: '串行' },
  { value: '2', label: '并行' },
  { value: '3', label: '条件' }
]

const statusOptions = [
  { value: '1', label: '启用' },
  { value: '0', label: '禁用' }
]

// 生成随机数据
const generateData = () => {
  const data = []
  for (let i = 1; i <= 25; i++) {
    data.push({
      id: i,
      name: Random.ctitle(3, 6) + '申请',
      code: 'AT' + Random.string('number', 4),
      approvalMode: Random.pick(['1', '2', '3']),
      description: Random.cparagraph(1, 3),
      status: Random.pick(['0', '1']),
      createTime: Random.datetime('yyyy-MM-dd HH:mm:ss')
    })
  }
  return data
}

const applyTypeList = generateData()

export default [
  {
    url: '/api/asset/apply-type/list',
    method: 'get',
    response: (config: any) => {
      const { name, pageNum = 1, pageSize = 10 } = config.query
      let filteredList = [...applyTypeList]
      
      if (name) {
        filteredList = filteredList.filter(item => item.name.includes(name))
      }
      
      const total = filteredList.length
      const start = (pageNum - 1) * pageSize
      const end = start + pageSize
      const rows = filteredList.slice(start, end)
      
      return {
        code: 200,
        msg: 'success',
        total,
        rows
      }
    }
  },
  {
    url: '/api/asset/apply-type/:id',
    method: 'get',
    response: (config: any) => {
      const id = parseInt(config.query.id)
      const data = applyTypeList.find(item => item.id === id)
      return {
        code: 200,
        msg: 'success',
        data
      }
    }
  },
  {
    url: '/api/asset/apply-type',
    method: 'post',
    response: (config: any) => {
      const data = config.body
      data.id = applyTypeList.length + 1
      data.createTime = new Date().toISOString()
      applyTypeList.push(data)
      return {
        code: 200,
        msg: 'success'
      }
    }
  },
  {
    url: '/api/asset/apply-type',
    method: 'put',
    response: (config: any) => {
      const data = config.body
      const index = applyTypeList.findIndex(item => item.id === data.id)
      if (index !== -1) {
        applyTypeList[index] = { ...applyTypeList[index], ...data }
      }
      return {
        code: 200,
        msg: 'success'
      }
    }
  },
  {
    url: '/api/asset/apply-type/:id',
    method: 'delete',
    response: (config: any) => {
      const id = parseInt(config.query.id)
      const index = applyTypeList.findIndex(item => item.id === id)
      if (index !== -1) {
        applyTypeList.splice(index, 1)
      }
      return {
        code: 200,
        msg: 'success'
      }
    }
  }
] as MockMethod[] 