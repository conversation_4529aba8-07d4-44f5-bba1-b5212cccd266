// 模拟数据生成工具 - 使用单位
import { reactive } from 'vue'

// 单位类型定义
export interface Unit {
  id: number
  name: string
  code: string
  parentId: number | null
  level: number
  sort: number
  description: string
  createBy: string
  createTime: string
  hasChildren?: boolean
  children?: Unit[]
}

// 模拟数据 - 使用单位列表
export const mockUnitData: Unit[] = reactive([
  {
    id: 1,
    name: '水投集团',
    code: 'WATER-GROUP',
    parentId: null,
    level: 1,
    sort: 1,
    description: '水务投资集团总部',
    createBy: '管理员',
    createTime: '2023-05-15 10:00:00',
    hasChildren: true,
    children: [
      {
        id: 2,
        name: '第一水厂',
        code: 'WATER-PLANT-01',
        parentId: 1,
        level: 2,
        sort: 1,
        description: '负责城区第一水厂的自来水生产和供应',
        createBy: '管理员',
        createTime: '2023-05-15 10:05:00',
        hasChildren: true,
        children: [
          {
            id: 6,
            name: '生产部',
            code: 'WP01-PROD',
            parentId: 2,
            level: 3,
            sort: 1,
            description: '负责水厂日常生产运营',
            createBy: '管理员',
            createTime: '2023-05-15 10:15:00',
            hasChildren: false
          },
          {
            id: 7,
            name: '设备维护部',
            code: 'WP01-MAINT',
            parentId: 2,
            level: 3,
            sort: 2,
            description: '负责水厂设备维护和检修',
            createBy: '管理员',
            createTime: '2023-05-15 10:16:00',
            hasChildren: false
          },
          {
            id: 8,
            name: '水质检测部',
            code: 'WP01-QUALITY',
            parentId: 2,
            level: 3,
            sort: 3,
            description: '负责水厂水质监测和分析',
            createBy: '管理员',
            createTime: '2023-05-15 10:17:00',
            hasChildren: false
          }
        ]
      },
      {
        id: 3,
        name: '第二水厂',
        code: 'WATER-PLANT-02',
        parentId: 1,
        level: 2,
        sort: 2,
        description: '负责城区第二水厂的自来水生产和供应',
        createBy: '管理员',
        createTime: '2023-05-15 10:06:00',
        hasChildren: true,
        children: [
          {
            id: 9,
            name: '生产部',
            code: 'WP02-PROD',
            parentId: 3,
            level: 3,
            sort: 1,
            description: '负责水厂日常生产运营',
            createBy: '管理员',
            createTime: '2023-05-15 10:18:00',
            hasChildren: false
          },
          {
            id: 10,
            name: '设备维护部',
            code: 'WP02-MAINT',
            parentId: 3,
            level: 3,
            sort: 2,
            description: '负责水厂设备维护和检修',
            createBy: '管理员',
            createTime: '2023-05-15 10:19:00',
            hasChildren: false
          }
        ]
      },
      {
        id: 4,
        name: '污水处理厂',
        code: 'SEWAGE-PLANT',
        parentId: 1,
        level: 2,
        sort: 3,
        description: '负责城区污水处理',
        createBy: '管理员',
        createTime: '2023-05-15 10:07:00',
        hasChildren: true,
        children: [
          {
            id: 11,
            name: '运营部',
            code: 'SP-OPERATION',
            parentId: 4,
            level: 3,
            sort: 1,
            description: '负责污水处理厂日常运营',
            createBy: '管理员',
            createTime: '2023-05-15 10:20:00',
            hasChildren: false
          },
          {
            id: 12,
            name: '技术部',
            code: 'SP-TECH',
            parentId: 4,
            level: 3,
            sort: 2,
            description: '负责污水处理工艺技术支持',
            createBy: '管理员',
            createTime: '2023-05-15 10:21:00',
            hasChildren: false
          }
        ]
      },
      {
        id: 5,
        name: '管网运营中心',
        code: 'PIPE-NETWORK',
        parentId: 1,
        level: 2,
        sort: 4,
        description: '负责供水管网运营维护',
        createBy: '管理员',
        createTime: '2023-05-15 10:08:00',
        hasChildren: true,
        children: [
          {
            id: 13,
            name: '管网巡检部',
            code: 'PN-INSPECT',
            parentId: 5,
            level: 3,
            sort: 1,
            description: '负责管网巡检和漏水检测',
            createBy: '管理员',
            createTime: '2023-05-15 10:22:00',
            hasChildren: false
          },
          {
            id: 14,
            name: '管网抢修部',
            code: 'PN-REPAIR',
            parentId: 5,
            level: 3,
            sort: 2,
            description: '负责管网抢修和维护',
            createBy: '管理员',
            createTime: '2023-05-15 10:23:00',
            hasChildren: false
          },
          {
            id: 15,
            name: '泵站管理部',
            code: 'PN-PUMP',
            parentId: 5,
            level: 3,
            sort: 3,
            description: '负责泵站设备管理和维护',
            createBy: '管理员',
            createTime: '2023-05-15 10:24:00',
            hasChildren: false
          }
        ]
      }
    ]
  }
])

// 模拟API - 获取单位列表（扁平结构）
export const mockGetUnitList = async (params: any) => {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 300))
  
  // 将树形结构转为扁平结构
  const flattenUnits: Unit[] = []
  
  function flatten(units: Unit[], parentName: string = '') {
    units.forEach(unit => {
      const flatUnit = { ...unit }
      if (parentName) {
        flatUnit.parentName = parentName
      }
      
      const { children, ...rest } = flatUnit
      flattenUnits.push(rest)
      
      if (children && children.length > 0) {
        flatten(children, unit.name)
      }
    })
  }
  
  flatten(mockUnitData)
  
  // 筛选
  let result = [...flattenUnits]
  if (params.name) {
    result = result.filter(unit => unit.name.includes(params.name))
  }
  if (params.code) {
    result = result.filter(unit => unit.code.includes(params.code))
  }
  
  // 计算总数
  const total = result.length
  
  // 分页
  const start = (params.pageNum - 1) * params.pageSize
  const end = start + params.pageSize
  const rows = result.slice(start, end)
  
  return {
    rows,
    total
  }
}

// 模拟API - 获取单位树
export const mockGetUnitTree = async () => {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 300))
  
  return mockUnitData
}

// 模拟API - 新增单位
export const mockAddUnit = async (data: any) => {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 300))
  
  // 找出当前最大ID
  let maxId = 0
  function findMaxId(units: Unit[]) {
    units.forEach(unit => {
      if (unit.id > maxId) maxId = unit.id
      if (unit.children && unit.children.length > 0) {
        findMaxId(unit.children)
      }
    })
  }
  
  findMaxId(mockUnitData)
  
  // 创建新单位
  const newUnit: Unit = {
    id: maxId + 1,
    name: data.name,
    code: data.code,
    parentId: data.parentId,
    level: data.level || 1,
    sort: data.sort || 999,
    description: data.description || '',
    createBy: '当前用户',
    createTime: new Date().toISOString(),
    hasChildren: false,
    children: []
  }
  
  // 如果有父级，添加到对应的children中
  if (data.parentId) {
    function addToParent(units: Unit[]) {
      for (const unit of units) {
        if (unit.id === data.parentId) {
          if (!unit.children) unit.children = []
          unit.hasChildren = true
          unit.children.push(newUnit)
          return true
        }
        
        if (unit.children && unit.children.length > 0) {
          if (addToParent(unit.children)) return true
        }
      }
      return false
    }
    
    const added = addToParent(mockUnitData)
    if (!added) {
      throw new Error('父级单位不存在')
    }
  } else {
    // 否则添加到根级别
    mockUnitData.push(newUnit)
  }
  
  return newUnit
}

// 模拟API - 修改单位
export const mockUpdateUnit = async (data: any) => {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 300))
  
  // 查找并更新单位
  function updateUnit(units: Unit[]): boolean {
    for (let i = 0; i < units.length; i++) {
      if (units[i].id === data.id) {
        // 更新单位信息
        units[i].name = data.name
        units[i].code = data.code
        units[i].description = data.description
        units[i].sort = data.sort
        return true
      }
      
      if (units[i].children && units[i].children.length > 0) {
        if (updateUnit(units[i].children)) return true
      }
    }
    return false
  }
  
  const updated = updateUnit(mockUnitData)
  if (!updated) {
    throw new Error('单位不存在')
  }
  
  return data
}

// 模拟API - 删除单位
export const mockDeleteUnit = async (id: number) => {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 300))
  
  // 查找并删除单位
  function deleteUnit(units: Unit[], parentArray?: Unit[]): boolean {
    for (let i = 0; i < units.length; i++) {
      if (units[i].id === id) {
        // 检查是否有子节点
        if (units[i].children && units[i].children.length > 0) {
          throw new Error('该单位下有子单位，无法删除')
        }
        
        // 从数组中移除
        units.splice(i, 1)
        
        // 检查父节点是否还有子节点
        if (parentArray && parentArray.length === 0) {
          const parent = parentArray[0]
          parent.hasChildren = false
        }
        
        return true
      }
      
      if (units[i].children && units[i].children.length > 0) {
        if (deleteUnit(units[i].children, [units[i]])) return true
      }
    }
    return false
  }
  
  const deleted = deleteUnit(mockUnitData)
  if (!deleted) {
    throw new Error('单位不存在')
  }
  
  return { success: true }
}

// 模拟API - 获取单位详情
export const mockGetUnitDetail = async (id: number) => {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 300))
  
  // 查找单位
  function findUnit(units: Unit[]): Unit | null {
    for (const unit of units) {
      if (unit.id === id) {
        return unit
      }
      
      if (unit.children && unit.children.length > 0) {
        const found = findUnit(unit.children)
        if (found) return found
      }
    }
    return null
  }
  
  const unit = findUnit(mockUnitData)
  if (!unit) {
    throw new Error('单位不存在')
  }
  
  return unit
} 