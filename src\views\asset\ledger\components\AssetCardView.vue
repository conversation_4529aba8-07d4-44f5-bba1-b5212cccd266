<template>
  <div class="asset-card-view">
    <el-row :gutter="16">
      <el-col
        v-for="asset in assets"
        :key="asset.id"
        :xs="24"
        :sm="12"
        :md="8"
        :lg="6"
        :xl="4"
        class="card-col"
      >
        <el-card
          shadow="hover"
          class="asset-card"
          :body-style="{ padding: '0' }"
        >
          <!-- 卡片头部 - 资产类型和状态 -->
          <div class="card-header">
            <el-tag size="small" type="info">{{ asset.assetType.name }}</el-tag>
            <el-tag
              size="small"
              :type="getStatusTagType(asset.statusId)"
            >
              {{ asset.statusName }}
            </el-tag>
          </div>
          
          <!-- 卡片内容 -->
          <div class="card-content">
            <div class="asset-title">
              <span class="asset-name" :title="asset.assetName">{{ asset.assetName }}</span>
            </div>
            
            <div class="asset-info">
              <div class="info-item">
                <span class="label">资产编号：</span>
                <span class="value">{{ asset.assetCode }}</span>
              </div>
              <div class="info-item">
                <span class="label">型号规格：</span>
                <span class="value">{{ asset.model }} {{ asset.specification }}</span>
              </div>
              <div class="info-item">
                <span class="label">单位部门：</span>
                <span class="value">{{ asset.unitName }} - {{ asset.departmentName }}</span>
              </div>
              <div class="info-item">
                <span class="label">采购日期：</span>
                <span class="value">{{ asset.purchaseDate }}</span>
              </div>
              <div class="info-item" v-if="asset.responsiblePerson">
                <span class="label">负责人：</span>
                <span class="value">{{ asset.responsiblePerson }}</span>
              </div>
              <div class="info-item" v-if="asset.attributesSummary">
                <span class="label">附加信息：</span>
                <el-tooltip
                  :content="asset.attributesSummary"
                  placement="top"
                  :show-after="200"
                >
                  <span class="value attribute-summary">{{ asset.attributesSummary }}</span>
                </el-tooltip>
              </div>
            </div>
          </div>
          
          <!-- 卡片底部 - 操作按钮 -->
          <div class="card-footer">
            <el-button
              type="primary"
              size="small"
              text
              @click="handleView(asset)"
            >详情</el-button>
            <el-button
              type="primary"
              size="small"
              text
              @click="handleEdit(asset)"
            >编辑</el-button>
            <el-button
              type="danger"
              size="small"
              text
              @click="handleDelete(asset)"
            >删除</el-button>
            <el-dropdown trigger="click" @command="command => handleCommand(command, asset)">
              <el-button type="primary" size="small" text>
                更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="qrcode">生成二维码</el-dropdown-item>
                  <el-dropdown-item command="print">打印标签</el-dropdown-item>
                  <el-dropdown-item command="transfer" divided>资产调拨</el-dropdown-item>
                  <el-dropdown-item command="repair">报修</el-dropdown-item>
                  <el-dropdown-item command="scrap">报废</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 空数据提示 -->
    <el-empty v-if="assets.length === 0" description="暂无资产数据" />
  </div>
</template>

<script setup lang="ts">
import { ArrowDown } from '@element-plus/icons-vue'
import type { AssetListItem } from '@/types/asset'
import { getStatusTagType } from '../utils'

// 定义属性
const props = defineProps<{
  assets: AssetListItem[]
}>()

// 定义事件
const emit = defineEmits([
  'view',
  'edit',
  'delete',
  'qrcode',
  'print',
  'transfer',
  'repair',
  'scrap'
])

// 处理查看详情
const handleView = (asset: AssetListItem) => {
  emit('view', asset)
}

// 处理编辑
const handleEdit = (asset: AssetListItem) => {
  emit('edit', asset)
}

// 处理删除
const handleDelete = (asset: AssetListItem) => {
  emit('delete', asset)
}

// 处理下拉菜单命令
const handleCommand = (command: string, asset: AssetListItem) => {
  switch(command) {
    case 'qrcode':
      emit('qrcode', asset)
      break
    case 'print':
      emit('print', asset)
      break
    case 'transfer':
      emit('transfer', asset)
      break
    case 'repair':
      emit('repair', asset)
      break
    case 'scrap':
      emit('scrap', asset)
      break
  }
}
</script>

<style lang="scss" scoped>
.asset-card-view {
  width: 100%;
  
  .card-col {
    margin-bottom: 16px;
  }
  
  .asset-card {
    height: 100%;
    transition: all 0.3s;
    
    &:hover {
      transform: translateY(-5px);
    }
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 12px;
      border-bottom: 1px solid #f0f0f0;
    }
    
    .card-content {
      padding: 12px;
      
      .asset-title {
        margin-bottom: 10px;
        
        .asset-name {
          font-size: 16px;
          font-weight: bold;
          color: #333;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          display: block;
        }
      }
      
      .asset-info {
        font-size: 13px;
        
        .info-item {
          margin-bottom: 5px;
          display: flex;
          
          .label {
            color: #909399;
            min-width: 70px;
            flex-shrink: 0;
          }
          
          .value {
            flex: 1;
            color: #606266;
            word-break: break-all;
            
            &.attribute-summary {
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              cursor: help;
            }
          }
        }
      }
    }
    
    .card-footer {
      display: flex;
      justify-content: space-around;
      align-items: center;
      padding: 8px 12px;
      border-top: 1px solid #f0f0f0;
      background-color: #fafafa;
    }
  }
}
</style> 