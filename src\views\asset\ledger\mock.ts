// 模拟数据生成工具 - 资产台账
import { reactive } from 'vue'
import type { Asset, AssetListItem, PageResult, QueryParams, ExportField, AssetQrCode } from '@/types/asset'

// 生成随机资产编号
const generateAssetCode = (prefix: string = 'ZC') => {
  const timestamp = Date.now().toString().slice(-8)
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
  return `${prefix}${timestamp}${random}`
}

// 生成随机日期（过去3年内）
const generateRandomDate = () => {
  const now = new Date()
  const threeYearsAgo = new Date()
  threeYearsAgo.setFullYear(now.getFullYear() - 3)
  
  const randomTimestamp = threeYearsAgo.getTime() + Math.random() * (now.getTime() - threeYearsAgo.getTime())
  const date = new Date(randomTimestamp)
  
  return date.toISOString().split('T')[0]
}

// 生成随机价格
const generateRandomPrice = (min: number = 1000, max: number = 100000) => {
  return parseFloat((Math.random() * (max - min) + min).toFixed(2))
}

// 资产类型列表
const assetTypes = [
  { id: 1, name: '水泵设备' },
  { id: 2, name: '电气设备' },
  { id: 3, name: '检测设备' },
  { id: 4, name: '管网设备' },
  { id: 5, name: '办公设备' },
  { id: 6, name: '车辆' }
]

// 单位列表
const units = [
  { id: 1, name: '水投集团' },
  { id: 2, name: '第一水厂' },
  { id: 3, name: '第二水厂' },
  { id: 4, name: '污水处理厂' },
  { id: 5, name: '管网运营中心' }
]

// 部门列表
const departments = [
  { id: 6, name: '生产部', unitId: 2 },
  { id: 7, name: '设备维护部', unitId: 2 },
  { id: 8, name: '水质检测部', unitId: 2 },
  { id: 9, name: '生产部', unitId: 3 },
  { id: 10, name: '设备维护部', unitId: 3 },
  { id: 11, name: '运营部', unitId: 4 },
  { id: 12, name: '技术部', unitId: 4 },
  { id: 13, name: '管网巡检部', unitId: 5 },
  { id: 14, name: '管网抢修部', unitId: 5 },
  { id: 15, name: '泵站管理部', unitId: 5 }
]

// 状态列表
const statuses = [
  { id: 1, name: '在用' },
  { id: 2, name: '闲置' },
  { id: 3, name: '维修中' },
  { id: 4, name: '调拨中' },
  { id: 5, name: '报废' }
]

// 供应商列表
const suppliers = [
  '科技水务设备有限公司',
  '德能机械有限公司',
  '清源水处理设备有限公司',
  '鑫源电气有限公司',
  '联创科技有限公司',
  '华为技术有限公司',
  '戴尔科技有限公司',
  '联想集团有限公司'
]

// 生成资产列表
const generateAssets = (count: number): Asset[] => {
  const assets: Asset[] = []
  
  for (let i = 0; i < count; i++) {
    const assetTypeIndex = Math.floor(Math.random() * assetTypes.length)
    const assetType = assetTypes[assetTypeIndex]
    
    const unitIndex = Math.floor(Math.random() * units.length)
    const unit = units[unitIndex]
    
    // 根据单位筛选部门
    const filteredDepartments = departments.filter(dept => dept.unitId === unit.id)
    let department
    
    if (filteredDepartments.length > 0) {
      const departmentIndex = Math.floor(Math.random() * filteredDepartments.length)
      department = filteredDepartments[departmentIndex]
    } else {
      // 如果没有匹配的部门，使用默认部门或创建一个虚拟部门
      department = {
        id: 0,
        name: '未分配部门',
        unitId: unit.id
      }
    }
    
    const statusIndex = Math.floor(Math.random() * statuses.length)
    const status = statuses[statusIndex]
    
    const supplierIndex = Math.floor(Math.random() * suppliers.length)
    const supplier = suppliers[supplierIndex]
    
    // 根据资产类型决定一些属性
    let model = ''
    let specification = ''
    let serialNumber = ''
    let attributes: Record<string, any> = {}
    
    switch (assetType.id) {
      case 1: // 水泵设备
        model = `WP-${Math.floor(Math.random() * 900) + 100}`
        specification = `${Math.floor(Math.random() * 50) + 10}m³/h`
        serialNumber = `SB${Math.floor(Math.random() * 90000) + 10000}`
        attributes = {
          power: `${Math.floor(Math.random() * 75) + 5}kW`,
          pumpType: ['离心泵', '轴流泵', '混流泵', '自吸泵'][Math.floor(Math.random() * 4)],
          material: ['铸铁', '不锈钢', '铸钢'][Math.floor(Math.random() * 3)],
          installationPosition: `${unit.name}-${Math.floor(Math.random() * 5) + 1}号泵房`
        }
        break
      case 2: // 电气设备
        model = `EL-${Math.floor(Math.random() * 900) + 100}`
        specification = `${Math.floor(Math.random() * 400) + 100}V`
        serialNumber = `DQ${Math.floor(Math.random() * 90000) + 10000}`
        attributes = {
          voltage: `${[220, 380, 400, 660][Math.floor(Math.random() * 4)]}V`,
          current: `${Math.floor(Math.random() * 100) + 10}A`,
          protectionLevel: `IP${Math.floor(Math.random() * 10) + 55}`,
          controlType: ['PLC', '变频', '手动', '远程'][Math.floor(Math.random() * 4)]
        }
        break
      case 3: // 检测设备
        model = `DT-${Math.floor(Math.random() * 900) + 100}`
        specification = `精度${(Math.random() * 0.1).toFixed(3)}`
        serialNumber = `JC${Math.floor(Math.random() * 90000) + 10000}`
        attributes = {
          range: `${Math.floor(Math.random() * 10) + 1}-${Math.floor(Math.random() * 100) + 50}`,
          accuracy: `±${(Math.random() * 0.5).toFixed(2)}%`,
          parameter: ['pH', '浊度', '余氯', '电导率', '溶解氧'][Math.floor(Math.random() * 5)],
          calibrationDate: generateRandomDate()
        }
        break
      case 4: // 管网设备
        model = `PN-${Math.floor(Math.random() * 900) + 100}`
        specification = `DN${[100, 200, 300, 400, 500, 600][Math.floor(Math.random() * 6)]}`
        serialNumber = `GW${Math.floor(Math.random() * 90000) + 10000}`
        attributes = {
          material: ['球墨铸铁', '不锈钢', 'PE', 'PVC'][Math.floor(Math.random() * 4)],
          pressure: `${Math.floor(Math.random() * 10) + 1}MPa`,
          connectionType: ['法兰', '承插', '焊接', '螺纹'][Math.floor(Math.random() * 4)],
          installationDate: generateRandomDate()
        }
        break
      case 5: // 办公设备
        model = `OE-${Math.floor(Math.random() * 900) + 100}`
        specification = ['标准配置', '高性能配置', '入门配置'][Math.floor(Math.random() * 3)]
        serialNumber = `BG${Math.floor(Math.random() * 90000) + 10000}`
        if (Math.random() > 0.5) {
          // 计算机设备
          attributes = {
            cpu: ['Intel i5', 'Intel i7', 'AMD Ryzen 5', 'AMD Ryzen 7'][Math.floor(Math.random() * 4)],
            memory: `${[8, 16, 32][Math.floor(Math.random() * 3)]}GB`,
            storage: `${[256, 512, 1024][Math.floor(Math.random() * 3)]}GB SSD`,
            operatingSystem: ['Windows 10', 'Windows 11'][Math.floor(Math.random() * 2)]
          }
        } else {
          // 打印机等其他办公设备
          attributes = {
            type: ['激光', '喷墨', '针式'][Math.floor(Math.random() * 3)],
            printSpeed: `${Math.floor(Math.random() * 30) + 10}页/分钟`,
            supportNetwork: Math.random() > 0.5 ? '是' : '否',
            paperSize: ['A4', 'A3', 'A4/A3'][Math.floor(Math.random() * 3)]
          }
        }
        break
      case 6: // 车辆
        model = `${['比亚迪', '特斯拉', '大众', '丰田', '本田'][Math.floor(Math.random() * 5)]}-${['轿车', 'SUV', '皮卡', '工程车'][Math.floor(Math.random() * 4)]}`
        specification = ['小型', '中型', '大型'][Math.floor(Math.random() * 3)]
        serialNumber = `CL${Math.floor(Math.random() * 90000) + 10000}`
        attributes = {
          plateNumber: `苏A${String.fromCharCode(65 + Math.floor(Math.random() * 26))}${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`,
          engineNumber: `EN${Math.floor(Math.random() * 1000000) + 100000}`,
          fuelType: ['汽油', '柴油', '电动', '混合动力'][Math.floor(Math.random() * 4)],
          mileage: `${Math.floor(Math.random() * 100000)}km`
        }
        break
    }
    
    // 创建资产对象
    const asset: Asset = {
      id: `asset-${i + 1}`,
      assetCode: generateAssetCode(),
      assetName: `${assetType.name}-${Math.floor(Math.random() * 1000) + 1}`,
      assetType: assetType,
      model,
      specification,
      unitId: unit.id,
      unitName: unit.name,
      departmentId: department.id,
      departmentName: department.name,
      statusId: status.id,
      statusName: status.name,
      purchaseDate: generateRandomDate(),
      purchasePrice: generateRandomPrice(),
      warrantyPeriod: `${Math.floor(Math.random() * 5) + 1}年`,
      manufacturer: supplier,
      supplier,
      location: `${unit.name}-${['一楼', '二楼', '三楼', '仓库', '机房'][Math.floor(Math.random() * 5)]}-${Math.floor(Math.random() * 30) + 1}号`,
      responsiblePerson: ['张三', '李四', '王五', '赵六', '钱七', '孙八'][Math.floor(Math.random() * 6)],
      serialNumber,
      useStatus: status.id === 1 ? 1 : 0,
      useStatusName: status.id === 1 ? '使用中' : '未使用',
      remark: Math.random() > 0.7 ? `资产说明-${i}` : undefined,
      createBy: '管理员',
      createTime: new Date(Date.now() - Math.floor(Math.random() * 30 * 24 * 60 * 60 * 1000)).toISOString(),
      updateBy: Math.random() > 0.5 ? '管理员' : undefined,
      updateTime: Math.random() > 0.5 ? new Date().toISOString() : undefined,
      attributes
    }
    
    assets.push(asset)
  }
  
  return assets
}

// 导出字段配置
export const exportFieldOptions: ExportField[] = [
  { label: '资产编号', value: 'assetCode', checked: true },
  { label: '资产名称', value: 'assetName', checked: true },
  { label: '资产类型', value: 'assetType.name', checked: true },
  { label: '型号', value: 'model', checked: true },
  { label: '规格', value: 'specification', checked: true },
  { label: '所属单位', value: 'unitName', checked: true },
  { label: '所属部门', value: 'departmentName', checked: true },
  { label: '状态', value: 'statusName', checked: true },
  { label: '采购日期', value: 'purchaseDate', checked: true },
  { label: '采购价格', value: 'purchasePrice', checked: true },
  { label: '质保期', value: 'warrantyPeriod', checked: false },
  { label: '制造商', value: 'manufacturer', checked: false },
  { label: '供应商', value: 'supplier', checked: false },
  { label: '存放位置', value: 'location', checked: false },
  { label: '责任人', value: 'responsiblePerson', checked: true },
  { label: '序列号', value: 'serialNumber', checked: false },
  { label: '使用状态', value: 'useStatusName', checked: false },
  { label: '备注', value: 'remark', checked: false },
  { label: '创建人', value: 'createBy', checked: false },
  { label: '创建时间', value: 'createTime', checked: false },
  { label: '更新人', value: 'updateBy', checked: false },
  { label: '更新时间', value: 'updateTime', checked: false }
]

// 创建模拟数据
export const mockAssetList = reactive<Asset[]>(generateAssets(100))

// 模拟API - 获取资产列表
export const mockGetAssetList = async (params: QueryParams): Promise<PageResult<AssetListItem>> => {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 300))
  
  let result = [...mockAssetList]
  
  // 筛选处理
  if (params.keyword) {
    const keyword = params.keyword.toLowerCase()
    result = result.filter(item => 
      item.assetCode.toLowerCase().includes(keyword) || 
      item.assetName.toLowerCase().includes(keyword) ||
      item.assetType.name.toLowerCase().includes(keyword) ||
      item.model.toLowerCase().includes(keyword) ||
      item.specification.toLowerCase().includes(keyword) ||
      item.unitName.toLowerCase().includes(keyword) ||
      (item.responsiblePerson && item.responsiblePerson.toLowerCase().includes(keyword))
    )
  }
  
  // 按资产类型筛选
  if (params.assetTypeId) {
    result = result.filter(item => item.assetType.id === params.assetTypeId)
  }
  
  // 按单位筛选
  if (params.unitId) {
    result = result.filter(item => item.unitId === params.unitId)
  }
  
  // 按部门筛选
  if (params.departmentId) {
    result = result.filter(item => item.departmentId === params.departmentId)
  }
  
  // 按状态筛选
  if (params.statusId) {
    result = result.filter(item => item.statusId === params.statusId)
  }
  
  // 日期范围筛选
  if (params.startDate && params.endDate) {
    result = result.filter(item => 
      item.purchaseDate >= params.startDate && 
      item.purchaseDate <= params.endDate
    )
  }
  
  // 总数
  const total = result.length
  
  // 转换为列表项
  const listItems: AssetListItem[] = result.map(asset => {
    // 把attributes转为摘要字符串
    let attributesSummary = ''
    if (asset.attributes) {
      const keyValues = Object.entries(asset.attributes)
      if (keyValues.length > 0) {
        attributesSummary = keyValues.map(([key, value]) => `${key}: ${value}`).join(', ')
      }
    }
    
    const { attributes, ...rest } = asset
    return {
      ...rest,
      attributesSummary
    }
  })
  
  // 分页
  const start = (params.pageNum - 1) * params.pageSize
  const end = start + params.pageSize
  const pagedItems = listItems.slice(start, end)
  
  return {
    rows: pagedItems,
    total
  }
}

// 模拟API - 获取资产详情
export const mockGetAssetDetail = async (id: string): Promise<Asset> => {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 300))
  
  const asset = mockAssetList.find(item => item.id === id)
  if (!asset) {
    throw new Error('资产不存在')
  }
  
  return { ...asset }
}

// 模拟API - 新增资产
export const mockAddAsset = async (data: any): Promise<Asset> => {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 300))
  
  // 获取类型
  const assetType = assetTypes.find(type => type.id === data.assetTypeId)
  if (!assetType) {
    throw new Error('资产类型不存在')
  }
  
  // 获取单位
  const unit = units.find(u => u.id === data.unitId)
  if (!unit) {
    throw new Error('单位不存在')
  }
  
  // 获取部门
  const department = departments.find(d => d.id === data.departmentId)
  // 获取状态
  const status = statuses.find(s => s.id === data.statusId)
  if (!status) {
    throw new Error('状态不存在')
  }
  
  // 创建新资产
  const newAsset: Asset = {
    id: `asset-${mockAssetList.length + 1}`,
    assetCode: data.assetCode || generateAssetCode(),
    assetName: data.assetName,
    assetType: assetType,
    model: data.model || '',
    specification: data.specification || '',
    unitId: data.unitId,
    unitName: unit.name,
    departmentId: data.departmentId,
    departmentName: department ? department.name : undefined,
    statusId: data.statusId,
    statusName: status.name,
    purchaseDate: data.purchaseDate,
    purchasePrice: data.purchasePrice,
    warrantyPeriod: data.warrantyPeriod,
    manufacturer: data.manufacturer,
    supplier: data.supplier,
    location: data.location,
    responsiblePerson: data.responsiblePerson,
    serialNumber: data.serialNumber,
    useStatus: data.statusId === 1 ? 1 : 0,
    useStatusName: data.statusId === 1 ? '使用中' : '未使用',
    remark: data.remark,
    createBy: '当前用户',
    createTime: new Date().toISOString(),
    attributes: data.attributes || {}
  }
  
  // 添加到列表
  mockAssetList.push(newAsset)
  
  return newAsset
}

// 模拟API - 修改资产
export const mockUpdateAsset = async (data: Partial<Asset>): Promise<Asset> => {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 300))
  
  const index = mockAssetList.findIndex(item => item.id === data.id)
  if (index === -1) {
    throw new Error('资产不存在')
  }
  
  // 如果有类型变更
  let assetType = mockAssetList[index].assetType
  if (data.assetType && data.assetType.id !== mockAssetList[index].assetType.id) {
    const newType = assetTypes.find(type => type.id === data.assetType!.id)
    if (newType) {
      assetType = newType
    }
  }
  
  // 如果有单位变更
  let unitName = mockAssetList[index].unitName
  if (data.unitId && data.unitId !== mockAssetList[index].unitId) {
    const unit = units.find(u => u.id === data.unitId)
    if (unit) {
      unitName = unit.name
    }
  }
  
  // 如果有部门变更
  let departmentName = mockAssetList[index].departmentName
  if (data.departmentId && data.departmentId !== mockAssetList[index].departmentId) {
    const department = departments.find(d => d.id === data.departmentId)
    if (department) {
      departmentName = department.name
    }
  }
  
  // 如果有状态变更
  let statusName = mockAssetList[index].statusName
  if (data.statusId && data.statusId !== mockAssetList[index].statusId) {
    const status = statuses.find(s => s.id === data.statusId)
    if (status) {
      statusName = status.name
    }
  }
  
  // 更新资产
  const updatedAsset = {
    ...mockAssetList[index],
    ...data,
    assetType,
    unitName,
    departmentName,
    statusName,
    useStatus: data.statusId === 1 ? 1 : 0,
    useStatusName: data.statusId === 1 ? '使用中' : '未使用',
    updateBy: '当前用户',
    updateTime: new Date().toISOString()
  }
  
  mockAssetList[index] = updatedAsset
  
  return updatedAsset
}

// 模拟API - 删除资产
export const mockDeleteAsset = async (id: string): Promise<void> => {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 300))
  
  const index = mockAssetList.findIndex(item => item.id === id)
  if (index === -1) {
    throw new Error('资产不存在')
  }
  
  mockAssetList.splice(index, 1)
}

// 模拟API - 批量删除资产
export const mockBatchDeleteAsset = async (ids: string[]): Promise<void> => {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 300))
  
  for (const id of ids) {
    const index = mockAssetList.findIndex(item => item.id === id)
    if (index !== -1) {
      mockAssetList.splice(index, 1)
    }
  }
}

// 模拟API - 生成二维码
export const mockGenerateQrCode = async (id: string): Promise<AssetQrCode> => {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 300))
  
  const asset = mockAssetList.find(item => item.id === id)
  if (!asset) {
    throw new Error('资产不存在')
  }
  
  // 实际项目中应该调用后端生成二维码，这里仅模拟返回数据
  return {
    id: `qr-${asset.id}`,
    assetId: asset.id,
    assetCode: asset.assetCode,
    assetName: asset.assetName,
    unitName: asset.unitName,
    responsiblePerson: asset.responsiblePerson,
    // 实际二维码图片URL，这里用模拟数据
    qrCodeUrl: `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(JSON.stringify({
      assetId: asset.id,
      assetCode: asset.assetCode,
      assetName: asset.assetName
    }))}`,
    createTime: new Date().toISOString()
  }
}

// 模拟API - 批量生成二维码
export const mockBatchGenerateQrCode = async (ids: string[]): Promise<AssetQrCode[]> => {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 300))
  
  const qrCodes: AssetQrCode[] = []
  
  for (const id of ids) {
    const asset = mockAssetList.find(item => item.id === id)
    if (asset) {
      qrCodes.push({
        id: `qr-${asset.id}`,
        assetId: asset.id,
        assetCode: asset.assetCode,
        assetName: asset.assetName,
        unitName: asset.unitName,
        responsiblePerson: asset.responsiblePerson,
        qrCodeUrl: `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(JSON.stringify({
          assetId: asset.id,
          assetCode: asset.assetCode,
          assetName: asset.assetName
        }))}`,
        createTime: new Date().toISOString()
      })
    }
  }
  
  return qrCodes
}

// 导出常量数据（用于表单选择等）
export const CONSTANTS = {
  assetTypes,
  units,
  departments,
  statuses,
  suppliers
} 