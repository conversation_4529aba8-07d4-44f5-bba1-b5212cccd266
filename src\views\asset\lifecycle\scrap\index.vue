<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ScrapProcess, ApprovalStatus, LifecycleQueryParams } from '@/types/asset/lifecycle'
import { getScrapList, cancelProcess } from '@/api/asset/lifecycle'
import { PageResult } from '@/types/asset'
import ScrapForm from './components/ScrapForm.vue'

// 页面状态
const loading = ref(false)
const scrapData = ref<ScrapProcess[]>([])
const pagination = reactive({
  total: 0,
  pageNum: 1,
  pageSize: 10
})

// 查询参数
const queryParams = reactive<LifecycleQueryParams>({
  pageNum: 1,
  pageSize: 10,
  keyword: '',
  status: undefined,
  startTime: undefined,
  endTime: undefined
})

// 表单
const formDrawerVisible = ref(false)
const formDrawerTitle = ref('新增资产报废')
const scrapFormRef = ref()

// 日期范围变量
const dateRange = ref([])

// 处理日期变化
const handleDateRangeChange = (val) => {
  if (val) {
    queryParams.startTime = val[0]
    queryParams.endTime = val[1]
  } else {
    queryParams.startTime = undefined
    queryParams.endTime = undefined
  }
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // Mock 数据
    setTimeout(() => {
      const mockData: PageResult<ScrapProcess> = {
        rows: Array.from({ length: pagination.pageSize }).map((_, index) => {
          const id = `scrap-${Date.now()}-${index}`
          const statuses = [
            ApprovalStatus.PENDING,
            ApprovalStatus.APPROVED,
            ApprovalStatus.REJECTED,
            ApprovalStatus.PROCESSING
          ]
          
          return {
            id,
            processNo: `BF${String(10000 + index).padStart(5, '0')}`,
            title: `资产报废申请-${index + 1}`,
            applyBy: `申请人${index + 1}`,
            applyTime: `2023-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')} 12:00:00`,
            status: statuses[Math.floor(Math.random() * statuses.length)],
            currentNode: '部门审批',
            currentHandler: '审批人',
            scrapReason: `报废原因${index + 1}`,
            planScrapDate: `2023-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`,
            actualScrapDate: index % 2 === 0 ? `2023-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}` : undefined,
            remark: index % 3 === 0 ? `备注说明-${index}` : undefined,
            createTime: `2023-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')} 12:00:00`,
            updateTime: `2023-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')} 13:00:00`,
            assetCount: Math.floor(Math.random() * 5) + 1,
            assetList: []
          }
        }),
        total: 100
      }

      scrapData.value = mockData.rows
      pagination.total = mockData.total
      loading.value = false
    }, 500)

    // 实际调用接口时使用下面的代码
    // const { data } = await getScrapList(queryParams)
    // scrapData.value = data.rows
    // pagination.total = data.total
  } catch (error) {
    console.error('获取资产报废列表失败', error)
    ElMessage.error('获取资产报废列表失败')
  } finally {
    loading.value = false
  }
}

// 重置查询条件
const resetQuery = () => {
  dateRange.value = []
  Object.assign(queryParams, {
    pageNum: 1,
    pageSize: 10,
    keyword: '',
    status: undefined,
    startTime: undefined,
    endTime: undefined
  })
  loadData()
}

// 处理表格行状态样式
const getRowClassName = ({ row }: { row: ScrapProcess }) => {
  if (row.status === ApprovalStatus.APPROVED) {
    return 'success-row'
  } else if (row.status === ApprovalStatus.REJECTED) {
    return 'error-row'
  }
  return ''
}

// 查看详情
const handleViewDetail = (row: ScrapProcess) => {
  // 跳转到详情页
  const routeData = {
    name: 'assetScrapDetail',
    params: { id: row.id }
  }
  // this.$router.push(routeData)
}

// 添加报废
const handleAdd = () => {
  formDrawerTitle.value = '新增资产报废'
  formDrawerVisible.value = true
  nextTick(() => {
    scrapFormRef.value?.resetForm()
  })
}

// 表单提交处理
const handleFormSubmit = () => {
  ElMessage.success('报废申请提交成功')
  formDrawerVisible.value = false
  loadData()
}

// 取消报废流程
const handleCancel = (row: ScrapProcess) => {
  ElMessageBox.confirm('确定要取消该报废流程吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      // 调用取消API
      // await cancelProcess(row.id, '用户手动取消')
      ElMessage.success('报废流程已取消')
      loadData()
    })
    .catch(() => {})
}

// 创建处置单
const handleCreateDisposal = (row: ScrapProcess) => {
  // 跳转到处置页面
  ElMessage.success('跳转到处置页面...')
}

// 查看审批记录
const handleViewApproval = (row: ScrapProcess) => {
  // 跳转到审批记录页面或弹出审批记录对话框
}

// 表格分页改变
const handlePageChange = (pageNum: number) => {
  queryParams.pageNum = pageNum
  loadData()
}

// 表格每页条数改变
const handleSizeChange = (pageSize: number) => {
  queryParams.pageSize = pageSize
  queryParams.pageNum = 1
  loadData()
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<template>
  <div class="scrap-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>资产报废管理</span>
          <el-button type="primary" @click="handleAdd">新增报废</el-button>
        </div>
      </template>

      <!-- 搜索区域 -->
      <el-form :model="queryParams" inline>
        <el-form-item label="关键字">
          <el-input
            v-model="queryParams.keyword"
            placeholder="流程编号/标题"
            clearable
            style="width: 200px"
            @keyup.enter="loadData"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 150px">
            <el-option label="待审批" :value="ApprovalStatus.PENDING" />
            <el-option label="审批中" :value="ApprovalStatus.PROCESSING" />
            <el-option label="已批准" :value="ApprovalStatus.APPROVED" />
            <el-option label="已拒绝" :value="ApprovalStatus.REJECTED" />
            <el-option label="已取消" :value="ApprovalStatus.CANCELED" />
          </el-select>
        </el-form-item>
        <el-form-item label="申请时间">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px"
            @change="handleDateRangeChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadData">搜索</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 表格区域 -->
      <el-table
        v-loading="loading"
        :data="scrapData"
        style="width: 100%"
        border
        :row-class-name="getRowClassName"
      >
        <el-table-column prop="processNo" label="流程编号" width="120" />
        <el-table-column prop="title" label="标题" min-width="180" show-overflow-tooltip />
        <el-table-column prop="planScrapDate" label="计划报废日期" width="120" />
        <el-table-column prop="assetCount" label="资产数量" width="100" align="center" />
        <el-table-column prop="applyBy" label="申请人" width="100" />
        <el-table-column prop="applyTime" label="申请时间" width="160" sortable />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag
              :type="
                row.status === ApprovalStatus.APPROVED
                  ? 'success'
                  : row.status === ApprovalStatus.REJECTED
                  ? 'danger'
                  : row.status === ApprovalStatus.PENDING
                  ? 'warning'
                  : row.status === ApprovalStatus.CANCELED
                  ? 'info'
                  : 'primary'
              "
            >
              {{
                row.status === ApprovalStatus.APPROVED
                  ? '已批准'
                  : row.status === ApprovalStatus.REJECTED
                  ? '已拒绝'
                  : row.status === ApprovalStatus.PENDING
                  ? '待审批'
                  : row.status === ApprovalStatus.CANCELED
                  ? '已取消'
                  : '审批中'
              }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="220">
          <template #default="{ row }">
            <el-button link type="primary" @click="handleViewDetail(row)">详情</el-button>
            <el-button
              v-if="row.status === ApprovalStatus.PENDING"
              link
              type="danger"
              @click="handleCancel(row)"
            >
              取消
            </el-button>
            <el-button 
              v-if="row.status === ApprovalStatus.APPROVED && !row.actualScrapDate"
              link 
              type="success" 
              @click="handleCreateDisposal(row)"
            >
              创建处置单
            </el-button>
            <el-button link type="primary" @click="handleViewApproval(row)">审批记录</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>

    <!-- 表单抽屉 -->
    <el-drawer v-model="formDrawerVisible" :title="formDrawerTitle" direction="rtl" size="60%">
      <ScrapForm ref="scrapFormRef" @submit="handleFormSubmit" />
    </el-drawer>
  </div>
</template>

<style lang="scss" scoped>
.scrap-container {
  padding: 20px 0;

  .box-card {
    margin-bottom: 20px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }

  :deep(.success-row) {
    --el-table-tr-bg-color: var(--el-color-success-lighter);
  }

  :deep(.error-row) {
    --el-table-tr-bg-color: var(--el-color-danger-lighter);
  }
}
</style> 