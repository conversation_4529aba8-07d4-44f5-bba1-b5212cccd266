<template>
  <div class="asset-status-container">
    <!-- 统计卡片 -->
    <div class="statistics-container">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8" :lg="4" :xl="4" v-for="(item, index) in statisticsData" :key="index">
          <el-card shadow="hover" class="statistics-card" :body-style="{ padding: '15px' }">
            <div class="statistics-card-content">
              <div class="statistics-icon" :class="item.class">
                <el-icon>
                  <component :is="item.icon" />
                </el-icon>
              </div>
              <div class="statistics-info">
                <div class="statistics-title">{{ item.title }}</div>
                <div class="statistics-value">{{ item.value }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索和工具栏 -->
    <div class="search-toolbar">
      <el-card>
        <div class="toolbar-container">
          <div class="search-container">
            <el-form :model="queryParams" inline>
              <el-form-item label="关键词">
                <el-input
                  v-model="queryParams.keyword"
                  placeholder="状态名称/编码"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item label="默认状态">
                <el-select v-model="queryParams.isDefault" clearable placeholder="请选择">
                  <el-option label="是" :value="true" />
                  <el-option label="否" :value="false" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handleQuery">
                  <el-icon><Search /></el-icon>搜索
                </el-button>
                <el-button @click="resetQuery">
                  <el-icon><Refresh /></el-icon>重置
                </el-button>
              </el-form-item>
            </el-form>
          </div>
          <div class="button-container">
            <el-button type="primary" @click="handleAdd">
              <el-icon><Plus /></el-icon>新增
            </el-button>
            <el-button 
              type="danger" 
              :disabled="!selectedRows.length" 
              @click="handleBatchDelete"
            >
              <el-icon><Delete /></el-icon>批量删除
            </el-button>
            <el-radio-group v-model="viewMode" size="small">
              <el-radio-button label="table">表格视图</el-radio-button>
              <el-radio-button label="card">卡片视图</el-radio-button>
            </el-radio-group>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 表格视图 -->
    <el-card v-if="viewMode === 'table'" class="table-container">
      <el-table
        v-loading="loading"
        :data="statusList"
        border
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="name" label="状态名称" show-overflow-tooltip />
        <el-table-column prop="code" label="状态编码" show-overflow-tooltip />
        <el-table-column prop="description" label="状态描述" show-overflow-tooltip />
        <el-table-column prop="isDefault" label="默认状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.isDefault ? 'success' : 'info'">
              {{ scope.row.isDefault ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createBy" label="创建人" />
        <el-table-column prop="createTime" label="创建时间" width="160">
          <template #default="scope">
            {{ formatDate(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220" fixed="right">
          <template #default="scope">
            <el-button type="primary" link @click="handleEdit(scope.row)">
              <el-icon><Edit /></el-icon>编辑
            </el-button>
            <el-button type="danger" link @click="handleDelete(scope.row)">
              <el-icon><Delete /></el-icon>删除
            </el-button>
            <el-button type="info" link @click="handleViewLogs(scope.row)">
              <el-icon><Document /></el-icon>变更记录
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 卡片视图 -->
    <div v-else class="card-view-container">
      <el-row :gutter="20">
        <el-col
          v-for="item in statusList"
          :key="item.id"
          :xs="24"
          :sm="12"
          :md="8"
          :lg="6"
          :xl="4"
        >
          <el-card class="status-card" shadow="hover">
            <div class="status-card-header">
              <span class="status-name">{{ item.name }}</span>
              <el-tag :type="item.isDefault ? 'success' : 'info'" size="small">
                {{ item.isDefault ? '默认' : '非默认' }}
              </el-tag>
            </div>
            <div class="status-card-content">
              <div class="status-item">
                <span class="label">编码：</span>
                <span class="value">{{ item.code }}</span>
              </div>
              <div class="status-item">
                <span class="label">描述：</span>
                <span class="value description">{{ item.description }}</span>
              </div>
              <div class="status-item">
                <span class="label">创建人：</span>
                <span class="value">{{ item.createBy }}</span>
              </div>
              <div class="status-item">
                <span class="label">创建时间：</span>
                <span class="value">{{ formatDate(item.createTime) }}</span>
              </div>
            </div>
            <div class="status-card-footer">
              <el-button type="primary" link @click="handleEdit(item)">
                <el-icon><Edit /></el-icon>编辑
              </el-button>
              <el-button type="danger" link @click="handleDelete(item)">
                <el-icon><Delete /></el-icon>删除
              </el-button>
              <el-button type="info" link @click="handleViewLogs(item)">
                <el-icon><Document /></el-icon>变更记录
              </el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="formTitle"
      width="580px"
      destroy-on-close
      @closed="resetForm"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        label-position="right"
      >
        <el-form-item label="状态名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入状态名称" />
        </el-form-item>
        <el-form-item label="状态编码" prop="code">
          <el-input v-model="form.code" placeholder="请输入状态编码" />
        </el-form-item>
        <el-form-item label="状态描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请输入状态描述"
          />
        </el-form-item>
        <el-form-item label="默认状态" prop="isDefault">
          <el-switch v-model="form.isDefault" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 变更记录抽屉 -->
    <el-drawer
      v-model="drawerVisible"
      title="资产状态变更记录"
      size="60%"
      direction="rtl"
      destroy-on-close
    >
      <el-timeline>
        <el-timeline-item
          v-for="(log, index) in changeLogs"
          :key="index"
          :timestamp="formatDate(log.operateTime)"
          :type="getTimelineItemType(log.operationType)"
        >
          <el-card>
            <h4>{{ log.operationDesc }}</h4>
            <p class="log-content">
              <template v-if="log.operationType === 'CREATE'">
                创建了资产状态 <strong>{{ log.statusName }}</strong>
              </template>
              <template v-else-if="log.operationType === 'UPDATE'">
                将资产状态 <strong>{{ log.statusName }}</strong> 的
                {{ getFieldName(log.oldValue, log.newValue) }}
                从 <strong>{{ getDisplayValue(log.oldValue) }}</strong>
                修改为 <strong>{{ getDisplayValue(log.newValue) }}</strong>
              </template>
              <template v-else-if="log.operationType === 'DELETE'">
                删除了资产状态 <strong>{{ log.statusName }}</strong>
              </template>
            </p>
            <p class="log-operator">操作人：{{ log.operateBy }}</p>
          </el-card>
        </el-timeline-item>
      </el-timeline>
      <div v-if="!changeLogs.length" class="empty-logs">
        <el-empty description="暂无变更记录" />
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { 
  Search, 
  Refresh, 
  Plus, 
  Edit, 
  Delete, 
  Document, 
  Connection, 
  Finished, 
  Odometer, 
  SetUp, 
  SuitcaseLine, 
  Timer 
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import type { AssetStatus, AssetStatusChangeLog } from '@/types/asset'
import { 
  getAssetStatusList, 
  getAssetStatusDetail, 
  addAssetStatus, 
  updateAssetStatus, 
  deleteAssetStatus, 
  batchDeleteAssetStatus, 
  getAssetStatusStatistics, 
  getAssetStatusChangeLogs 
} from '@/api/asset/status'

// 视图模式
const viewMode = ref<'table' | 'card'>('table')

// 加载状态
const loading = ref(false)

// 分页数据
const statusList = ref<AssetStatus[]>([])
const total = ref(0)

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  keyword: '',
  isDefault: undefined as boolean | undefined
})

// 选中行
const selectedRows = ref<AssetStatus[]>([])

// 对话框控制
const dialogVisible = ref(false)
const formTitle = ref('')
const formRef = ref<FormInstance>()

// 表单数据
const form = reactive<Partial<AssetStatus>>({
  id: undefined,
  name: '',
  code: '',
  description: '',
  isDefault: false
})

// 表单校验规则
const rules = reactive<FormRules>({
  name: [
    { required: true, message: '请输入状态名称', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入状态编码', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  description: [
    { max: 200, message: '长度不能超过 200 个字符', trigger: 'blur' }
  ]
})

// 抽屉控制
const drawerVisible = ref(false)
const changeLogs = ref<AssetStatusChangeLog[]>([])

// 模拟数据 - 状态统计
const statisticsData = ref([
  { title: '在用', value: 120, icon: 'Finished', class: 'status-in-use' },
  { title: '闲置', value: 45, icon: 'Timer', class: 'status-idle' },
  { title: '调拨中', value: 18, icon: 'Connection', class: 'status-transferring' },
  { title: '维修中', value: 12, icon: 'SetUp', class: 'status-maintenance' },
  { title: '待处理', value: 8, icon: 'SuitcaseLine', class: 'status-pending' },
  { title: '报废', value: 24, icon: 'Delete', class: 'status-scrapped' }
])

// 模拟数据 - 资产状态
const mockStatuses: AssetStatus[] = [
  {
    id: 1,
    name: '在用',
    code: 'IN_USE',
    description: '资产正常使用中，状态良好，可正常发挥功能',
    isDefault: true,
    createBy: '系统管理员',
    createTime: '2023-01-10T09:30:00'
  },
  {
    id: 2,
    name: '闲置',
    code: 'IDLE',
    description: '资产处于可使用状态，但当前未被分配使用',
    isDefault: true,
    createBy: '系统管理员',
    createTime: '2023-01-10T09:30:00'
  },
  {
    id: 3,
    name: '调拨中',
    code: 'TRANSFERRING',
    description: '资产正在从一个部门或地点转移到另一个部门或地点',
    isDefault: true,
    createBy: '系统管理员',
    createTime: '2023-01-10T09:30:00'
  },
  {
    id: 4,
    name: '维修中',
    code: 'MAINTENANCE',
    description: '资产出现故障或损坏，正在进行维修',
    isDefault: true,
    createBy: '系统管理员',
    createTime: '2023-01-10T09:30:00'
  },
  {
    id: 5,
    name: '报废',
    code: 'SCRAPPED',
    description: '资产已经无法正常使用或修复成本过高，准备进行报废处理',
    isDefault: true,
    createBy: '系统管理员',
    createTime: '2023-01-10T09:30:00'
  },
  {
    id: 6,
    name: '已售出',
    code: 'SOLD',
    description: '资产已经通过正规流程出售',
    isDefault: false,
    createBy: '张三',
    createTime: '2023-03-15T14:20:00'
  },
  {
    id: 7,
    name: '租借中',
    code: 'LEASED',
    description: '资产当前处于租借状态，临时提供给其他部门或单位使用',
    isDefault: false,
    createBy: '李四',
    createTime: '2023-04-22T11:05:00'
  },
  {
    id: 8,
    name: '待验收',
    code: 'PENDING_ACCEPTANCE',
    description: '新采购的资产，正在进行验收流程',
    isDefault: false,
    createBy: '王五',
    createTime: '2023-05-30T16:45:00'
  },
  {
    id: 9,
    name: '封存',
    code: 'SEALED',
    description: '资产因特殊原因被封存，暂不投入使用',
    isDefault: false,
    createBy: '赵六',
    createTime: '2023-06-12T10:30:00'
  },
  {
    id: 10,
    name: '丢失',
    code: 'LOST',
    description: '资产处于丢失状态，需要进行寻找或赔偿处理',
    isDefault: false,
    createBy: '钱七',
    createTime: '2023-07-05T09:15:00'
  },
  {
    id: 11,
    name: '已归还',
    code: 'RETURNED',
    description: '租借的资产已归还原部门',
    isDefault: false,
    createBy: '孙八',
    createTime: '2023-08-18T14:20:00'
  },
  {
    id: 12,
    name: '待检修',
    code: 'PENDING_INSPECTION',
    description: '资产需要进行定期检修',
    isDefault: false,
    createBy: '周九',
    createTime: '2023-09-25T11:10:00'
  }
]

// 模拟数据 - 变更记录
const mockChangeLogs: AssetStatusChangeLog[] = [
  {
    id: 1,
    statusId: 6,
    statusName: '已售出',
    operationType: 'CREATE',
    operationDesc: '创建资产状态',
    oldValue: '',
    newValue: '已售出',
    operateBy: '张三',
    operateTime: '2023-03-15T14:20:00'
  },
  {
    id: 2,
    statusId: 6,
    statusName: '已售出',
    operationType: 'UPDATE',
    operationDesc: '修改资产状态',
    oldValue: '{"description":"资产已售出"}',
    newValue: '{"description":"资产已经通过正规流程出售"}',
    operateBy: '李四',
    operateTime: '2023-04-10T09:30:00'
  },
  {
    id: 3,
    statusId: 6,
    statusName: '已售出',
    operationType: 'UPDATE',
    operationDesc: '修改资产状态',
    oldValue: '{"isDefault":true}',
    newValue: '{"isDefault":false}',
    operateBy: '王五',
    operateTime: '2023-05-20T16:45:00'
  },
  {
    id: 4,
    statusId: 7,
    statusName: '租借中',
    operationType: 'CREATE',
    operationDesc: '创建资产状态',
    oldValue: '',
    newValue: '租借中',
    operateBy: '李四',
    operateTime: '2023-04-22T11:05:00'
  },
  {
    id: 5,
    statusId: 8,
    statusName: '待验收',
    operationType: 'CREATE',
    operationDesc: '创建资产状态',
    oldValue: '',
    newValue: '待验收',
    operateBy: '王五',
    operateTime: '2023-05-30T16:45:00'
  }
]

// 模拟 API 调用函数
const fetchAssetStatusList = async () => {
  loading.value = true
  try {
    // 模拟 API 延迟
    await new Promise(resolve => setTimeout(resolve, 300))
    
    // 过滤数据
    let result = [...mockStatuses]
    
    // 关键词过滤
    if (queryParams.keyword) {
      const keyword = queryParams.keyword.toLowerCase()
      result = result.filter(
        item => item.name.toLowerCase().includes(keyword) || 
                item.code.toLowerCase().includes(keyword) ||
                item.description.toLowerCase().includes(keyword)
      )
    }
    
    // 默认状态过滤
    if (queryParams.isDefault !== undefined) {
      result = result.filter(item => item.isDefault === queryParams.isDefault)
    }
    
    // 计算总数
    total.value = result.length
    
    // 分页处理
    const startIndex = (queryParams.pageNum - 1) * queryParams.pageSize
    const endIndex = startIndex + queryParams.pageSize
    statusList.value = result.slice(startIndex, endIndex)
  } finally {
    loading.value = false
  }
}

// 模拟获取统计数据
const fetchStatistics = async () => {
  try {
    // 模拟 API 延迟
    await new Promise(resolve => setTimeout(resolve, 300))
    
    // 这里使用模拟数据，实际项目中应调用 getAssetStatusStatistics API
    // 模拟数据在上方已定义
  } catch (error) {
    console.error('获取统计数据失败', error)
  }
}

// 模拟获取变更记录
const fetchStatusChangeLogs = async (statusId: number) => {
  try {
    // 模拟 API 延迟
    await new Promise(resolve => setTimeout(resolve, 300))
    
    // 过滤指定状态的变更记录
    changeLogs.value = mockChangeLogs.filter(log => log.statusId === statusId)
  } catch (error) {
    console.error('获取变更记录失败', error)
  }
}

// 添加资产状态
const addStatus = async (data: Omit<AssetStatus, 'id' | 'createTime' | 'createBy'>) => {
  try {
    // 模拟 API 延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 模拟生成新ID
    const newId = Math.max(...mockStatuses.map(item => item.id)) + 1
    
    // 构造新状态对象
    const newStatus: AssetStatus = {
      id: newId,
      name: data.name,
      code: data.code,
      description: data.description || '',
      isDefault: data.isDefault || false,
      createBy: '当前用户',
      createTime: new Date().toISOString()
    }
    
    // 添加到模拟数据中
    mockStatuses.push(newStatus)
    
    // 模拟添加变更记录
    const newLog: AssetStatusChangeLog = {
      id: Math.max(...mockChangeLogs.map(item => item.id)) + 1,
      statusId: newId,
      statusName: newStatus.name,
      operationType: 'CREATE',
      operationDesc: '创建资产状态',
      oldValue: '',
      newValue: newStatus.name,
      operateBy: '当前用户',
      operateTime: new Date().toISOString()
    }
    mockChangeLogs.push(newLog)
    
    return newStatus
  } catch (error) {
    console.error('添加资产状态失败', error)
    throw error
  }
}

// 更新资产状态
const updateStatus = async (data: Partial<AssetStatus> & { id: number }) => {
  try {
    // 模拟 API 延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 查找要更新的状态
    const index = mockStatuses.findIndex(item => item.id === data.id)
    if (index === -1) {
      throw new Error('资产状态不存在')
    }
    
    const oldStatus = { ...mockStatuses[index] }
    
    // 更新状态
    mockStatuses[index] = {
      ...oldStatus,
      ...data,
      updateBy: '当前用户',
      updateTime: new Date().toISOString()
    }
    
    // 添加变更记录
    const changedFields: Record<string, any> = {}
    if (data.name !== oldStatus.name) changedFields.name = { old: oldStatus.name, new: data.name }
    if (data.code !== oldStatus.code) changedFields.code = { old: oldStatus.code, new: data.code }
    if (data.description !== oldStatus.description) changedFields.description = { old: oldStatus.description, new: data.description }
    if (data.isDefault !== oldStatus.isDefault) changedFields.isDefault = { old: oldStatus.isDefault, new: data.isDefault }
    
    // 只有实际有变更时才添加记录
    if (Object.keys(changedFields).length > 0) {
      const newLog: AssetStatusChangeLog = {
        id: Math.max(...mockChangeLogs.map(item => item.id)) + 1,
        statusId: data.id,
        statusName: mockStatuses[index].name,
        operationType: 'UPDATE',
        operationDesc: '修改资产状态',
        oldValue: JSON.stringify(changedFields),
        newValue: JSON.stringify(changedFields),
        operateBy: '当前用户',
        operateTime: new Date().toISOString()
      }
      mockChangeLogs.push(newLog)
    }
    
    return mockStatuses[index]
  } catch (error) {
    console.error('更新资产状态失败', error)
    throw error
  }
}

// 删除资产状态
const removeStatus = async (id: number) => {
  try {
    // 模拟 API 延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 查找要删除的状态
    const index = mockStatuses.findIndex(item => item.id === id)
    if (index === -1) {
      throw new Error('资产状态不存在')
    }
    
    const statusName = mockStatuses[index].name
    
    // 删除状态
    mockStatuses.splice(index, 1)
    
    // 添加变更记录
    const newLog: AssetStatusChangeLog = {
      id: Math.max(...mockChangeLogs.map(item => item.id)) + 1,
      statusId: id,
      statusName: statusName,
      operationType: 'DELETE',
      operationDesc: '删除资产状态',
      oldValue: statusName,
      newValue: '',
      operateBy: '当前用户',
      operateTime: new Date().toISOString()
    }
    mockChangeLogs.push(newLog)
    
    return true
  } catch (error) {
    console.error('删除资产状态失败', error)
    throw error
  }
}

// 批量删除资产状态
const batchRemoveStatus = async (ids: number[]) => {
  try {
    // 模拟 API 延迟
    await new Promise(resolve => setTimeout(resolve, 700))
    
    for (const id of ids) {
      // 查找要删除的状态
      const index = mockStatuses.findIndex(item => item.id === id)
      if (index !== -1) {
        const statusName = mockStatuses[index].name
        
        // 删除状态
        mockStatuses.splice(index, 1)
        
        // 添加变更记录
        const newLog: AssetStatusChangeLog = {
          id: Math.max(...mockChangeLogs.map(item => item.id)) + 1,
          statusId: id,
          statusName: statusName,
          operationType: 'DELETE',
          operationDesc: '批量删除资产状态',
          oldValue: statusName,
          newValue: '',
          operateBy: '当前用户',
          operateTime: new Date().toISOString()
        }
        mockChangeLogs.push(newLog)
      }
    }
    
    return true
  } catch (error) {
    console.error('批量删除资产状态失败', error)
    throw error
  }
}

// 处理查询
const handleQuery = () => {
  queryParams.pageNum = 1
  fetchAssetStatusList()
}

// 重置查询条件
const resetQuery = () => {
  queryParams.keyword = ''
  queryParams.isDefault = undefined
  handleQuery()
}

// 处理页码变更
const handleCurrentChange = (page: number) => {
  queryParams.pageNum = page
  fetchAssetStatusList()
}

// 处理每页数量变更
const handleSizeChange = (size: number) => {
  queryParams.pageSize = size
  queryParams.pageNum = 1
  fetchAssetStatusList()
}

// 处理选择变更
const handleSelectionChange = (selection: AssetStatus[]) => {
  selectedRows.value = selection
}

// 处理添加
const handleAdd = () => {
  formTitle.value = '添加资产状态'
  form.id = undefined
  form.name = ''
  form.code = ''
  form.description = ''
  form.isDefault = false
  dialogVisible.value = true
}

// 处理编辑
const handleEdit = (row: AssetStatus) => {
  formTitle.value = '编辑资产状态'
  form.id = row.id
  form.name = row.name
  form.code = row.code
  form.description = row.description
  form.isDefault = row.isDefault
  dialogVisible.value = true
}

// 处理删除
const handleDelete = (row: AssetStatus) => {
  ElMessageBox.confirm(`确定要删除资产状态"${row.name}"吗？`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await removeStatus(row.id)
      ElMessage.success('删除成功')
      fetchAssetStatusList()
    } catch (error) {
      ElMessage.error('删除失败')
    }
  }).catch(() => {})
}

// 处理批量删除
const handleBatchDelete = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的记录')
    return
  }
  
  const names = selectedRows.value.map(row => row.name).join('、')
  const ids = selectedRows.value.map(row => row.id)
  
  ElMessageBox.confirm(`确定要删除以下资产状态吗？<br/>${names}`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
    dangerouslyUseHTMLString: true
  }).then(async () => {
    try {
      await batchRemoveStatus(ids)
      ElMessage.success('批量删除成功')
      fetchAssetStatusList()
    } catch (error) {
      ElMessage.error('批量删除失败')
    }
  }).catch(() => {})
}

// 处理查看变更记录
const handleViewLogs = (row: AssetStatus) => {
  drawerVisible.value = true
  fetchStatusChangeLogs(row.id)
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (form.id) {
          // 更新
          await updateStatus(form as Required<typeof form>)
          ElMessage.success('更新成功')
        } else {
          // 新增
          await addStatus(form as Omit<AssetStatus, 'id' | 'createTime' | 'createBy'>)
          ElMessage.success('添加成功')
        }
        dialogVisible.value = false
        fetchAssetStatusList()
      } catch (error) {
        ElMessage.error(form.id ? '更新失败' : '添加失败')
      }
    }
  })
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 格式化日期
const formatDate = (dateString: string): string => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  });
}

// 获取时间线项目类型
const getTimelineItemType = (operationType: string): 'primary' | 'success' | 'warning' | 'danger' | 'info' => {
  switch (operationType) {
    case 'CREATE':
      return 'success'
    case 'UPDATE':
      return 'primary'
    case 'DELETE':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取字段名称
const getFieldName = (oldValue: string, newValue: string): string => {
  try {
    const oldObj = JSON.parse(oldValue)
    const keys = Object.keys(oldObj)
    if (keys.length > 0) {
      switch (keys[0]) {
        case 'name': return '名称'
        case 'code': return '编码'
        case 'description': return '描述'
        case 'isDefault': return '默认状态'
        default: return '信息'
      }
    }
    return '信息'
  } catch (e) {
    return '信息'
  }
}

// 获取显示值
const getDisplayValue = (value: string): string => {
  try {
    const obj = JSON.parse(value)
    const keys = Object.keys(obj)
    if (keys.length > 0) {
      const key = keys[0]
      const val = obj[key]
      
      // 处理布尔类型
      if (typeof val === 'boolean') {
        return val ? '是' : '否'
      }
      
      return val?.toString() || ''
    }
    return value
  } catch (e) {
    return value
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchAssetStatusList()
  fetchStatistics()
})

</script>

<style scoped lang="scss">
.asset-status-container {
  padding: 20px;
  
  .statistics-container {
    margin-bottom: 20px;
    
    .statistics-card {
      height: 100px;
      margin-bottom: 20px;
      
      .statistics-card-content {
        display: flex;
        align-items: center;
        
        .statistics-icon {
          width: 60px;
          height: 60px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 15px;
          
          .el-icon {
            font-size: 28px;
            color: #fff;
          }
          
          &.status-in-use {
            background-color: #67c23a;
          }
          
          &.status-idle {
            background-color: #909399;
          }
          
          &.status-transferring {
            background-color: #409eff;
          }
          
          &.status-maintenance {
            background-color: #e6a23c;
          }
          
          &.status-pending {
            background-color: #9561e2;
          }
          
          &.status-scrapped {
            background-color: #f56c6c;
          }
        }
        
        .statistics-info {
          flex: 1;
          
          .statistics-title {
            font-size: 14px;
            color: #606266;
            margin-bottom: 5px;
          }
          
          .statistics-value {
            font-size: 24px;
            font-weight: bold;
            color: #303133;
          }
        }
      }
    }
  }
  
  .search-toolbar {
    margin-bottom: 20px;
    
    .toolbar-container {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      
      .search-container {
        flex: 1;
        margin-right: 20px;
      }
      
      .button-container {
        display: flex;
        align-items: center;
        gap: 10px;
      }
      
      @media (max-width: 768px) {
        .search-container {
          width: 100%;
          margin-right: 0;
          margin-bottom: 15px;
        }
        
        .button-container {
          width: 100%;
          justify-content: flex-end;
        }
      }
    }
  }
  
  .table-container {
    margin-bottom: 20px;
  }
  
  .card-view-container {
    margin-bottom: 20px;
    
    .status-card {
      height: 100%;
      margin-bottom: 20px;
      transition: all 0.3s;
      
      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      }
      
      .status-card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        
        .status-name {
          font-size: 16px;
          font-weight: bold;
        }
      }
      
      .status-card-content {
        margin-bottom: 15px;
        
        .status-item {
          margin-bottom: 8px;
          display: flex;
          
          .label {
            color: #606266;
            width: 70px;
            flex-shrink: 0;
          }
          
          .value {
            color: #303133;
            flex: 1;
            
            &.description {
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
            }
          }
        }
      }
      
      .status-card-footer {
        display: flex;
        justify-content: flex-end;
        border-top: 1px solid #ebeef5;
        padding-top: 10px;
      }
    }
  }
  
  .pagination-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }
  
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
  }
  
  .log-content {
    margin: 10px 0;
    line-height: 1.5;
  }
  
  .log-operator {
    font-size: 12px;
    color: #909399;
    margin-top: 5px;
  }
  
  .empty-logs {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300px;
  }
}
</style> 