<template>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    label-width="100px"
    class="asset-form"
  >
    <!-- 基本信息 -->
    <el-divider content-position="left">基本信息</el-divider>
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="资产编号" prop="assetCode">
          <el-input 
            v-model="form.assetCode" 
            placeholder="请输入资产编号" 
            :disabled="isEdit || autoGenerateCode"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12" v-if="!isEdit">
        <el-form-item>
          <el-checkbox v-model="autoGenerateCode">自动生成编号</el-checkbox>
        </el-form-item>
      </el-col>
    </el-row>
    
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="资产名称" prop="assetName">
          <el-input v-model="form.assetName" placeholder="请输入资产名称" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="资产类型" prop="assetTypeId">
          <el-select 
            v-model="form.assetTypeId" 
            placeholder="请选择资产类型" 
            style="width: 100%"
            @change="handleAssetTypeChange"
          >
            <el-option
              v-for="item in constants.assetTypes"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="型号" prop="model">
          <el-input v-model="form.model" placeholder="请输入资产型号" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="规格" prop="specification">
          <el-input v-model="form.specification" placeholder="请输入规格" />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="所属单位" prop="unitId">
          <el-select 
            v-model="form.unitId" 
            placeholder="请选择所属单位" 
            style="width: 100%"
            @change="handleUnitChange"
          >
            <el-option
              v-for="item in constants.units"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="所属部门" prop="departmentId">
          <el-select 
            v-model="form.departmentId" 
            placeholder="请选择所属部门" 
            style="width: 100%"
            :disabled="!form.unitId"
          >
            <el-option
              v-for="item in filteredDepartments"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="状态" prop="statusId">
          <el-select v-model="form.statusId" placeholder="请选择状态" style="width: 100%">
            <el-option
              v-for="item in constants.statuses"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
              <el-tag
                :type="getStatusTagType(item.id)"
                disable-transitions
                size="small"
                style="margin-right: 5px"
              >
                {{ item.name }}
              </el-tag>
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="序列号" prop="serialNumber">
          <el-input v-model="form.serialNumber" placeholder="请输入序列号" />
        </el-form-item>
      </el-col>
    </el-row>

    <!-- 采购信息 -->
    <el-divider content-position="left">采购信息</el-divider>
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="采购日期" prop="purchaseDate">
          <el-date-picker
            v-model="form.purchaseDate"
            type="date"
            placeholder="请选择采购日期"
            style="width: 100%"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="采购价格" prop="purchasePrice">
          <el-input-number
            v-model="form.purchasePrice"
            :precision="2"
            :step="100"
            :min="0"
            style="width: 100%"
            placeholder="请输入采购价格"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="供应商" prop="supplier">
          <el-select 
            v-model="form.supplier" 
            placeholder="请选择供应商" 
            style="width: 100%"
            filterable
            allow-create
          >
            <el-option
              v-for="item in constants.suppliers"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="质保期" prop="warrantyPeriod">
          <el-input v-model="form.warrantyPeriod" placeholder="如：3年" />
        </el-form-item>
      </el-col>
    </el-row>

    <!-- 位置信息 -->
    <el-divider content-position="left">位置信息</el-divider>
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="存放位置" prop="location">
          <el-input v-model="form.location" placeholder="请输入存放位置" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="责任人" prop="responsiblePerson">
          <el-input v-model="form.responsiblePerson" placeholder="请输入责任人" />
        </el-form-item>
      </el-col>
    </el-row>

    <!-- 附加属性 -->
    <el-divider content-position="left">附加属性</el-divider>
    <div v-if="assetTypeAttributes.length > 0">
      <el-row :gutter="20" v-for="(attr, index) in assetTypeAttributes" :key="index">
        <el-col :span="12">
          <el-form-item :label="attr.label" :prop="`attributes.${attr.key}`">
            <el-input 
              v-model="form.attributes[attr.key]" 
              :placeholder="`请输入${attr.label}`" 
            />
          </el-form-item>
        </el-col>
      </el-row>
    </div>
    <el-empty v-else description="请先选择资产类型" />

    <!-- 备注 -->
    <el-divider content-position="left">附加信息</el-divider>
    <el-form-item label="备注" prop="remark">
      <el-input
        v-model="form.remark"
        type="textarea"
        placeholder="请输入备注信息"
        :rows="3"
      />
    </el-form-item>

    <!-- 表单操作 -->
    <el-form-item>
      <el-button type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="resetForm">重 置</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'
// 引入模拟数据
import { CONSTANTS } from '../mock'
import { generateAssetCode } from '../utils'

// 定义属性
const props = defineProps<{
  isEdit?: boolean
  assetData?: any
}>()

// 定义事件
const emit = defineEmits(['submit', 'cancel'])

// 表单引用
const formRef = ref<FormInstance>()

// 自动生成编号
const autoGenerateCode = ref(true)

// 资产类型特定属性
const assetTypeAttributes = ref<{ key: string; label: string }[]>([])

// 常量数据
const constants = CONSTANTS

// 表单数据
const form = reactive({
  id: undefined as string | undefined,
  assetCode: '',
  assetName: '',
  assetTypeId: undefined as number | undefined,
  model: '',
  specification: '',
  unitId: undefined as number | undefined,
  departmentId: undefined as number | undefined,
  statusId: 1, // 默认"在用"
  purchaseDate: '',
  purchasePrice: undefined as number | undefined,
  warrantyPeriod: '',
  manufacturer: '',
  supplier: '',
  location: '',
  responsiblePerson: '',
  serialNumber: '',
  remark: '',
  attributes: {} as Record<string, any>
})

// 表单验证规则
const rules = {
  assetCode: [
    { required: true, message: '请输入资产编号', trigger: 'blur' },
    { min: 3, max: 50, message: '长度在 3 到 50 个字符', trigger: 'blur' }
  ],
  assetName: [
    { required: true, message: '请输入资产名称', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  assetTypeId: [
    { required: true, message: '请选择资产类型', trigger: 'change' }
  ],
  unitId: [
    { required: true, message: '请选择所属单位', trigger: 'change' }
  ],
  departmentId: [
    { required: true, message: '请选择所属部门', trigger: 'change' }
  ],
  statusId: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ],
  purchaseDate: [
    { required: true, message: '请选择采购日期', trigger: 'change' }
  ],
  purchasePrice: [
    { required: true, message: '请输入采购价格', trigger: 'blur' }
  ]
}

// 根据选择的单位过滤部门
const filteredDepartments = computed(() => {
  if (!form.unitId) return []
  return constants.departments.filter(dept => dept.unitId === form.unitId)
})

// 获取状态标签类型
const getStatusTagType = (statusId: number): string => {
  const statusTypeMap: Record<number, string> = {
    1: 'success', // 在用
    2: 'info',    // 闲置
    3: 'warning', // 维修中
    4: 'warning', // 调拨中
    5: 'danger'   // 报废
  }
  return statusTypeMap[statusId] || ''
}

// 资产类型变更处理
const handleAssetTypeChange = (typeId: number) => {
  // 根据资产类型设置特定的属性字段
  form.attributes = {} // 清空现有属性
  
  switch (typeId) {
    case 1: // 水泵设备
      assetTypeAttributes.value = [
        { key: 'power', label: '功率' },
        { key: 'pumpType', label: '泵类型' },
        { key: 'material', label: '材质' },
        { key: 'installationPosition', label: '安装位置' }
      ]
      break
    case 2: // 电气设备
      assetTypeAttributes.value = [
        { key: 'voltage', label: '电压' },
        { key: 'current', label: '电流' },
        { key: 'protectionLevel', label: '防护等级' },
        { key: 'controlType', label: '控制类型' }
      ]
      break
    case 3: // 检测设备
      assetTypeAttributes.value = [
        { key: 'range', label: '检测范围' },
        { key: 'accuracy', label: '精度' },
        { key: 'parameter', label: '测量参数' },
        { key: 'calibrationDate', label: '校准日期' }
      ]
      break
    case 4: // 管网设备
      assetTypeAttributes.value = [
        { key: 'material', label: '材质' },
        { key: 'pressure', label: '压力等级' },
        { key: 'connectionType', label: '连接方式' },
        { key: 'installationDate', label: '安装日期' }
      ]
      break
    case 5: // 办公设备
      assetTypeAttributes.value = [
        { key: 'type', label: '设备类型' },
        { key: 'cpu', label: 'CPU型号' },
        { key: 'memory', label: '内存' },
        { key: 'storage', label: '存储' },
        { key: 'operatingSystem', label: '操作系统' }
      ]
      break
    case 6: // 车辆
      assetTypeAttributes.value = [
        { key: 'plateNumber', label: '车牌号' },
        { key: 'engineNumber', label: '发动机号' },
        { key: 'fuelType', label: '燃料类型' },
        { key: 'mileage', label: '里程数' }
      ]
      break
    default:
      assetTypeAttributes.value = []
  }
  
  // 为每个属性初始化一个空值
  assetTypeAttributes.value.forEach(attr => {
    if (!form.attributes[attr.key]) {
      form.attributes[attr.key] = ''
    }
  })
}

// 单位变更处理
const handleUnitChange = () => {
  form.departmentId = undefined // 清空部门选择
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate((valid, fields) => {
    if (valid) {
      // 如果自动生成编号且不是编辑模式
      if (autoGenerateCode.value && !props.isEdit) {
        form.assetCode = generateAssetCode()
      }
      
      // 提交表单数据
      const formData = { ...form }
      emit('submit', formData)
    } else {
      ElMessage.error('表单填写有误，请检查')
      console.log('表单错误字段:', fields)
    }
  })
}

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
  
  // 如果是编辑模式，恢复原始数据
  if (props.isEdit && props.assetData) {
    initFormData()
  } else {
    // 清空附加属性
    form.attributes = {}
    assetTypeAttributes.value = []
  }
}

// 初始化表单数据
const initFormData = () => {
  if (!props.assetData) return
  
  // 设置基本字段
  form.id = props.assetData.id
  form.assetCode = props.assetData.assetCode
  form.assetName = props.assetData.assetName
  form.assetTypeId = props.assetData.assetType.id
  form.model = props.assetData.model || ''
  form.specification = props.assetData.specification || ''
  form.unitId = props.assetData.unitId
  form.departmentId = props.assetData.departmentId
  form.statusId = props.assetData.statusId
  form.purchaseDate = props.assetData.purchaseDate
  form.purchasePrice = props.assetData.purchasePrice
  form.warrantyPeriod = props.assetData.warrantyPeriod || ''
  form.manufacturer = props.assetData.manufacturer || ''
  form.supplier = props.assetData.supplier || ''
  form.location = props.assetData.location || ''
  form.responsiblePerson = props.assetData.responsiblePerson || ''
  form.serialNumber = props.assetData.serialNumber || ''
  form.remark = props.assetData.remark || ''
  
  // 设置附加属性
  if (props.assetData.attributes) {
    form.attributes = { ...props.assetData.attributes }
    
    // 触发资产类型变更以设置正确的属性字段
    handleAssetTypeChange(form.assetTypeId)
  }
}

// 监听编码自动生成变化
watch(autoGenerateCode, (newVal) => {
  if (newVal) {
    form.assetCode = ''
  }
})

// 组件挂载时初始化数据
onMounted(() => {
  if (props.isEdit && props.assetData) {
    autoGenerateCode.value = false
    initFormData()
  }
})
</script>

<style lang="scss" scoped>
.asset-form {
  padding: 20px;
  
  .el-divider {
    margin: 15px 0;
  }
  
  :deep(.el-input-number .el-input__inner) {
    text-align: left;
  }
}
</style> 