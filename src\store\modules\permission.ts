import { defineStore } from 'pinia'
import { store } from '@/store'
import { cloneDeep } from 'lodash-es'
import remainingRouter from '@/router/modules/remaining'
import assetRoutes from '@/router/modules/asset'
import { flatMultiLevelRoutes, generateRoute } from '@/utils/routerHelper'
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'
import router from '@/router'
import type { RouteRecordRaw } from 'vue-router'

const { wsCache } = useCache()

export interface PermissionState {
  routers: AppRouteRecordRaw[]
  addRouters: AppRouteRecordRaw[]
  menuTabRouters: AppRouteRecordRaw[]
}

export const usePermissionStore = defineStore('permission', {
  state: (): PermissionState => ({
    routers: [],
    addRouters: [],
    menuTabRouters: []
  }),
  getters: {
    getRouters(): AppRouteRecordRaw[] {
      return this.routers
    },
    getAddRouters(): AppRouteRecordRaw[] {
      return flatMultiLevelRoutes(cloneDeep(this.addRouters))
    },
    getMenuTabRouters(): AppRouteRecordRaw[] {
      return this.menuTabRouters
    }
  },
  actions: {
    async generateRoutes(): Promise<unknown> {
      return new Promise<void>(async (resolve) => {
        // 检查是否使用资产管理模块
        const useAssetModule = true // 设置为true可以只加载资产管理模块
        
        if (useAssetModule) {
          // 强制使用本地的资产管理路由，不从后端获取
          wsCache.set(CACHE_KEY.ASSET_ROUTERS, [assetRoutes])
          
          // 处理资产管理路由
          const routerMap: AppRouteRecordRaw[] = [assetRoutes] as AppRouteRecordRaw[]
          
          // 添加404路由
          this.addRouters = routerMap.concat([
            {
              path: '/:path(.*)*',
              component: () => import('@/views/Error/404.vue'),
              name: '404Page',
              meta: {
                hidden: true,
                breadcrumb: false
              }
            }
          ])
          
          // 使用资产路由和默认路由
          this.routers = cloneDeep(remainingRouter).concat(routerMap)
          
          // 将资产路由信息存入缓存，以便后续使用
          wsCache.set(CACHE_KEY.ROLE_ROUTERS, routerMap)
          
          // 手动添加路由
          routerMap.forEach((route) => {
            router.addRoute(route as unknown as RouteRecordRaw)
          })
          
          resolve()
          return
        }
        
        // 以下为原有代码 - 如果不使用资产管理模块，则保持原有逻辑
        let res: AppCustomRouteRecordRaw[] = []
        const roleRouters = wsCache.get(CACHE_KEY.ROLE_ROUTERS)
        if (roleRouters) {
          res = roleRouters as AppCustomRouteRecordRaw[]
        }
        const routerMap: AppRouteRecordRaw[] = generateRoute(res)
        this.addRouters = routerMap.concat([
          {
            path: '/:path(.*)*',
            component: () => import('@/views/Error/404.vue'),
            name: '404Page',
            meta: {
              hidden: true,
              breadcrumb: false
            }
          }
        ])
        this.routers = cloneDeep(remainingRouter).concat(routerMap)
        resolve()
      })
    },
    setMenuTabRouters(routers: AppRouteRecordRaw[]): void {
      this.menuTabRouters = routers
    }
  },
  persist: false
})

export const usePermissionStoreWithOut = () => {
  return usePermissionStore(store)
}
