<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { Plus, Edit, Delete, Upload, Download } from '@element-plus/icons-vue'

// 类型定义
interface AssetType {
  id: string
  code: string
  name: string
  parentId: string | null
  level: number
  sort: number
  description: string
  isLeaf: boolean
  children?: AssetType[]
  createTime: string
  createBy: string
}

// 状态
const loading = ref(false)
const dialogVisible = ref(false)
const dialogTitle = ref('')
const isEdit = ref(false)
const currentType = ref<AssetType | null>(null)

// 表单
const formRef = ref<FormInstance>()
const form = reactive({
  code: '',
  name: '',
  parentId: '',
  level: 1,
  sort: 0,
  description: ''
})

// 表单校验规则
const rules = reactive<FormRules>({
  code: [
    { required: true, message: '请输入类型编码', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入类型名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  parentId: [
    { required: true, message: '请选择上级类型', trigger: 'change' }
  ]
})

// 表格数据
const tableData = ref<AssetType[]>([])

// 获取资产类型列表
const fetchAssetTypes = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取数据
    // 模拟数据
    tableData.value = [
      {
        id: '1',
        code: 'PROD',
        name: '生产设备',
        parentId: null,
        level: 1,
        sort: 1,
        description: '用于生产制造的设备',
        isLeaf: false,
        createTime: '2024-01-01 10:00:00',
        createBy: 'admin',
        children: [
          {
            id: '1-1',
            code: 'PROD-MACHINE',
            name: '机械设备',
            parentId: '1',
            level: 2,
            sort: 1,
            description: '各类机械设备',
            isLeaf: true,
            createTime: '2024-01-01 10:00:00',
            createBy: 'admin'
          },
          {
            id: '1-2',
            code: 'PROD-ELECTRIC',
            name: '电气设备',
            parentId: '1',
            level: 2,
            sort: 2,
            description: '各类电气设备',
            isLeaf: true,
            createTime: '2024-01-01 10:00:00',
            createBy: 'admin'
          }
        ]
      },
      {
        id: '2',
        code: 'OFFICE',
        name: '办公设备',
        parentId: null,
        level: 1,
        sort: 2,
        description: '用于办公的设备',
        isLeaf: false,
        createTime: '2024-01-01 10:00:00',
        createBy: 'admin',
        children: [
          {
            id: '2-1',
            code: 'OFFICE-COMPUTER',
            name: '计算机设备',
            parentId: '2',
            level: 2,
            sort: 1,
            description: '电脑、打印机等',
            isLeaf: true,
            createTime: '2024-01-01 10:00:00',
            createBy: 'admin'
          }
        ]
      }
    ]
  } catch (error) {
    console.error('获取资产类型列表失败', error)
    ElMessage.error('获取资产类型列表失败')
  } finally {
    loading.value = false
  }
}

// 打开新增对话框
const handleAdd = (row?: AssetType) => {
  isEdit.value = false
  dialogTitle.value = '新增资产类型'
  currentType.value = null
  
  // 重置表单
  Object.assign(form, {
    code: '',
    name: '',
    parentId: row?.id || '',
    level: row ? row.level + 1 : 1,
    sort: 0,
    description: ''
  })
  
  dialogVisible.value = true
}

// 打开编辑对话框
const handleEdit = (row: AssetType) => {
  isEdit.value = true
  dialogTitle.value = '编辑资产类型'
  currentType.value = row
  
  // 填充表单
  Object.assign(form, {
    code: row.code,
    name: row.name,
    parentId: row.parentId || '',
    level: row.level,
    sort: row.sort,
    description: row.description
  })
  
  dialogVisible.value = true
}

// 删除资产类型
const handleDelete = (row: AssetType) => {
  ElMessageBox.confirm(
    '确定要删除该资产类型吗？删除后不可恢复，且会同时删除其下所有子类型。',
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      // TODO: 调用删除API
      ElMessage.success('删除成功')
      fetchAssetTypes()
    } catch (error) {
      console.error('删除资产类型失败', error)
      ElMessage.error('删除资产类型失败')
    }
  }).catch(() => {})
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        // TODO: 调用保存API
        ElMessage.success(isEdit.value ? '修改成功' : '新增成功')
        dialogVisible.value = false
        fetchAssetTypes()
      } catch (error) {
        console.error('保存资产类型失败', error)
        ElMessage.error('保存资产类型失败')
      }
    }
  })
}

// 导入资产类型
const handleImport = () => {
  // TODO: 实现导入功能
  ElMessage.info('导入功能开发中')
}

// 导出资产类型
const handleExport = () => {
  // TODO: 实现导出功能
  ElMessage.info('导出功能开发中')
}

// 页面加载时获取数据
onMounted(() => {
  fetchAssetTypes()
})
</script>

<template>
  <div class="asset-type-container">
    <el-card v-loading="loading">
      <template #header>
        <div class="card-header">
          <div class="header-title">
            <h3>资产类型管理</h3>
            <el-tag type="info" effect="plain">支持树形结构管理资产类型</el-tag>
          </div>
          <div class="header-actions">
            <el-button type="primary" :icon="Plus" @click="handleAdd()">新增根类型</el-button>
            <el-button :icon="Upload" @click="handleImport">导入</el-button>
            <el-button :icon="Download" @click="handleExport">导出</el-button>
          </div>
        </div>
      </template>

      <!-- 资产类型表格 -->
      <el-table
        :data="tableData"
        row-key="id"
        border
        default-expand-all
        :tree-props="{ children: 'children', hasChildren: '!isLeaf' }"
      >
        <el-table-column prop="code" label="类型编码" width="150" />
        <el-table-column prop="name" label="类型名称" width="200" />
        <el-table-column prop="level" label="层级" width="80" align="center" />
        <el-table-column prop="sort" label="排序" width="80" align="center" />
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="createTime" label="创建时间" width="160" />
        <el-table-column prop="createBy" label="创建人" width="100" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link :icon="Plus" @click="handleAdd(row)">新增子类型</el-button>
            <el-button type="primary" link :icon="Edit" @click="handleEdit(row)">编辑</el-button>
            <el-button type="danger" link :icon="Delete" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      destroy-on-close
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="上级类型" prop="parentId">
          <el-cascader
            v-model="form.parentId"
            :options="tableData"
            :props="{
              checkStrictly: true,
              value: 'id',
              label: 'name',
              children: 'children',
              emitPath: false
            }"
            placeholder="请选择上级类型"
            clearable
          />
        </el-form-item>
        <el-form-item label="类型编码" prop="code">
          <el-input v-model="form.code" placeholder="请输入类型编码" />
        </el-form-item>
        <el-form-item label="类型名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入类型名称" />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="form.sort" :min="0" :max="999" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请输入描述信息"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.asset-type-container {
  padding: 20px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-title {
      display: flex;
      align-items: center;
      gap: 10px;

      h3 {
        margin: 0;
        font-size: 18px;
      }
    }

    .header-actions {
      display: flex;
      gap: 10px;
    }
  }
}
</style> 