<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { IdleForm as IIdleForm, AttachmentInfo } from '@/types/asset/lifecycle'
import { Asset } from '@/types/asset'
import { createIdle } from '@/api/asset/lifecycle'
import AssetSelector from '@/components/AssetSelector/index.vue'
import FileUpload from '@/components/FileUpload/index.vue'

// 表单引用
const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive<IIdleForm>({
  title: '',
  idleReason: '',
  planIdleDate: '',
  expectedReturnDate: '',
  assetList: [] as any[], // 类型兼容处理
  remark: '',
  attachments: []
})

// 表单验证规则
const formRules = reactive<FormRules>({
  title: [
    { required: true, message: '请输入闲置标题', trigger: 'blur' },
    { min: 3, max: 100, message: '长度在 3 到 100 个字符', trigger: 'blur' }
  ],
  idleReason: [
    { required: true, message: '请输入闲置原因', trigger: 'blur' },
    { min: 2, max: 200, message: '长度在 2 到 200 个字符', trigger: 'blur' }
  ],
  planIdleDate: [
    { required: true, message: '请选择计划闲置日期', trigger: 'change' }
  ],
  assetList: [
    { required: true, message: '请选择需要闲置的资产', trigger: 'change' },
    { type: 'array', min: 1, message: '至少选择一项资产', trigger: 'change' }
  ]
})

// 资产选择器引用
const assetSelectorRef = ref()

// 移除资产
const handleRemoveAsset = (index: number) => {
  formData.assetList.splice(index, 1)
}

// 重置表单
const resetForm = () => {
  if (!formRef.value) return
  formRef.value.resetFields()
  formData.assetList = []
  formData.remark = ''
  formData.attachments = []
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid, fields) => {
    if (valid) {
      try {
        // 模拟API调用
        setTimeout(() => {
          // 实际调用接口时使用下面的代码
          // await createIdle(formData)
          ElMessage.success('闲置申请提交成功')
          emit('submit')
        }, 500)
      } catch (error) {
        console.error('提交闲置申请失败', error)
        ElMessage.error('提交闲置申请失败')
      }
    } else {
      console.log('验证失败', fields)
    }
  })
}

// 事件发射
const emit = defineEmits(['submit'])

// 提供重置方法给父组件
defineExpose({
  resetForm
})
</script>

<template>
  <div class="idle-form">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      class="form-container"
    >
      <el-form-item label="闲置标题" prop="title">
        <el-input v-model="formData.title" placeholder="请输入闲置标题" />
      </el-form-item>

      <el-form-item label="闲置原因" prop="idleReason">
        <el-input
          v-model="formData.idleReason"
          type="textarea"
          :rows="3"
          placeholder="请输入闲置原因"
        />
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="计划闲置日期" prop="planIdleDate">
            <el-date-picker
              v-model="formData.planIdleDate"
              type="date"
              placeholder="请选择计划闲置日期"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="预计启用日期" prop="expectedReturnDate">
            <el-date-picker
              v-model="formData.expectedReturnDate"
              type="date"
              placeholder="请选择预计启用日期"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="闲置资产" prop="assetList">
        <div class="asset-selector-wrapper">
          <!-- 假设状态 1 表示"在用" -->
          <AssetSelector
            ref="assetSelectorRef"
            v-model="formData.assetList"
            :filter-by-status="[1]" 
            title="选择闲置资产"
          />
        </div>
      </el-form-item>

      <!-- 已选资产列表 -->
      <el-form-item v-if="formData.assetList.length > 0" label="闲置清单">
        <el-table :data="formData.assetList" border style="width: 100%" max-height="400">
          <el-table-column type="index" width="50" />
          <el-table-column prop="assetCode" label="资产编号" width="120" />
          <el-table-column prop="assetName" label="资产名称" min-width="150" />
          <el-table-column prop="assetType.name" label="资产类型" width="120" />
          <el-table-column prop="model" label="型号" width="120" />
          <el-table-column prop="specification" label="规格" width="120" />
          <el-table-column prop="unitName" label="所属单位" width="120" />
          <el-table-column prop="departmentName" label="所属部门" width="120" />
          <el-table-column prop="statusName" label="状态" width="100">
            <template #default="{ row }">
              <el-tag
                :type="
                  row.statusId === 1 
                    ? 'success' 
                    : row.statusId === 2 
                    ? 'warning' 
                    : row.statusId === 3 
                    ? 'info' 
                    : row.statusId === 4 
                    ? 'danger' 
                    : 'primary'
                "
              >
                {{ row.statusName }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="90" fixed="right">
            <template #default="{ $index }">
              <el-button link type="danger" @click="handleRemoveAsset($index)">移除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>

      <el-form-item label="备注说明">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注说明（选填）"
        />
      </el-form-item>

      <el-form-item label="附件">
        <FileUpload v-model="formData.attachments" />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<style lang="scss" scoped>
.idle-form {
  padding: 20px;

  .form-container {
    max-width: 1200px;
  }

  .asset-selector-wrapper {
    display: flex;
    align-items: center;
    gap: 10px;
  }
}
</style> 