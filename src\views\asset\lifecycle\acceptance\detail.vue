<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ApprovalDetail, ApprovalStatus, AcceptanceProcess, AcceptanceAssetItem } from '@/types/asset/lifecycle'
import { getAcceptanceDetail, approveProcess, rejectProcess } from '@/api/asset/lifecycle'

// 获取路由参数
const route = useRoute()
const id = route.params.id as string

// 页面状态
const loading = ref(false)
const approvalDetail = ref<ApprovalDetail>()

// 审批操作表单
const approvalForm = reactive({
  opinion: '',
  submitting: false
})

// 当前操作的节点ID
const currentNodeId = ref('')

// 获取详情数据
const getDetail = async () => {
  loading.value = true
  try {
    // 模拟获取详情数据
    setTimeout(() => {
      const process: AcceptanceProcess = {
        id,
        processNo: `YS${String(10000).padStart(5, '0')}`,
        title: '资产验收申请-示例',
        applyBy: '申请人A',
        applyTime: '2023-05-15 12:00:00',
        status: ApprovalStatus.PENDING,
        currentNode: '部门审批',
        currentHandler: '审批人B',
        createTime: '2023-05-15 12:00:00',
        updateTime: '2023-05-15 13:00:00',
        assetCount: 3,
        assetList: Array.from({ length: 3 }).map((_, index) => {
          return {
            id: `asset-${index}`,
            assetCode: `ZC${String(100000 + index).padStart(6, '0')}`,
            assetName: `测试资产${index + 1}`,
            assetType: {
              id: index % 3 === 0 ? 1 : (index % 3 === 1 ? 2 : 3),
              name: index % 3 === 0 ? '水泵设备' : (index % 3 === 1 ? '电气设备' : '办公设备')
            },
            model: `型号-${index}`,
            specification: `规格-${index}`,
            unitId: 1,
            unitName: '水投集团',
            departmentId: index % 2 === 0 ? 1 : 2,
            departmentName: index % 2 === 0 ? '运营部' : '技术部',
            statusId: 5, // 待验收
            statusName: '待验收',
            purchaseDate: `2023-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`,
            purchasePrice: Math.floor(Math.random() * 10000) + 1000,
            warrantyPeriod: `${Math.floor(Math.random() * 3) + 1}年`,
            manufacturer: `制造商-${index}`,
            supplier: `供应商-${index}`,
            location: `位置-${index}`,
            responsiblePerson: `负责人-${index}`,
            serialNumber: `SN${Date.now()}${index}`,
            acceptanceResult: index === 2 ? 'fail' : 'pass',
            acceptanceOpinion: index === 2 ? '设备不符合规格要求' : ''
          } as AcceptanceAssetItem
        }),
        attachments: [
          {
            id: 'file-1',
            name: '验收申请表.pdf',
            size: 1024 * 1024,
            url: 'https://example.com/file1.pdf',
            uploadTime: '2023-05-15 12:00:00',
            uploadBy: '申请人A'
          }
        ],
        remark: '请尽快审批，设备需要投入使用'
      }

      const nodes = [
        {
          id: 'node-1',
          name: '提交申请',
          type: 'start',
          handlers: ['申请人A'],
          handlerNames: ['申请人A']
        },
        {
          id: 'node-2',
          name: '部门审批',
          type: 'approve',
          handlers: ['审批人B'],
          handlerNames: ['审批人B']
        },
        {
          id: 'node-3',
          name: '资产管理员审批',
          type: 'approve',
          handlers: ['审批人C'],
          handlerNames: ['审批人C']
        },
        {
          id: 'node-4',
          name: '结束',
          type: 'end'
        }
      ]

      const records = [
        {
          id: 'record-1',
          processId: id,
          nodeId: 'node-1',
          nodeName: '提交申请',
          approvalBy: '申请人A',
          approvalTime: '2023-05-15 12:00:00',
          approvalType: 'approve',
          approvalResult: ApprovalStatus.APPROVED,
          approvalOpinion: '提交验收申请'
        }
      ]

      approvalDetail.value = {
        processInfo: process,
        nodes,
        records
      }

      // 设置当前节点
      currentNodeId.value = 'node-2' // 假设当前是部门审批节点
      
      loading.value = false
    }, 500)

    // 实际调用接口时使用下面的代码
    // const { data } = await getAcceptanceDetail(id)
    // approvalDetail.value = data
    // currentNodeId.value = data.nodes.find(node => node.name === data.processInfo.currentNode)?.id || ''
  } catch (error) {
    console.error('获取验收详情失败', error)
    ElMessage.error('获取验收详情失败')
  } finally {
    loading.value = false
  }
}

// 审批通过
const handleApprove = async () => {
  if (!approvalDetail.value || !currentNodeId.value) return
  if (!approvalForm.opinion.trim()) {
    ElMessage.warning('请输入审批意见')
    return
  }

  approvalForm.submitting = true
  try {
    // 实际调用接口时使用下面的代码
    // await approveProcess(id, currentNodeId.value, approvalForm.opinion)
    
    // 模拟API调用
    setTimeout(() => {
      ElMessage.success('审批通过成功')
      getDetail() // 重新加载详情
      approvalForm.opinion = ''
    }, 500)
  } catch (error) {
    console.error('审批操作失败', error)
    ElMessage.error('审批操作失败')
  } finally {
    approvalForm.submitting = false
  }
}

// 审批拒绝
const handleReject = async () => {
  if (!approvalDetail.value || !currentNodeId.value) return
  if (!approvalForm.opinion.trim()) {
    ElMessage.warning('请输入拒绝理由')
    return
  }

  ElMessageBox.confirm('确定要拒绝该验收申请吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      approvalForm.submitting = true
      try {
        // 实际调用接口时使用下面的代码
        // await rejectProcess(id, currentNodeId.value, approvalForm.opinion)
        
        // 模拟API调用
        setTimeout(() => {
          ElMessage.success('已拒绝该验收申请')
          getDetail() // 重新加载详情
          approvalForm.opinion = ''
        }, 500)
      } catch (error) {
        console.error('拒绝操作失败', error)
        ElMessage.error('拒绝操作失败')
      } finally {
        approvalForm.submitting = false
      }
    })
    .catch(() => {})
}

// 获取资产验收结果样式
const getAcceptanceResultType = (result?: string) => {
  if (!result) return ''
  return result === 'pass' ? 'success' : 'danger'
}

// 获取资产验收结果文本
const getAcceptanceResultText = (result?: string) => {
  if (!result) return '未设置'
  return result === 'pass' ? '合格' : '不合格'
}

// 获取状态类型
const getStatusType = (status: ApprovalStatus) => {
  switch (status) {
    case ApprovalStatus.APPROVED:
      return 'success'
    case ApprovalStatus.REJECTED:
      return 'danger'
    case ApprovalStatus.PENDING:
      return 'warning'
    case ApprovalStatus.CANCELED:
      return 'info'
    default:
      return 'primary'
  }
}

// 获取状态文本
const getStatusText = (status: ApprovalStatus) => {
  switch (status) {
    case ApprovalStatus.APPROVED:
      return '已批准'
    case ApprovalStatus.REJECTED:
      return '已拒绝'
    case ApprovalStatus.PENDING:
      return '待审批'
    case ApprovalStatus.CANCELED:
      return '已取消'
    case ApprovalStatus.PROCESSING:
      return '审批中'
    default:
      return '未知状态'
  }
}

// 下载附件
const downloadAttachment = (url: string, fileName: string) => {
  // 实际项目中根据后端接口和文件存储方式实现下载逻辑
  window.open(url)
}

// 组件挂载时加载详情
onMounted(() => {
  getDetail()
})
</script>

<template>
  <div class="acceptance-detail" v-loading="loading">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>资产验收详情</span>
          <el-button @click="$router.back()">返回</el-button>
        </div>
      </template>

      <div v-if="approvalDetail" class="detail-content">
        <!-- 基本信息 -->
        <el-descriptions title="基本信息" :column="2" border>
          <el-descriptions-item label="流程编号">
            {{ approvalDetail.processInfo.processNo }}
          </el-descriptions-item>
          <el-descriptions-item label="标题">
            {{ approvalDetail.processInfo.title }}
          </el-descriptions-item>
          <el-descriptions-item label="申请人">
            {{ approvalDetail.processInfo.applyBy }}
          </el-descriptions-item>
          <el-descriptions-item label="申请时间">
            {{ approvalDetail.processInfo.applyTime }}
          </el-descriptions-item>
          <el-descriptions-item label="当前节点">
            {{ approvalDetail.processInfo.currentNode }}
          </el-descriptions-item>
          <el-descriptions-item label="当前处理人">
            {{ approvalDetail.processInfo.currentHandler }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(approvalDetail.processInfo.status)">
              {{ getStatusText(approvalDetail.processInfo.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="资产数量">
            {{ approvalDetail.processInfo.assetCount }}
          </el-descriptions-item>
          <el-descriptions-item label="备注说明" :span="2">
            {{ approvalDetail.processInfo.remark || '无' }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 流程步骤 -->
        <div class="process-steps">
          <h3>流程步骤</h3>
          <el-steps
            :active="approvalDetail.records.length"
            finish-status="success"
            simple
            style="margin-top: 20px"
          >
            <el-step
              v-for="node in approvalDetail.nodes"
              :key="node.id"
              :title="node.name"
              :description="node.handlerNames?.join(', ') || ''"
            />
          </el-steps>
        </div>

        <!-- 资产列表 -->
        <div class="asset-list">
          <h3>验收资产列表</h3>
          <el-table :data="approvalDetail.processInfo.assetList" border style="width: 100%">
            <el-table-column type="index" width="50" />
            <el-table-column prop="assetCode" label="资产编号" width="120" />
            <el-table-column prop="assetName" label="资产名称" min-width="150" />
            <el-table-column prop="assetType.name" label="资产类型" width="120" />
            <el-table-column prop="model" label="型号" width="120" />
            <el-table-column prop="specification" label="规格" width="120" />
            <el-table-column prop="purchasePrice" label="采购价格" width="120">
              <template #default="{ row }">
                {{ row.purchasePrice?.toFixed(2) }} 元
              </template>
            </el-table-column>
            <el-table-column prop="acceptanceResult" label="验收结果" width="100">
              <template #default="{ row }">
                <el-tag :type="getAcceptanceResultType(row.acceptanceResult)">
                  {{ getAcceptanceResultText(row.acceptanceResult) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="acceptanceOpinion" label="验收意见" min-width="200" show-overflow-tooltip />
          </el-table>
        </div>

        <!-- 附件列表 -->
        <div class="attachment-list" v-if="approvalDetail.processInfo.attachments?.length">
          <h3>附件列表</h3>
          <el-table :data="approvalDetail.processInfo.attachments" border style="width: 100%">
            <el-table-column prop="name" label="文件名" min-width="200" />
            <el-table-column prop="size" label="大小" width="120">
              <template #default="{ row }">
                {{ (row.size / 1024 / 1024).toFixed(2) }} MB
              </template>
            </el-table-column>
            <el-table-column prop="uploadBy" label="上传人" width="120" />
            <el-table-column prop="uploadTime" label="上传时间" width="160" />
            <el-table-column label="操作" width="120" fixed="right">
              <template #default="{ row }">
                <el-button
                  link
                  type="primary"
                  @click="downloadAttachment(row.url, row.name)"
                >
                  下载
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 审批记录 -->
        <div class="approval-records">
          <h3>审批记录</h3>
          <el-table :data="approvalDetail.records" border style="width: 100%">
            <el-table-column prop="nodeName" label="节点名称" width="150" />
            <el-table-column prop="approvalBy" label="审批人" width="120" />
            <el-table-column prop="approvalTime" label="审批时间" width="160" />
            <el-table-column prop="approvalType" label="操作类型" width="120">
              <template #default="{ row }">
                <el-tag :type="row.approvalType === 'approve' ? 'success' : row.approvalType === 'reject' ? 'danger' : 'info'">
                  {{ row.approvalType === 'approve' ? '同意' : row.approvalType === 'reject' ? '拒绝' : row.approvalType === 'transfer' ? '转交' : '评论' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="approvalOpinion" label="审批意见" min-width="200" show-overflow-tooltip />
          </el-table>
        </div>

        <!-- 审批操作 -->
        <div
          class="approval-actions"
          v-if="
            approvalDetail.processInfo.status === ApprovalStatus.PENDING ||
            approvalDetail.processInfo.status === ApprovalStatus.PROCESSING
          "
        >
          <h3>审批操作</h3>
          <el-form :model="approvalForm" label-width="100px">
            <el-form-item label="审批意见" required>
              <el-input
                v-model="approvalForm.opinion"
                type="textarea"
                :rows="3"
                placeholder="请输入您的审批意见"
              />
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                @click="handleApprove"
                :loading="approvalForm.submitting"
              >
                同意
              </el-button>
              <el-button
                type="danger"
                @click="handleReject"
                :loading="approvalForm.submitting"
              >
                拒绝
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <el-empty v-else description="暂无数据" />
    </el-card>
  </div>
</template>

<style lang="scss" scoped>
.acceptance-detail {
  padding: 20px 0;

  .box-card {
    margin-bottom: 20px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .detail-content {
    display: flex;
    flex-direction: column;
    gap: 30px;

    h3 {
      margin-top: 0;
      margin-bottom: 15px;
      font-weight: 500;
      color: #303133;
      border-left: 4px solid #409eff;
      padding-left: 10px;
    }

    .process-steps,
    .asset-list,
    .attachment-list,
    .approval-records,
    .approval-actions {
      margin-top: 20px;
    }
  }
}
</style> 