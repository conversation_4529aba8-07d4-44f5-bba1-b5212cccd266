import request from '@/config/axios'

// 查询申请类型列表
export const listApplyTypes = (params: any) => {
  return request.get({ url: '/api/asset/apply-type/list', params })
}

// 查询申请类型详细
export const getApplyType = (id: number) => {
  return request.get({ url: `/api/asset/apply-type/${id}` })
}

// 新增申请类型
export const addApplyType = (data: any) => {
  return request.post({ url: '/api/asset/apply-type', data })
}

// 修改申请类型
export const updateApplyType = (data: any) => {
  return request.put({ url: '/api/asset/apply-type', data })
}

// 删除申请类型
export const deleteApplyType = (id: number) => {
  return request.delete({ url: `/api/asset/apply-type/${id}` })
}

// 批量删除申请类型
export const batchDeleteApplyType = (ids: number[]) => {
  return request.delete({ url: '/api/asset/apply-type/batch', data: ids })
}

// 修改申请类型状态
export const changeApplyTypeStatus = (id: number, status: string) => {
  return request.put({ url: `/api/asset/apply-type/status`, data: { id, status } })
}

// 获取资产分类列表
export const getAssetCategories = () => {
  return request.get({ url: '/api/asset/category/list' })
} 