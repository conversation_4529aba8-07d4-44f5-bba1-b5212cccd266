<template>
  <el-card class="application-type-card" :body-style="{ padding: '20px' }">
    <div class="card-header">
      <h3 class="title">{{ data.name }}</h3>
      <el-tag :type="data.enabled ? 'success' : 'info'" size="small">
        {{ data.enabled ? '启用' : '停用' }}
      </el-tag>
    </div>
    <div class="card-content">
      <div class="info-item">
        <span class="label">编码：</span>
        <span class="value">{{ data.code }}</span>
      </div>
      <div class="info-item">
        <span class="label">资产类型：</span>
        <el-tag size="small">{{ data.assetTypeName }}</el-tag>
      </div>
      <div class="description">
        {{ data.description }}
      </div>
    </div>
    <div class="card-footer">
      <el-button type="primary" link @click="handleView">查看</el-button>
      <el-button type="primary" link @click="handleEdit">编辑</el-button>
      <el-button type="danger" link @click="handleDelete">删除</el-button>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ElMessageBox } from 'element-plus'

const props = defineProps<{
  data: {
    id: string
    name: string
    code: string
    assetTypeName: string
    enabled: boolean
    description: string
  }
}>()

const emit = defineEmits<{
  (e: 'view', id: string): void
  (e: 'edit', id: string): void
  (e: 'delete', id: string): void
}>()

const handleView = () => {
  emit('view', props.data.id)
}

const handleEdit = () => {
  emit('edit', props.data.id)
}

const handleDelete = () => {
  ElMessageBox.confirm('确认删除该申请类型吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    emit('delete', props.data.id)
  })
}
</script>

<style scoped>
.application-type-card {
  margin-bottom: 20px;
  transition: all 0.3s;
}

.application-type-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.title {
  margin: 0;
  font-size: 16px;
  font-weight: bold;
}

.card-content {
  margin-bottom: 15px;
}

.info-item {
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.label {
  color: #606266;
  margin-right: 8px;
}

.value {
  color: #303133;
}

.description {
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
  margin-top: 10px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.card-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style> 