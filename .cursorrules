# 技术栈
- 使用 Vue 3 框架，开发语言为 TypeScript
- UI 框架为 Element Plus，组件使用 script setup + Composition API 风格
- 使用 SCSS 作为样式语言，样式建议使用 scoped
- 使用 Axios 进行 API 调用，API 风格为 RESTful

# 项目结构
- 页面文件放置在 src/views 目录中
- 通用组件放置在 src/components 中
- 接口请求文件放置在 src/api 中,API中需要引入`import request from '@/config/axios'`，并且按照封装的请求方法发送请求
- 数据类型定义放置在 src/types 中
- 工具函数放置在 src/utils 中

# 表单与交互
- 所有表单应启用表单校验规则
- 表单建议以 Drawer 或 Dialog 弹窗形式呈现
- 表单提交调用对应 API 接口（使用 Axios）
- 表格应支持分页、搜索、重置操作
- 搜索区域应支持展开/收起高级搜索项
- 所有下拉、状态类字段应统一使用 Tag 或 Badge 显示效果
- 列表操作建议使用“操作列 + 操作按钮组”

# Mock 数据
- 生成页面时务必加入高质量 mock 数据（使用 faker 或模拟 JSON）
- 模拟字段包括资产编号、名称、部门、状态、创建人、日期等常用字段
- 表格默认加载 mock 数据（不为空），支持分页切换

# 页面形式
- 默认列表展示形式为表格，但鼓励根据业务使用卡片视图、标签页视图或统计视图
- 一定要以用户的角度出发，考虑界面的设计，对于某项业务，尽量采用业界主流展示方式
- 页面支持“新增”、“编辑”、“删除”、“详情”操作
- 详情页可使用 Drawer 或 Dialog 呈现，不一定需要单独页面
- 鼓励在页面顶部区域添加摘要统计信息（例如：数量统计、资产总值等）

# 命名规范
- 文件命名使用 kebab-case
- 组件命名使用 PascalCase
- 路由命名使用 camelCase

# 权限处理
- 所有权限由后端控制，前端无需编写权限控制逻辑（如 v-if 等）

# 功能需求

 企业资产管理系统功能清单（适用于水投集团）

模块

子模块

功能点

详细功能说明

一、资产分类与基础信息管理

资产类型管理

区分生产类与非生产类资产

支持表格展示全部资产类型，表格中包括类型名称、编码、描述、创建人、创建时间。支持添加、编辑、删除操作，仅管理员可执行。新增或编辑时使用弹出表单，输入名称、编码、描述等字段。



资产属性模板

支持字段模板配置

每种资产类型支持设置字段模板，如水泵需要“安装位置、规格”，电脑需要“品牌、型号”。使用表格展示模板字段列表，新增字段时用弹窗形式配置字段名、字段类型（文本/下拉/日期等）、是否必填。



使用单位设置

设置资产归属单位

使用树形结构展示单位结构，如“水投集团 > XX水厂 > XX部门”，支持多级单位维护。用于后续资产归属、查询过滤、权限管理。

| 二、资产台账管理 | 台账信息录入 | 手动/导入方式添加资产 | 表格展示资产台账，字段包括资产编号、名称、型号、所属单位、状态、采购日期等。支持Excel模板导入历史数据，导入时校验字段格式与必填项，错误数据提示。 |
|  | 卡片打印 | 标签二维码打印 | 支持为每个资产生成二维码并打印卡片，卡片内容包括资产名称、编号、单位、负责人、二维码等，使用A4模板或热敏纸格式打印。 |
|  | 资产列表视图 | 多种展示方式 | 支持列表视图（表格）、卡片视图（图文块）、详情页视图。支持自定义列展示、字段筛选、排序。 |
|  | 导出功能 | 自定义字段导出 | 在资产列表页中提供“导出”按钮，弹出多选字段窗口，支持按Excel格式导出所选字段内容。 |

| 三、资产全生命周期管理 | 资产验收 | 验收申请、审批、入库 | 提交验收申请时填写资产信息（表单填写），自动生成待审批任务，审批通过后状态变为“在用”，入账台账。验收流程支持短信或系统消息提醒。 |
|  | 资产调拨 | 单位/部门间调拨申请 | 提交调拨申请表单，选择原使用单位、目标单位（树形选择），填写调拨原因与资产清单（资产多选）。审批通过后自动更新资产归属单位并记录调拨历史。 |
|  | 资产闲置与回转 | 闲置处理、回转启用 | 闲置：填写闲置申请表单，说明原因并审批。回转：从闲置资产中选择资产提交启用申请。状态自动变更。支持台账中通过操作按钮发起流程。 |
|  | 报废与处置 | 报废申请、审批、处置登记 | 报废申请支持资产多选，填写退役原因，审批通过后变为“报废”状态。可登记处理方式、残值、转让信息等。表格展示处置记录，支持导出。 |
|  | 资产变更 | 信息变更与日志 | 支持变更使用人、安装位置、所属单位等，发起变更申请（弹出表单），流程走审批流。变更完成后台账自动更新，所有变更记录保留，支持对比展示“变更前/变更后”。 |

| 四、资产维修与维保管理 | 维修申请 | 故障上报、审批、维修派工 | 用户在资产详情页点击“报修”，弹出故障填写窗口，填写故障描述、拍照上传等。管理员审批后分配维修人员。维修任务可推送至移动端。 |
|  | 维修验收 | 维修单验收操作 | 维修完成后由验收人员在系统中填写验收意见，结果合格/不合格，支持退回维修。表格展示待验收任务列表。 |
|  | 维修确认 | 确认费用并入账 | 验收后，维修负责人填写维修费用与配件使用情况，确认后不能更改关键字段。支持将成本纳入资产运营费用报表。 |
|  | 维修记录管理 | 查询历史维修记录 | 资产详情页展示维修历史列表，包括时间、问题、处理人、耗材等。支持附件上传，如维修照片、检测报告。 |
|  | 维修统计报表 | 多维度统计报表 | 提供月度/季度/年度维修分析报表，图表展示维修数量、成本、平均响应时间等。支持Excel导出。 |
|  | 维保计划管理 | 计划任务、执行提醒 | 支持为设备设定维保周期，自动生成维保任务，提醒相关人员执行。维保计划可按设备类别批量配置。 |

| 五、权限与角色管理 | 权限控制 | 权限分级分组 | 支持按单位、资产类型、角色配置查看/操作权限。使用权限树结构配置界面。支持只读、可编辑、审批权限等设置。 |
|  | 管理员配置 | 设置不同级别管理员 | 可为各单位、资产类型配置负责人，统一管理其范围内资产和审批任务。支持通过表格维护。 |

| 六、查询与报表分析 | 多条件查询 | 灵活搜索 | 查询条件支持资产名称、编号、单位、状态、类型、时间等，支持组合查询。页面展示结果使用分页表格，提供导出功能。 |
|  | 查询视图 | 多样化展示 | 查询结果可切换为表格视图、卡片视图，未来支持GIS地图展示。视图切换按钮位于查询结果上方。 |
|  | 报表导出 | 灵活输出 | 所有查询结果、统计报表均支持按字段选择导出为Excel/PDF格式。导出模板可配置。 |

| 七、我的资产与部门资产 | 我的资产 | 当前用户资产展示 | 登录用户自动筛选其名下资产，展示为表格列表，支持导出、打印。支持分类排序，如按状态、类型、单位分组。 |
|  | 部门资产 | 展示部门资产 | 部门负责人可查看本部门所有资产，支持台账下载。展示方式为表格列表，字段与主台账一致。 |

| 八、资产补录与导入导出 | 批量导入 | 历史资产补录 | 提供Excel模板下载，支持批量导入固定资产与行政资产，导入时校验格式并提示错误。 |
|  | 批量导出 | 自定义格式导出 | 支持选择导出字段与排序方式，生成Excel文件。配合资产盘点使用。 |

| 九、流程审批引擎 | 流程配置 | 自定义审批流设计 | 提供流程图形化设计器，支持串行、并行、条件节点配置。用于验收、调拨、报废、变更等流程。 |
|  | 流程管理 | 查看流程状态与日志 | 每笔流程可查看审批记录、当前节点与办理人、流转时间等，支持流程退回与催办功能。 |

| 十、扩展功能（可选） | 资产盘点 | 盘点计划与扫码盘点 | 支持生成盘点计划，盘点人员通过移动端扫码盘点，系统自动比对差异并生成盘点报告。 |
|  | 资产位置管理 | 区域/楼层分布管理 | 在资产录入时支持选择所在位置（楼宇-楼层-房间），未来可结合GIS展示。 |
|  | 二维码管理 | 唯一码管理与扫码查询 | 每个资产自动生成二维码，可扫码查询资产详情、发起维修或盘点。支持打印二维码标签。



# 其他建议
- 鼓励使用业务组件抽象（如 AssetForm.vue, AssetTable.vue 等）
- 所有模块页面建议保留页面级注释（功能简介、模块职责）
- 鼓励设计用户操作链路的逻辑完整性，不做功能堆叠
