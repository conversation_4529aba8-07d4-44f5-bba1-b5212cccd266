<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { AcceptanceForm as IAcceptanceForm, AcceptanceAssetItem, AttachmentInfo } from '@/types/asset/lifecycle'
import { createAcceptance } from '@/api/asset/lifecycle'
import AssetSelector from '@/components/AssetSelector/index.vue'
import FileUpload from '@/components/FileUpload/index.vue'

// 表单引用
const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive<IAcceptanceForm>({
  title: '',
  assetList: [],
  remark: '',
  attachments: []
})

// 资产列表显示更多控制
const showMoreColumns = ref(false)
const toggleMoreColumns = () => {
  showMoreColumns.value = !showMoreColumns.value
}

// 表单验证规则
const formRules = reactive<FormRules>({
  title: [
    { required: true, message: '请输入验收标题', trigger: 'blur' },
    { min: 3, max: 100, message: '长度在 3 到 100 个字符', trigger: 'blur' }
  ],
  assetList: [
    { required: true, message: '请选择需要验收的资产', trigger: 'change' },
    { type: 'array', min: 1, message: '至少选择一项资产', trigger: 'change' }
  ]
})

// 资产选择器引用
const assetSelectorRef = ref()

// 选择资产
const handleSelectAsset = () => {
  assetSelectorRef.value?.open()
}

// 移除资产
const handleRemoveAsset = (index: number) => {
  formData.assetList.splice(index, 1)
}

// 验收结果变更
const handleAcceptanceResultChange = (result: string, index: number) => {
  if (result === 'fail' && !formData.assetList[index].acceptanceOpinion) {
    formData.assetList[index].acceptanceOpinion = '不合格原因' // 默认提示文本
  }
}

// 重置表单
const resetForm = () => {
  if (!formRef.value) return
  formRef.value.resetFields()
  formData.assetList = []
  formData.remark = ''
  formData.attachments = []
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid, fields) => {
    if (valid) {
      try {
        // 检查是否有未设置验收结果的资产
        const noResultAsset = formData.assetList.find(asset => !asset.acceptanceResult)
        if (noResultAsset) {
          ElMessage.warning('请为所有资产设置验收结果')
          return
        }
        
        // 模拟API调用
        setTimeout(() => {
          // 实际调用接口时使用下面的代码
          // await createAcceptance(formData)
          ElMessage.success('验收申请提交成功')
          emit('submit')
        }, 500)
      } catch (error) {
        console.error('提交验收申请失败', error)
        ElMessage.error('提交验收申请失败')
      }
    } else {
      console.log('验证失败', fields)
    }
  })
}

// 事件发射
const emit = defineEmits(['submit'])

// 提供重置方法给父组件
defineExpose({
  resetForm
})
</script>

<template>
  <div class="acceptance-form">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      class="form-container"
    >
      <el-form-item label="验收标题" prop="title">
        <el-input v-model="formData.title" placeholder="请输入验收标题" />
      </el-form-item>

      <el-form-item label="验收资产" prop="assetList">
        <div class="asset-selector-wrapper">
          <AssetSelector
            ref="assetSelectorRef"
            v-model="formData.assetList"
            :filter-by-status="[5]" 
            title="选择待验收资产"
          />
          <!-- 假设状态 5 表示"待验收" -->
        </div>
      </el-form-item>

      <!-- 已选资产列表 -->
      <el-form-item v-if="formData.assetList.length > 0" label="验收清单">
        <el-table :data="formData.assetList" border style="width: 100%" max-height="400">
          <el-table-column type="index" width="50" />
          <el-table-column prop="assetCode" label="资产编号" width="120" />
          <el-table-column prop="assetName" label="资产名称" min-width="150" />
          <el-table-column prop="assetType.name" label="资产类型" width="120" />
          <el-table-column prop="model" label="型号" width="120" v-if="showMoreColumns" />
          <el-table-column prop="specification" label="规格" width="120" v-if="showMoreColumns" />
          <el-table-column prop="unitName" label="所属单位" width="120" v-if="showMoreColumns" />

          <el-table-column label="验收结果" width="160">
            <template #default="{ row, $index }">
              <el-radio-group 
                v-model="row.acceptanceResult" 
                @change="(val) => handleAcceptanceResultChange(val, $index)"
              >
                <el-radio label="pass">合格</el-radio>
                <el-radio label="fail">不合格</el-radio>
              </el-radio-group>
            </template>
          </el-table-column>

          <el-table-column label="验收意见" min-width="200">
            <template #default="{ row }">
              <el-input 
                v-model="row.acceptanceOpinion" 
                type="textarea" 
                :rows="1" 
                placeholder="请输入验收意见"
                :disabled="row.acceptanceResult === 'pass'"
              />
            </template>
          </el-table-column>

          <el-table-column label="操作" width="90" fixed="right">
            <template #default="{ $index }">
              <el-button link type="danger" @click="handleRemoveAsset($index)">移除</el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <div class="table-footer">
          <el-button type="text" @click="toggleMoreColumns">
            {{ showMoreColumns ? '隐藏详细信息' : '显示详细信息' }}
            <el-icon>
              <component :is="showMoreColumns ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" />
            </el-icon>
          </el-button>
        </div>
      </el-form-item>

      <el-form-item label="备注说明">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注说明（选填）"
        />
      </el-form-item>

      <el-form-item label="附件">
        <FileUpload v-model="formData.attachments" />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<style lang="scss" scoped>
.acceptance-form {
  padding: 20px;

  .form-container {
    max-width: 1200px;
  }

  .asset-selector-wrapper {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .table-footer {
    margin-top: 10px;
    display: flex;
    justify-content: center;
  }
}
</style> 