<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'

// 定义单位树节点类型
interface UnitTreeNode {
  id: number;
  name: string;
  children?: UnitTreeNode[];
  isLeaf?: boolean;
}

const props = defineProps({
  modelValue: {
    type: [Number, String],
    default: undefined
  },
  placeholder: {
    type: String,
    default: '请选择单位'
  },
  disabled: {
    type: Boolean,
    default: false
  },
  clearable: {
    type: Boolean,
    default: true
  },
  showDepartment: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

// 加载状态
const loading = ref(false)

// 本地响应式值，用于绑定到 el-tree-select
const localValue = ref(props.modelValue)

// 单位树数据
const unitTreeData = ref<UnitTreeNode[]>([])

// 生成模拟单位和部门树数据
const generateMockUnitTree = () => {
  // 水投集团及其子单位
  const waterInvestmentGroup: UnitTreeNode = {
    id: 1,
    name: '水投集团',
    children: []
  }

  // 添加水厂
  for (let i = 1; i <= 3; i++) {
    const waterPlant: UnitTreeNode = {
      id: 10 + i,
      name: `第${i}水厂`,
      children: []
    }

    // 添加部门（如果需要展示部门）
    if (props.showDepartment) {
      const departments: UnitTreeNode[] = [
        {
          id: 100 + i * 10 + 1,
          name: '运营部',
          isLeaf: true
        },
        {
          id: 100 + i * 10 + 2,
          name: '技术部',
          isLeaf: true
        },
        {
          id: 100 + i * 10 + 3,
          name: '行政部',
          isLeaf: true
        }
      ]
      waterPlant.children!.push(...departments)
    } else {
      waterPlant.isLeaf = true
    }

    waterInvestmentGroup.children!.push(waterPlant)
  }

  // 添加水务分公司
  for (let i = 1; i <= 2; i++) {
    const waterBranch: UnitTreeNode = {
      id: 20 + i,
      name: `水务分公司${i}`,
      children: []
    }

    // 添加部门（如果需要展示部门）
    if (props.showDepartment) {
      const departments: UnitTreeNode[] = [
        {
          id: 200 + i * 10 + 1,
          name: '运营部',
          isLeaf: true
        },
        {
          id: 200 + i * 10 + 2,
          name: '技术部',
          isLeaf: true
        }
      ]
      waterBranch.children!.push(...departments)
    } else {
      waterBranch.isLeaf = true
    }

    waterInvestmentGroup.children!.push(waterBranch)
  }

  // 集团总部部门
  if (props.showDepartment) {
    const headquarters: UnitTreeNode[] = [
      {
        id: 1001,
        name: '总经办',
        isLeaf: true
      },
      {
        id: 1002,
        name: '财务部',
        isLeaf: true
      },
      {
        id: 1003,
        name: '人力资源部',
        isLeaf: true
      },
      {
        id: 1004,
        name: '信息技术部',
        isLeaf: true
      }
    ]
    waterInvestmentGroup.children!.push(...headquarters)
  }

  return [waterInvestmentGroup]
}

// 初始化获取单位树数据
const getUnitTreeData = async () => {
  loading.value = true
  try {
    // 模拟API请求延迟
    setTimeout(() => {
      unitTreeData.value = generateMockUnitTree()
      loading.value = false
    }, 500)
  } catch (error) {
    console.error('获取单位树数据失败', error)
    ElMessage.error('获取单位树数据失败')
    loading.value = false
  }
}

// 选择处理
const handleSelect = (value: number) => {
  localValue.value = value
  emit('update:modelValue', value)
  emit('change', value)
}

// 监听 props.modelValue 变化，更新本地值
watch(
  () => props.modelValue,
  (newVal) => {
    localValue.value = newVal
  }
)

// 组件挂载时获取数据
onMounted(() => {
  getUnitTreeData()
})
</script>

<template>
  <div class="unit-tree-select">
    <el-tree-select
      v-model="localValue"
      :data="unitTreeData"
      :props="{
        label: 'name',
        value: 'id',
        children: 'children'
      }"
      check-strictly
      node-key="id"
      :placeholder="placeholder"
      :disabled="disabled"
      :clearable="clearable"
      @change="handleSelect"
    />
  </div>
</template>

<style lang="scss" scoped>
.unit-tree-select {
  width: 100%;

  :deep(.el-tree-select) {
    width: 100%;
  }
}
</style> 