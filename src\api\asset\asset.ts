import request from '@/config/axios'
import type { QueryParams } from '@/types/asset'

// 查询资产列表
export const getAssetList = (params: QueryParams) => {
  return request.get({ url: '/api/asset/list', params })
}

// 查询资产详情
export const getAssetDetail = (id: string) => {
  return request.get({ url: `/api/asset/${id}` })
}

// 新增资产
export const addAsset = (data: any) => {
  return request.post({ url: '/api/asset', data })
}

// 修改资产
export const updateAsset = (data: any) => {
  return request.put({ url: '/api/asset', data })
}

// 删除资产
export const deleteAsset = (id: string) => {
  return request.delete({ url: `/api/asset/${id}` })
}

// 批量删除资产
export const batchDeleteAsset = (ids: string[]) => {
  return request.delete({ url: '/api/asset/batch', data: { ids } })
}

// 导出资产
export const exportAsset = (params: any) => {
  return request.download({ url: '/api/asset/export', params })
}

// 下载导入模板
export const downloadImportTemplate = () => {
  return request.download({ url: '/api/asset/import/template' })
}

// 预览导入数据
export const previewImportData = (data: FormData) => {
  return request.post({ 
    url: '/api/asset/import/preview', 
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 确认导入数据
export const confirmImport = (data: any) => {
  return request.post({ url: '/api/asset/import/confirm', data })
}

// 生成资产二维码
export const generateQrCode = (id: string) => {
  return request.get({ url: `/api/asset/qrcode/${id}` })
}

// 批量生成二维码（用于打印）
export const batchGenerateQrCode = (ids: string[]) => {
  return request.post({ url: '/api/asset/qrcode/batch', data: { ids } })
} 