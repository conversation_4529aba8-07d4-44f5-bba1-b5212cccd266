<template>
  <el-button
    type="text"
    icon="Tickets"
    @click="gotoAttributeTemplate(row)"
  >属性模板</el-button>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { defineProps, defineEmits } from 'vue'

const props = defineProps({
  row: {
    type: Object,
    required: true
  }
})

const router = useRouter()

// 跳转到属性模板页面
const gotoAttributeTemplate = (row: any) => {
  router.push({
    path: '/asset/config/template',
    query: {
      assetTypeId: row.id
    }
  })
}
</script> 