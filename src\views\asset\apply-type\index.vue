<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch">
      <el-form-item label="申请类型名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入申请类型名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="类型编码" prop="code">
        <el-input
          v-model="queryParams.code"
          placeholder="请输入类型编码"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['asset:apply-type:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['asset:apply-type:remove']"
        >删除</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @query-table="getList" />
    </el-row>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="applyTypeList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="申请类型名称" align="center" prop="name" />
      <el-table-column label="类型编码" align="center" prop="code" />
      <el-table-column label="适用资产分类" align="center" prop="categories">
        <template #default="scope">
          <el-tag
            v-for="category in scope.row.categories"
            :key="category"
            style="margin-right: 5px"
          >{{ category }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="审批模式" align="center" prop="approvalMode">
        <template #default="scope">
          <dict-tag :options="approvalModeOptions" :value="scope.row.approvalMode" />
        </template>
      </el-table-column>
      <el-table-column label="启用状态" align="center" prop="status">
        <template #default="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="'1'"
            :inactive-value="'0'"
            @change="handleStatusChange(scope.row)"
            v-hasPermi="['asset:apply-type:edit']"
          />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            type="text"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['asset:apply-type:edit']"
          >修改</el-button>
          <el-button
            type="text"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['asset:apply-type:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改申请类型对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="applyTypeRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="申请类型名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入申请类型名称" />
        </el-form-item>
        <el-form-item label="类型编码" prop="code">
          <el-input v-model="form.code" placeholder="请输入类型编码" />
        </el-form-item>
        <el-form-item label="适用资产分类" prop="categories">
          <el-select
            v-model="form.categories"
            multiple
            placeholder="请选择适用资产分类"
            style="width: 100%"
          >
            <el-option
              v-for="item in assetCategories"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="审批模式" prop="approvalMode">
          <el-select v-model="form.approvalMode" placeholder="请选择审批模式" style="width: 100%">
            <el-option
              v-for="dict in approvalModeOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="条件字段" prop="conditionField" v-if="form.approvalMode === '3'">
          <el-input v-model="form.conditionField" placeholder="请输入条件字段" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入描述" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  listApplyTypes,
  getApplyType,
  addApplyType,
  updateApplyType,
  deleteApplyType,
  batchDeleteApplyType,
  changeApplyTypeStatus,
  getAssetCategories
} from '@/api/asset/applyType'

// 审批模式选项
const approvalModeOptions = ref([
  { value: '1', label: '串行' },
  { value: '2', label: '并行' },
  { value: '3', label: '条件' }
])

// 资产分类选项
const assetCategories = ref([
  { value: 'PRODUCTION', label: '生产类资产' },
  { value: 'ADMIN', label: '行政类资产' },
  { value: 'FRANCHISE', label: '特许资产' }
])

const loading = ref(true)
const showSearch = ref(true)
const total = ref(0)
const open = ref(false)
const title = ref('')
const applyTypeList = ref([])
const ids = ref<number[]>([])
const single = ref(true)
const multiple = ref(true)

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  name: undefined,
  code: undefined
})

// 表单参数
const form = reactive({
  id: undefined,
  name: undefined,
  code: undefined,
  categories: [],
  approvalMode: undefined,
  conditionField: undefined,
  description: undefined,
  status: '1'
})

// 表单校验规则
const rules = reactive({
  name: [{ required: true, message: '申请类型名称不能为空', trigger: 'blur' }],
  code: [{ required: true, message: '类型编码不能为空', trigger: 'blur' }],
  categories: [{ required: true, message: '适用资产分类不能为空', trigger: 'change' }],
  approvalMode: [{ required: true, message: '审批模式不能为空', trigger: 'change' }],
  conditionField: [{ required: true, message: '条件字段不能为空', trigger: 'blur' }]
})

/** 查询申请类型列表 */
const getList = async () => {
  loading.value = true
  try {
    const res = await listApplyTypes(queryParams)
    applyTypeList.value = res.rows
    total.value = res.total
  } finally {
    loading.value = false
  }
}

/** 取消按钮 */
const cancel = () => {
  open.value = false
  reset()
}

/** 表单重置 */
const reset = () => {
  form.id = undefined
  form.name = undefined
  form.code = undefined
  form.categories = []
  form.approvalMode = undefined
  form.conditionField = undefined
  form.description = undefined
  form.status = '1'
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryParams.name = undefined
  queryParams.code = undefined
  handleQuery()
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset()
  open.value = true
  title.value = '添加申请类型'
}

/** 修改按钮操作 */
const handleUpdate = (row: any) => {
  reset()
  const id = row.id
  form.id = id
  form.name = row.name
  form.code = row.code
  form.categories = row.categories
  form.approvalMode = row.approvalMode
  form.conditionField = row.conditionField
  form.description = row.description
  form.status = row.status
  open.value = true
  title.value = '修改申请类型'
}

/** 提交按钮 */
const submitForm = async () => {
  const applyTypeRef = ref()
  await applyTypeRef.value.validate()
  if (form.id) {
    await updateApplyType(form)
    ElMessage.success('修改成功')
  } else {
    await addApplyType(form)
    ElMessage.success('新增成功')
  }
  open.value = false
  getList()
}

/** 删除按钮操作 */
const handleDelete = (row: any) => {
  const delIds = row.id || ids.value
  ElMessageBox.confirm('是否确认删除申请类型编号为"' + delIds + '"的数据项?', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    if (row.id) {
      await deleteApplyType(row.id)
    } else {
      await batchDeleteApplyType(delIds)
    }
    getList()
    ElMessage.success('删除成功')
  })
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: any[]) => {
  ids.value = selection.map(item => item.id)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

/** 状态修改 */
const handleStatusChange = async (row: any) => {
  const text = row.status === '1' ? '启用' : '停用'
  try {
    await changeApplyTypeStatus(row.id, row.status)
    ElMessage.success(text + '成功')
  } catch (error) {
    row.status = row.status === '1' ? '0' : '1'
  }
}

onMounted(() => {
  getList()
})
</script> 