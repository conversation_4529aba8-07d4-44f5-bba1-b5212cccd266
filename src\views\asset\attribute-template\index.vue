<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="资产类型" prop="assetTypeId">
        <el-select
          v-model="queryParams.assetTypeId"
          placeholder="请选择资产类型"
          clearable
          style="width: 240px"
          @change="handleTypeChange"
        >
          <el-option
            v-for="item in assetTypeOptions"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="字段名称" prop="fieldName">
        <el-input
          v-model="queryParams.fieldName"
          placeholder="请输入字段名称"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          :disabled="!queryParams.assetTypeId"
        >新增</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <!-- <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          :disabled="!queryParams.assetTypeId"
        >新增</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @query-table="getList" />
    </el-row> -->

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="templateList">
      <el-table-column label="字段名称" align="center" prop="fieldName" min-width="120" />
      <el-table-column label="字段标识" align="center" prop="fieldKey" min-width="120" />
      <el-table-column label="字段类型" align="center" min-width="100">
        <template #default="scope">
          <el-tag :type="getFieldTypeTag(scope.row.fieldType) as any">
            {{ getFieldTypeName(scope.row.fieldType) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="是否必填" align="center" width="80">
        <template #default="scope">
          <el-tag :type="scope.row.required ? 'danger' : 'info'">
            {{ scope.row.required ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="默认值" align="center" prop="defaultValue" min-width="100" />
      <el-table-column label="排序" align="center" prop="sort" width="60" />
      <el-table-column label="描述" align="center" prop="description" min-width="150" :show-overflow-tooltip="true" />
      <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            type="text"
            icon="Edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button
            type="text"
            icon="Delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改属性模板字段对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="attributeFormRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="资产类型" prop="assetTypeId">
          <el-select
            v-model="form.assetTypeId"
            placeholder="请选择资产类型"
            disabled
            style="width: 100%"
          >
            <el-option
              v-for="item in assetTypeOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="字段名称" prop="fieldName">
          <el-input v-model="form.fieldName" placeholder="请输入字段名称" />
        </el-form-item>
        <el-form-item label="字段标识" prop="fieldKey">
          <el-input v-model="form.fieldKey" placeholder="请输入字段标识（英文）" />
        </el-form-item>
        <el-form-item label="字段类型" prop="fieldType">
          <el-select
            v-model="form.fieldType"
            placeholder="请选择字段类型"
            style="width: 100%"
          >
            <el-option label="文本" value="text" />
            <el-option label="数字" value="number" />
            <el-option label="下拉选择" value="select" />
            <el-option label="日期" value="date" />
            <el-option label="时间" value="datetime" />
            <el-option label="多行文本" value="textarea" />
            <el-option label="开关" value="switch" />
            <el-option label="单选框" value="radio" />
            <el-option label="多选框" value="checkbox" />
          </el-select>
        </el-form-item>
        <el-form-item label="默认值" prop="defaultValue">
          <el-input v-model="form.defaultValue" placeholder="请输入默认值" />
        </el-form-item>
        <el-form-item label="是否必填" prop="required">
          <el-switch v-model="form.required" />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="form.sort" :min="0" :max="999" controls-position="right" style="width: 100px" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入描述" />
        </el-form-item>
        <el-form-item label="选项值" prop="options" v-if="form.fieldType === 'select' || form.fieldType === 'radio' || form.fieldType === 'checkbox'">
          <el-tag
            v-for="(tag, index) in form.options"
            :key="index"
            closable
            :disable-transitions="false"
            @close="handleCloseTag(tag)"
            class="mx-1 mb-1"
          >
            {{ tag }}
          </el-tag>
          <el-input
            v-if="inputVisible"
            ref="InputRef"
            v-model="inputValue"
            class="ml-1 w-100"
            size="small"
            @keyup.enter="handleInputConfirm"
            @blur="handleInputConfirm"
          />
          <el-button v-else class="button-new-tag ml-1" size="small" @click="showInput">
            + 添加选项
          </el-button>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
// 引入mock数据和方法
import { 
  assetTypeOptions, 
  mockGetList, 
  mockAddField, 
  mockUpdateField, 
  mockDeleteField,
  getFieldTypeName,
  getFieldTypeTag
} from './mock'

const loading = ref(true)
const total = ref(0)
const open = ref(false)
const title = ref('')
const templateList = ref<any[]>([])
const inputVisible = ref(false)
const inputValue = ref('')
const InputRef = ref()

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  assetTypeId: undefined as undefined | number,
  fieldName: undefined as undefined | string
})

// 表单参数
const form = reactive({
  id: undefined as undefined | number,
  assetTypeId: undefined as undefined | number,
  fieldName: undefined as undefined | string,
  fieldKey: undefined as undefined | string,
  fieldType: 'text',
  required: false,
  defaultValue: undefined as undefined | string,
  sort: 0,
  description: undefined as undefined | string,
  options: [] as string[]
})

// 表单校验规则
const rules = reactive({
  assetTypeId: [{ required: true, message: '请选择资产类型', trigger: 'blur' }],
  fieldName: [{ required: true, message: '字段名称不能为空', trigger: 'blur' }],
  fieldKey: [{ required: true, message: '字段标识不能为空', trigger: 'blur' }],
  fieldType: [{ required: true, message: '请选择字段类型', trigger: 'blur' }]
})

/** 查询属性模板列表 - 使用mock API */
const getList = async () => {
  loading.value = true
  try {
    const { rows, total: totalCount } = await mockGetList(queryParams)
    templateList.value = rows
    total.value = totalCount
  } catch (error) {
    console.error('获取列表数据失败:', error)
    templateList.value = []
    total.value = 0
    ElMessage.error('获取数据失败，请重试')
  } finally {
    loading.value = false
  }
}

/** 取消按钮 */
const cancel = () => {
  open.value = false
  resetForm()
}

/** 表单重置 */
const resetForm = () => {
  form.id = undefined
  form.fieldName = undefined
  form.fieldKey = undefined
  form.fieldType = 'text'
  form.required = false
  form.defaultValue = undefined
  form.sort = 0
  form.description = undefined
  form.options = []
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryParams.fieldName = undefined
  handleQuery()
}

/** 资产类型变更 */
const handleTypeChange = () => {
  queryParams.pageNum = 1
  getList()
}

/** 新增按钮操作 */
const handleAdd = () => {
  resetForm()
  form.assetTypeId = queryParams.assetTypeId
  open.value = true
  title.value = '添加属性模板字段'
}

/** 修改按钮操作 */
const handleUpdate = (row: any) => {
  resetForm()
  form.id = row.id
  form.assetTypeId = row.assetTypeId
  form.fieldName = row.fieldName
  form.fieldKey = row.fieldKey
  form.fieldType = row.fieldType
  form.required = row.required
  form.defaultValue = row.defaultValue
  form.sort = row.sort
  form.description = row.description
  form.options = [...row.options]
  open.value = true
  title.value = '修改属性模板字段'
}

/** 提交按钮 - 使用mock API */
const submitForm = async () => {
  // 简化版表单验证
  if (!form.fieldName || !form.fieldKey || !form.fieldType) {
    ElMessage.error('请填写必填项')
    return
  }

  // 验证选项类字段必须有选项
  if (['select', 'radio', 'checkbox'].includes(form.fieldType) && form.options.length === 0) {
    ElMessage.error('请至少添加一个选项值')
    return
  }
  
  try {
    if (form.id) {
      // 修改字段
      await mockUpdateField(form)
      ElMessage.success('修改成功')
    } else {
      // 新增字段
      await mockAddField(form)
      ElMessage.success('新增成功')
    }
    
    open.value = false
    getList()
  } catch (error) {
    if (error instanceof Error) {
      ElMessage.error(error.message)
    } else {
      ElMessage.error('操作失败')
    }
  }
}

/** 删除按钮操作 - 使用mock API */
const handleDelete = (row: any) => {
  ElMessageBox.confirm(`是否确认删除字段"${row.fieldName}"?`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await mockDeleteField(row.id, row.assetTypeId)
      ElMessage.success('删除成功')
      getList()
    } catch (error) {
      if (error instanceof Error) {
        ElMessage.error(error.message)
      } else {
        ElMessage.error('删除失败')
      }
    }
  }).catch(() => {})
}

/** 显示新标签输入框 */
const showInput = () => {
  inputVisible.value = true
  nextTick(() => {
    InputRef.value?.focus()
  })
}

/** 处理输入确认 */
const handleInputConfirm = () => {
  if (inputValue.value) {
    // 避免重复添加
    if (!form.options.includes(inputValue.value)) {
      form.options.push(inputValue.value)
    }
  }
  inputVisible.value = false
  inputValue.value = ''
}

/** 关闭标签 */
const handleCloseTag = (tag: string) => {
  form.options.splice(form.options.indexOf(tag), 1)
}

// 获取URL参数中的assetTypeId
const setAssetTypeFromQuery = () => {
  const { search } = window.location
  const params = new URLSearchParams(search)
  const assetTypeId = params.get('assetTypeId')
  if (assetTypeId) {
    queryParams.assetTypeId = parseInt(assetTypeId, 10)
    // 自动触发查询
    handleQuery()
  }
}

onMounted(() => {
  try {
  setAssetTypeFromQuery()
    if (!queryParams.assetTypeId) {
      loading.value = false // 如果没有资产类型ID，需要手动关闭加载状态
    }
  } catch (error) {
    console.error('初始化失败:', error)
    loading.value = false
  }
})
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.mr-10 {
  margin-right: 10px;
}

.mb-10 {
  margin-bottom: 10px;
}

.mt-10 {
  margin-top: 10px;
}

.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}

.el-tag {
  margin-right: 10px;
  margin-bottom: 10px;
}
</style> 