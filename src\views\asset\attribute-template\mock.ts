// 模拟数据生成工具 - 资产属性模板
import { reactive } from 'vue'

// 资产类型基础数据
export const assetTypeOptions = [
  { id: 1, name: '生产类资产', code: 'PRODUCTION' },
  { id: 2, name: '行政类资产', code: 'ADMIN' },
  { id: 3, name: '特许资产', code: 'FRANCHISE' },
  { id: 4, name: '基础设施资产', code: 'INFRASTRUCTURE' },
  { id: 5, name: '机器设备', code: 'MACHINERY' },
  { id: 6, name: '运输设备', code: 'TRANSPORT' },
  { id: 7, name: '电子设备', code: 'ELECTRONIC' },
  { id: 8, name: '软件系统', code: 'SOFTWARE' },
  { id: 9, name: '土地资产', code: 'LAND' },
  { id: 10, name: '房屋建筑', code: 'BUILDING' }
]

// 字段类型对应关系
export const fieldTypeMap = {
  text: { name: '文本', tag: '' },
  number: { name: '数字', tag: 'success' },
  select: { name: '下拉选择', tag: 'warning' },
  date: { name: '日期', tag: 'info' },
  datetime: { name: '时间', tag: 'info' },
  textarea: { name: '多行文本', tag: '' },
  switch: { name: '开关', tag: 'warning' },
  radio: { name: '单选框', tag: 'warning' },
  checkbox: { name: '多选框', tag: 'warning' }
}

// 模拟数据 - 属性模板数据
export const mockTemplateData: Record<number, any[]> = reactive({
  // 生产类资产
  1: [
    { id: 1, assetTypeId: 1, fieldName: '设备型号', fieldKey: 'model', fieldType: 'text', required: true, defaultValue: '', sort: 1, description: '设备的型号规格', options: [] },
    { id: 2, assetTypeId: 1, fieldName: '安装位置', fieldKey: 'installLocation', fieldType: 'text', required: true, defaultValue: '', sort: 2, description: '设备的安装位置或区域', options: [] },
    { id: 3, assetTypeId: 1, fieldName: '生产厂商', fieldKey: 'manufacturer', fieldType: 'text', required: true, defaultValue: '', sort: 3, description: '生产该设备的厂商名称', options: [] },
    { id: 4, assetTypeId: 1, fieldName: '生产日期', fieldKey: 'productionDate', fieldType: 'date', required: true, defaultValue: '', sort: 4, description: '设备的生产日期', options: [] },
    { id: 5, assetTypeId: 1, fieldName: '保修期限', fieldKey: 'warrantyPeriod', fieldType: 'number', required: false, defaultValue: '12', sort: 5, description: '保修期限（月）', options: [] },
    { id: 6, assetTypeId: 1, fieldName: '设备状态', fieldKey: 'status', fieldType: 'select', required: true, defaultValue: '正常', sort: 6, description: '设备当前运行状态', options: ['正常', '故障', '维修中', '待报废'] }
  ],
  // 行政类资产
  2: [
    { id: 18, assetTypeId: 2, fieldName: '办公位置', fieldKey: 'officeLocation', fieldType: 'text', required: true, defaultValue: '', sort: 1, description: '资产所在办公室位置', options: [] },
    { id: 19, assetTypeId: 2, fieldName: '购置日期', fieldKey: 'purchaseDate', fieldType: 'date', required: true, defaultValue: '', sort: 2, description: '资产购置日期', options: [] },
    { id: 20, assetTypeId: 2, fieldName: '责任人', fieldKey: 'responsiblePerson', fieldType: 'text', required: true, defaultValue: '', sort: 3, description: '资产责任人姓名', options: [] },
    { id: 21, assetTypeId: 2, fieldName: '资产状态', fieldKey: 'assetStatus', fieldType: 'select', required: true, defaultValue: '在用', sort: 4, description: '资产当前使用状态', options: ['在用', '闲置', '维修', '报废'] }
  ],
  // 特许资产 
  3: [
    { id: 22, assetTypeId: 3, fieldName: '特许权名称', fieldKey: 'franchiseName', fieldType: 'text', required: true, defaultValue: '', sort: 1, description: '特许经营权名称', options: [] },
    { id: 23, assetTypeId: 3, fieldName: '授权单位', fieldKey: 'authorizingUnit', fieldType: 'text', required: true, defaultValue: '', sort: 2, description: '特许经营权授权单位', options: [] },
    { id: 24, assetTypeId: 3, fieldName: '特许期限', fieldKey: 'franchiseTerm', fieldType: 'number', required: true, defaultValue: '', sort: 3, description: '特许经营权期限（年）', options: [] },
    { id: 25, assetTypeId: 3, fieldName: '开始日期', fieldKey: 'startDate', fieldType: 'date', required: true, defaultValue: '', sort: 4, description: '特许权开始日期', options: [] },
    { id: 26, assetTypeId: 3, fieldName: '结束日期', fieldKey: 'endDate', fieldType: 'date', required: true, defaultValue: '', sort: 5, description: '特许权结束日期', options: [] },
    { id: 27, assetTypeId: 3, fieldName: '特许区域', fieldKey: 'franchiseArea', fieldType: 'textarea', required: true, defaultValue: '', sort: 6, description: '特许经营权覆盖区域', options: [] }
  ],
  // 基础设施资产
  4: [
    { id: 13, assetTypeId: 4, fieldName: '设施编码', fieldKey: 'facilityCode', fieldType: 'text', required: true, defaultValue: '', sort: 1, description: '基础设施编码', options: [] },
    { id: 14, assetTypeId: 4, fieldName: '建造日期', fieldKey: 'constructionDate', fieldType: 'date', required: true, defaultValue: '', sort: 2, description: '设施建造日期', options: [] },
    { id: 15, assetTypeId: 4, fieldName: '设计寿命', fieldKey: 'designLife', fieldType: 'number', required: true, defaultValue: '', sort: 3, description: '设计使用寿命（年）', options: [] },
    { id: 16, assetTypeId: 4, fieldName: '最近检修日期', fieldKey: 'lastMaintenance', fieldType: 'date', required: false, defaultValue: '', sort: 4, description: '最近一次检修日期', options: [] },
    { id: 17, assetTypeId: 4, fieldName: '安全等级', fieldKey: 'safetyLevel', fieldType: 'select', required: true, defaultValue: 'A', sort: 5, description: '安全等级评定', options: ['A', 'B', 'C', 'D'] }
  ],
  // 电子设备
  7: [
    { id: 7, assetTypeId: 7, fieldName: '品牌', fieldKey: 'brand', fieldType: 'text', required: true, defaultValue: '', sort: 1, description: '电子设备品牌', options: [] },
    { id: 8, assetTypeId: 7, fieldName: '型号', fieldKey: 'model', fieldType: 'text', required: true, defaultValue: '', sort: 2, description: '电子设备型号', options: [] },
    { id: 9, assetTypeId: 7, fieldName: '序列号', fieldKey: 'serialNumber', fieldType: 'text', required: true, defaultValue: '', sort: 3, description: '设备唯一序列号', options: [] },
    { id: 10, assetTypeId: 7, fieldName: '购买日期', fieldKey: 'purchaseDate', fieldType: 'date', required: true, defaultValue: '', sort: 4, description: '设备购买日期', options: [] },
    { id: 11, assetTypeId: 7, fieldName: '配置信息', fieldKey: 'configuration', fieldType: 'textarea', required: false, defaultValue: '', sort: 5, description: '详细配置信息', options: [] },
    { id: 12, assetTypeId: 7, fieldName: '使用状态', fieldKey: 'useStatus', fieldType: 'select', required: true, defaultValue: '在用', sort: 6, description: '使用状态', options: ['在用', '闲置', '维修', '报废'] }
  ]
})

// 模拟API - 查询列表
export const mockGetList = async (params: any) => {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 300))
  
  // 检查是否选择了资产类型
  if (!params.assetTypeId) {
    return {
      rows: [],
      total: 0
    }
  }
  
  // 获取当前资产类型的模板数据
  const typeId = params.assetTypeId
  let data = mockTemplateData[typeId] || []
  
  // 按字段名称过滤
  if (params.fieldName) {
    data = data.filter(
      item => item.fieldName.includes(params.fieldName || '')
    )
  }
  
  // 计算总数
  const total = data.length
  
  // 分页处理
  const start = (params.pageNum - 1) * params.pageSize
  const end = start + params.pageSize
  const rows = data.slice(start, end)
  
  return {
    rows,
    total
  }
}

// 模拟API - 新增字段
export const mockAddField = async (data: any) => {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 300))
  
  const typeId = data.assetTypeId
  
  // 确保该类型的数据数组存在
  if (typeId && !mockTemplateData[typeId]) {
    mockTemplateData[typeId] = []
  }
  
  // 获取数据数组的引用
  const dataArray = typeId ? mockTemplateData[typeId] : []
  
  // 计算新ID (在所有类型中查找最大ID)
  let maxId = 0
  Object.values(mockTemplateData).forEach((typeData: any) => {
    typeData.forEach((item: any) => {
      if (item.id > maxId) maxId = item.id
    })
  })
  
  // 添加新记录
  if (typeId) {
    const newItem = { 
      ...data,
      id: maxId + 1,
      assetTypeId: typeId 
    }
    
    dataArray.push(newItem)
    return newItem
  }
  
  throw new Error('添加失败：资产类型不存在')
}

// 模拟API - 更新字段
export const mockUpdateField = async (data: any) => {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 300))
  
  const typeId = data.assetTypeId
  
  // 确保该类型的数据数组存在
  if (!typeId || !mockTemplateData[typeId]) {
    throw new Error('更新失败：资产类型不存在')
  }
  
  // 获取数据数组的引用
  const dataArray = mockTemplateData[typeId]
  
  // 查找并更新
  const index = dataArray.findIndex(item => item.id === data.id)
  if (index !== -1) {
    // 复制一份完整数据，然后覆盖提交的字段
    const updatedItem = { ...dataArray[index], ...data }
    dataArray[index] = updatedItem
    return updatedItem
  }
  
  throw new Error('更新失败：字段不存在')
}

// 模拟API - 删除字段
export const mockDeleteField = async (id: number, typeId: number) => {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 300))
  
  if (!typeId || !mockTemplateData[typeId]) {
    throw new Error('删除失败：资产类型不存在')
  }
  
  const dataArray = mockTemplateData[typeId]
  const index = dataArray.findIndex(item => item.id === id)
  
  if (index !== -1) {
    dataArray.splice(index, 1)
    return { success: true }
  }
  
  throw new Error('删除失败：字段不存在')
}

// 根据字段类型获取显示名称
export const getFieldTypeName = (type: string) => {
  return fieldTypeMap[type as keyof typeof fieldTypeMap]?.name || type
}

// 根据字段类型获取标签类型
export const getFieldTypeTag = (type: string): '' | 'success' | 'warning' | 'info' | 'danger' => {
  return (fieldTypeMap[type as keyof typeof fieldTypeMap]?.tag || '') as '' | 'success' | 'warning' | 'info' | 'danger'
} 