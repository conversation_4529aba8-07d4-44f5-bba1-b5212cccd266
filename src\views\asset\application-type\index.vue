<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-card class="search-card">
      <el-form :model="queryParams" ref="queryForm" :inline="true">
        <el-form-item label="关键词" prop="keyword">
          <el-input
            v-model="queryParams.keyword"
            placeholder="请输入申请类型名称/编码"
            clearable
            style="width: 240px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="资产类型" prop="assetType">
          <el-select
            v-model="queryParams.assetType"
            placeholder="请选择资产类型"
            clearable
            style="width: 180px"
          >
            <el-option
              v-for="item in assetTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="启用状态" prop="enabled">
          <el-select
            v-model="queryParams.enabled"
            placeholder="请选择状态"
            clearable
            style="width: 180px"
          >
            <el-option label="启用" :value="true" />
            <el-option label="停用" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计图表 -->
    <ApplicationTypeChart />

    <!-- Tab分类 -->
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane
        v-for="item in categoryOptions"
        :key="item.value"
        :label="item.label"
        :name="item.value"
      >
        <!-- 操作按钮 -->
        <div class="operation-buttons">
          <el-button type="primary" icon="Plus" @click="handleAdd">新建申请类型</el-button>
        </div>

        <!-- 卡片列表 -->
        <el-row :gutter="20">
          <el-col
            v-for="item in applicationTypeList"
            :key="item.id"
            :xs="24"
            :sm="12"
            :md="8"
            :lg="6"
            :xl="4"
          >
            <ApplicationTypeCard
              :data="item"
              @view="handleView"
              @edit="handleEdit"
              @delete="handleDelete"
            />
          </el-col>
        </el-row>

        <!-- 分页 -->
        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-tab-pane>
    </el-tabs>

    <!-- 表单抽屉 -->
    <ApplicationTypeFormDrawer
      v-model:visible="drawerVisible"
      :id="currentId"
      @success="handleSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, provide } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import ApplicationTypeCard from './components/ApplicationTypeCard.vue'
import ApplicationTypeFormDrawer from './components/ApplicationTypeFormDrawer.vue'
import ApplicationTypeChart from './components/ApplicationTypeChart.vue'
// 注释原API导入，改为使用模拟数据
// import { listApplicationTypes, deleteApplicationType } from '@/api/asset/applicationType'

// 资产类型选项
const assetTypeOptions = [
  { value: 'PRODUCTION', label: '生产类资产' },
  { value: 'ADMIN', label: '行政类资产' },
  { value: 'FRANCHISE', label: '特许资产' }
]

// 申请场景选项
const categoryOptions = [
  { value: 'ASSET_NEW', label: '资产新增申请' },
  { value: 'ASSET_SCRAP', label: '资产报废申请' },
  { value: 'ASSET_TRANSFER', label: '资产调拨申请' },
  { value: 'ASSET_MAINTENANCE', label: '资产维修申请' }
]

// 模拟数据
const mockApplicationTypes = ref([
  {
    id: 'AT0001',
    name: '设备采购申请',
    code: 'AT0001',
    assetType: 'PRODUCTION',
    assetTypeName: '生产类资产',
    category: 'ASSET_NEW',
    categoryName: '资产新增申请',
    enabled: true,
    description: '用于向集团申请采购新的生产设备，需要提供设备技术参数、估算金额和采购理由。',
    createTime: '2023-04-10T08:30:00'
  },
  {
    id: 'AT0002',
    name: '办公设备申请',
    code: 'AT0002',
    assetType: 'ADMIN',
    assetTypeName: '行政类资产',
    category: 'ASSET_NEW',
    categoryName: '资产新增申请',
    enabled: true,
    description: '用于向行政部门申请采购办公设备，包括电脑、打印机等办公用品。',
    createTime: '2023-04-12T10:15:00'
  },
  {
    id: 'AT0003',
    name: '特许权资产申请',
    code: 'AT0003',
    assetType: 'FRANCHISE',
    assetTypeName: '特许资产',
    category: 'ASSET_NEW',
    categoryName: '资产新增申请',
    enabled: true,
    description: '用于申请新增水务特许经营权等无形资产，需要提供项目详细资料和可行性分析。',
    createTime: '2023-04-15T14:20:00'
  },
  {
    id: 'AT0004',
    name: '设备报废申请',
    code: 'AT0004',
    assetType: 'PRODUCTION',
    assetTypeName: '生产类资产',
    category: 'ASSET_SCRAP',
    categoryName: '资产报废申请',
    enabled: true,
    description: '用于申请报废已无法正常使用或修复成本过高的生产设备，需提供设备状况评估报告。',
    createTime: '2023-04-18T09:30:00'
  },
  {
    id: 'AT0005',
    name: '办公设备报废',
    code: 'AT0005',
    assetType: 'ADMIN',
    assetTypeName: '行政类资产',
    category: 'ASSET_SCRAP',
    categoryName: '资产报废申请',
    enabled: true,
    description: '用于申请报废老旧或损坏的办公设备，需说明设备使用年限和损坏情况。',
    createTime: '2023-04-20T11:45:00'
  },
  {
    id: 'AT0006',
    name: '特许资产处置',
    code: 'AT0006',
    assetType: 'FRANCHISE',
    assetTypeName: '特许资产',
    category: 'ASSET_SCRAP',
    categoryName: '资产报废申请',
    enabled: false,
    description: '用于申请处置到期或需要转让的特许经营权资产，需提供相关法律文件和评估报告。',
    createTime: '2023-04-22T16:00:00'
  },
  {
    id: 'AT0007',
    name: '设备内部调拨',
    code: 'AT0007',
    assetType: 'PRODUCTION',
    assetTypeName: '生产类资产',
    category: 'ASSET_TRANSFER',
    categoryName: '资产调拨申请',
    enabled: true,
    description: '用于集团内部各单位间的生产设备调拨，需说明调拨原因和使用计划。',
    createTime: '2023-04-25T08:50:00'
  },
  {
    id: 'AT0008',
    name: '办公资产调拨',
    code: 'AT0008',
    assetType: 'ADMIN',
    assetTypeName: '行政类资产',
    category: 'ASSET_TRANSFER',
    categoryName: '资产调拨申请',
    enabled: true,
    description: '用于办公设备在各部门之间的调拨，包括电脑、办公家具等资产的转移。',
    createTime: '2023-04-27T13:25:00'
  },
  {
    id: 'AT0009',
    name: '特许权转让',
    code: 'AT0009',
    assetType: 'FRANCHISE',
    assetTypeName: '特许资产',
    category: 'ASSET_TRANSFER',
    categoryName: '资产调拨申请',
    enabled: false,
    description: '用于申请特许经营权在集团内部不同实体间的转让，需提供转让协议和价值评估。',
    createTime: '2023-04-29T15:10:00'
  },
  {
    id: 'AT0010',
    name: '设备维修申请',
    code: 'AT0010',
    assetType: 'PRODUCTION',
    assetTypeName: '生产类资产',
    category: 'ASSET_MAINTENANCE',
    categoryName: '资产维修申请',
    enabled: true,
    description: '用于申请对生产设备进行维修或保养，需说明故障情况和维修预算。',
    createTime: '2023-05-02T09:15:00'
  },
  {
    id: 'AT0011',
    name: '办公设备维修',
    code: 'AT0011',
    assetType: 'ADMIN',
    assetTypeName: '行政类资产',
    category: 'ASSET_MAINTENANCE',
    categoryName: '资产维修申请',
    enabled: true,
    description: '用于申请对办公设备进行维修，包括电脑、打印机等设备的故障修复。',
    createTime: '2023-05-05T10:30:00'
  },
  {
    id: 'AT0012',
    name: '水务设施修缮',
    code: 'AT0012',
    assetType: 'FRANCHISE',
    assetTypeName: '特许资产',
    category: 'ASSET_MAINTENANCE',
    categoryName: '资产维修申请',
    enabled: true,
    description: '用于申请对特许经营权范围内的水务设施进行修缮和技术改造，需提供工程方案和预算。',
    createTime: '2023-05-08T14:45:00'
  }
])

const loading = ref(false)
const total = ref(0)
const applicationTypeList = ref([])
const activeTab = ref('ASSET_NEW')
const drawerVisible = ref(false)
const currentId = ref('')

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 8,
  keyword: '',
  assetType: '',
  enabled: undefined,
  category: 'ASSET_NEW'
})

// 模拟API - 获取列表数据
const getList = async () => {
  loading.value = true
  try {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 300))
    
    // 按条件筛选数据
    let filteredData = [...mockApplicationTypes.value]
    
    // 分类筛选
    filteredData = filteredData.filter(item => item.category === activeTab.value)
    
    // 资产类型筛选
    if (queryParams.assetType) {
      filteredData = filteredData.filter(item => item.assetType === queryParams.assetType)
    }
    
    // 状态筛选
    if (queryParams.enabled !== undefined && queryParams.enabled !== '') {
      filteredData = filteredData.filter(item => item.enabled === queryParams.enabled)
    }
    
    // 关键词筛选
    if (queryParams.keyword) {
      const keyword = queryParams.keyword.toLowerCase()
      filteredData = filteredData.filter(item => 
        item.name.toLowerCase().includes(keyword) || 
        item.code.toLowerCase().includes(keyword)
      )
    }
    
    // 更新总数
    total.value = filteredData.length
    
    // 分页处理
    const start = (queryParams.pageNum - 1) * queryParams.pageSize
    const end = start + queryParams.pageSize
    applicationTypeList.value = filteredData.slice(start, end)
  } finally {
    loading.value = false
  }
}

// 模拟API - 删除
const deleteApplicationType = async (id: string) => {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 300))
  
  const index = mockApplicationTypes.value.findIndex(item => item.id === id)
  if (index !== -1) {
    mockApplicationTypes.value.splice(index, 1)
    return { code: 200, msg: '删除成功' }
  } else {
    throw new Error('记录不存在')
  }
}

// 搜索
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置搜索
const resetQuery = () => {
  queryParams.keyword = ''
  queryParams.assetType = ''
  queryParams.enabled = undefined
  handleQuery()
}

// Tab切换
const handleTabClick = () => {
  queryParams.pageNum = 1
  getList()
}

// 新增
const handleAdd = () => {
  currentId.value = ''
  drawerVisible.value = true
}

// 查看
const handleView = (id: string) => {
  currentId.value = id
  drawerVisible.value = true
}

// 编辑
const handleEdit = (id: string) => {
  currentId.value = id
  drawerVisible.value = true
}

// 删除
const handleDelete = async (id: string) => {
  try {
    await deleteApplicationType(id)
    ElMessage.success('删除成功')
    getList()
  } catch (error) {
    console.error('删除失败:', error)
    ElMessage.error('删除失败')
  }
}

// 表单提交成功
const handleSuccess = () => {
  getList()
}

// 模拟数据 - 统计信息
const statsData = computed(() => {
  const total = mockApplicationTypes.value.length
  const enabled = mockApplicationTypes.value.filter(item => item.enabled).length
  const disabled = total - enabled
  
  const byCategory = categoryOptions.map(category => ({
    name: category.label,
    value: mockApplicationTypes.value.filter(item => item.category === category.value).length
  }))
  
  const byAssetType = assetTypeOptions.map(type => ({
    name: type.label,
    value: mockApplicationTypes.value.filter(item => item.assetType === type.value).length
  }))
  
  return {
    total,
    enabled,
    disabled,
    byCategory,
    byAssetType
  }
})

// 为表单抽屉提供模拟API方法
const getApplicationType = async (id: string) => {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 300))
  
  const item = mockApplicationTypes.value.find(item => item.id === id)
  if (item) {
    return { code: 200, msg: 'success', data: { ...item } }
  } else {
    throw new Error('记录不存在')
  }
}

const addApplicationType = async (data: any) => {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 500))
  
  // 生成新ID
  const newId = `AT${String(mockApplicationTypes.value.length + 1).padStart(4, '0')}`
  
  // 根据选择的类型和分类获取名称
  const assetTypeObj = assetTypeOptions.find(item => item.value === data.assetType)
  const categoryObj = categoryOptions.find(item => item.value === data.category)
  
  const newItem = {
    ...data,
    id: newId,
    assetTypeName: assetTypeObj?.label || '',
    categoryName: categoryObj?.label || '',
    createTime: new Date().toISOString()
  }
  
  mockApplicationTypes.value.push(newItem)
  return { code: 200, msg: '添加成功', data: newItem }
}

const updateApplicationType = async (data: any) => {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 500))
  
  const index = mockApplicationTypes.value.findIndex(item => item.id === data.id)
  if (index !== -1) {
    // 根据选择的类型和分类获取名称
    const assetTypeObj = assetTypeOptions.find(item => item.value === data.assetType)
    const categoryObj = categoryOptions.find(item => item.value === data.category)
    
    mockApplicationTypes.value[index] = {
      ...mockApplicationTypes.value[index],
      ...data,
      assetTypeName: assetTypeObj?.label || mockApplicationTypes.value[index].assetTypeName,
      categoryName: categoryObj?.label || mockApplicationTypes.value[index].categoryName
    }
    return { code: 200, msg: '更新成功' }
  } else {
    throw new Error('记录不存在')
  }
}

// 为图表组件提供模拟API
const getApplicationTypeStats = async () => {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 300))
  
  return { 
    code: 200, 
    msg: 'success', 
    data: statsData.value
  }
}

// 提供API方法给子组件
provide('getApplicationType', getApplicationType)
provide('addApplicationType', addApplicationType)
provide('updateApplicationType', updateApplicationType)
provide('getApplicationTypeStats', getApplicationTypeStats)

onMounted(() => {
  getList()
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.search-card {
  margin-bottom: 20px;
}

.operation-buttons {
  margin-bottom: 20px;
}
</style> 