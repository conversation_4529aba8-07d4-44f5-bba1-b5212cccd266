<script lang="ts">
// 使用lang="ts"替代script setup，解决导入未使用的错误问题
</script>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { 
  Search, 
  Refresh, 
  Plus, 
  Edit, 
  Delete, 
  Download,
  View
} from '@element-plus/icons-vue'

// 类型定义
interface Asset {
  id: string
  code: string
  name: string
  type: string
  department: string
  location: string
  status: string
  value: number
  purchaseDate: string
  warrantyEndDate: string
  responsible: string
  createTime: string
}

// 状态
const loading = ref(false)
const drawerVisible = ref(false)
const currentAsset = ref<Asset | null>(null)
const showAdvanced = ref(false)

// 搜索表单
const searchFormRef = ref<FormInstance>()
const searchForm = reactive({
  keyword: '',
  type: '',
  department: '',
  status: '',
  dateRange: [] as string[]
})

// 表格数据
const tableData = ref<Asset[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

// 创建mock数据数组
const createMockData = (): Asset[] => {
  const result: Asset[] = [];
  for (let i = 0; i < 100; i++) {
    result.push({
      id: `${i + 1}`,
      code: `ZC${String(i + 1).padStart(6, '0')}`,
      name: `测试资产${i + 1}`,
      type: ['生产设备', '办公设备', '运输设备'][i % 3],
      department: ['生产部', '行政部', '技术部'][i % 3],
      location: ['一号车间', '二号车间', '办公区'][i % 3],
      status: ['在用', '维修中', '闲置', '报废'][i % 4],
      value: Math.floor(Math.random() * 100000),
      purchaseDate: '2024-01-01',
      warrantyEndDate: '2027-01-01',
      responsible: ['张三', '李四', '王五'][i % 3],
      createTime: '2024-01-01 10:00:00'
    });
  }
  return result;
};

// 获取资产列表
const fetchAssetList = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取数据
    // 模拟数据
    const mockData: Asset[] = createMockData();
    
    // 模拟分页
    const start = (currentPage.value - 1) * pageSize.value
    const end = start + pageSize.value
    tableData.value = mockData.slice(start, end)
    total.value = mockData.length
  } catch (error) {
    console.error('获取资产列表失败', error)
    ElMessage.error('获取资产列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchAssetList()
}

// 重置搜索
const handleReset = () => {
  if (!searchFormRef.value) return
  searchFormRef.value.resetFields()
  handleSearch()
}

// 新增资产
const handleAdd = () => {
  // TODO: 实现新增功能
  ElMessage.info('新增功能开发中')
}

// 编辑资产
const handleEdit = (row: Asset) => {
  // TODO: 实现编辑功能
  ElMessage.info('编辑功能开发中')
}

// 删除资产
const handleDelete = (row: Asset) => {
  ElMessageBox.confirm(
    '确定要删除该资产吗？删除后不可恢复。',
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      // TODO: 调用删除API
      ElMessage.success('删除成功')
      fetchAssetList()
    } catch (error) {
      console.error('删除资产失败', error)
      ElMessage.error('删除资产失败')
    }
  }).catch(() => {})
}

// 查看详情
const handleView = (row: Asset) => {
  currentAsset.value = row
  drawerVisible.value = true
}

// 导出资产
const handleExport = () => {
  // TODO: 实现导出功能
  ElMessage.info('导出功能开发中')
}

// 分页变化
const handlePageChange = (page: number) => {
  currentPage.value = page
  fetchAssetList()
}

// 每页条数变化
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  fetchAssetList()
}

// 页面加载时获取数据
onMounted(() => {
  fetchAssetList()
})
</script>

<template>
  <div class="asset-list-container">
    <el-card v-loading="loading">
      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form
          ref="searchFormRef"
          :model="searchForm"
          :inline="true"
          class="search-form"
        >
          <el-form-item label="关键字" prop="keyword">
            <el-input
              v-model="searchForm.keyword"
              placeholder="资产编号/名称"
              clearable
              @keyup.enter="handleSearch"
            />
          </el-form-item>
          <el-form-item label="资产类型" prop="type">
            <el-select v-model="searchForm.type" placeholder="请选择" clearable>
              <el-option label="生产设备" value="生产设备" />
              <el-option label="办公设备" value="办公设备" />
              <el-option label="运输设备" value="运输设备" />
            </el-select>
          </el-form-item>
          <el-form-item label="所属部门" prop="department">
            <el-select v-model="searchForm.department" placeholder="请选择" clearable>
              <el-option label="生产部" value="生产部" />
              <el-option label="行政部" value="行政部" />
              <el-option label="技术部" value="技术部" />
            </el-select>
          </el-form-item>
          <el-form-item label="资产状态" prop="status">
            <el-select v-model="searchForm.status" placeholder="请选择" clearable>
              <el-option label="在用" value="在用" />
              <el-option label="维修中" value="维修中" />
              <el-option label="闲置" value="闲置" />
              <el-option label="报废" value="报废" />
            </el-select>
          </el-form-item>
          <el-form-item label="购置日期" prop="dateRange">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" :icon="Search" @click="handleSearch">搜索</el-button>
            <el-button :icon="Refresh" @click="handleReset">重置</el-button>
            <el-button link type="primary" @click="showAdvanced = !showAdvanced">
              {{ showAdvanced ? '收起' : '展开' }}
              <el-icon class="el-icon--right">
                <component :is="showAdvanced ? 'ArrowUp' : 'ArrowDown'" />
              </el-icon>
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 操作按钮 -->
      <div class="table-operations">
        <el-button type="primary" :icon="Plus" @click="handleAdd">新增资产</el-button>
        <el-button :icon="Download" @click="handleExport">导出</el-button>
      </div>

      <!-- 表格 -->
      <el-table :data="tableData" border style="width: 100%">
        <el-table-column prop="code" label="资产编号" width="120" />
        <el-table-column prop="name" label="资产名称" width="150" />
        <el-table-column prop="type" label="资产类型" width="100" />
        <el-table-column prop="department" label="所属部门" width="100" />
        <el-table-column prop="location" label="存放位置" width="120" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag
              :type="
                row.status === '在用' ? 'success' :
                row.status === '维修中' ? 'warning' :
                row.status === '闲置' ? 'info' :
                'danger'
              "
            >
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="value" label="资产价值" width="120">
          <template #default="{ row }">
            ¥{{ row.value.toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column prop="purchaseDate" label="购置日期" width="100" />
        <el-table-column prop="warrantyEndDate" label="保修到期" width="100" />
        <el-table-column prop="responsible" label="负责人" width="100" />
        <el-table-column prop="createTime" label="创建时间" width="160" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link :icon="View" @click="handleView(row)">查看</el-button>
            <el-button type="primary" link :icon="Edit" @click="handleEdit(row)">编辑</el-button>
            <el-button type="danger" link :icon="Delete" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>

    <!-- 详情抽屉 -->
    <el-drawer
      v-model="drawerVisible"
      title="资产详情"
      size="600px"
      destroy-on-close
    >
      <template v-if="currentAsset">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="资产编号">{{ currentAsset.code }}</el-descriptions-item>
          <el-descriptions-item label="资产名称">{{ currentAsset.name }}</el-descriptions-item>
          <el-descriptions-item label="资产类型">{{ currentAsset.type }}</el-descriptions-item>
          <el-descriptions-item label="所属部门">{{ currentAsset.department }}</el-descriptions-item>
          <el-descriptions-item label="存放位置">{{ currentAsset.location }}</el-descriptions-item>
          <el-descriptions-item label="资产状态">
            <el-tag
              :type="
                currentAsset.status === '在用' ? 'success' :
                currentAsset.status === '维修中' ? 'warning' :
                currentAsset.status === '闲置' ? 'info' :
                'danger'
              "
            >
              {{ currentAsset.status }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="资产价值">
            ¥{{ currentAsset.value.toLocaleString() }}
          </el-descriptions-item>
          <el-descriptions-item label="购置日期">{{ currentAsset.purchaseDate }}</el-descriptions-item>
          <el-descriptions-item label="保修到期">{{ currentAsset.warrantyEndDate }}</el-descriptions-item>
          <el-descriptions-item label="负责人">{{ currentAsset.responsible }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ currentAsset.createTime }}</el-descriptions-item>
        </el-descriptions>

        <!-- 资产图片 -->
        <div class="asset-images">
          <h4>资产图片</h4>
          <el-image
            style="width: 100px; height: 100px"
            src="https://via.placeholder.com/100"
            :preview-src-list="['https://via.placeholder.com/100']"
          />
        </div>

        <!-- 维修记录 -->
        <div class="maintenance-records">
          <h4>维修记录</h4>
          <el-timeline>
            <el-timeline-item
              v-for="(record, index) in 3"
              :key="index"
              :timestamp="'2024-01-01 10:00:00'"
              placement="top"
            >
              <el-card>
                <h4>维修记录 {{ index + 1 }}</h4>
                <p>维修内容描述...</p>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<style lang="scss" scoped>
.asset-list-container {
  padding: 20px;

  .search-area {
    margin-bottom: 20px;
  }

  .table-operations {
    margin-bottom: 20px;
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }

  .asset-images {
    margin-top: 20px;
    
    h4 {
      margin-bottom: 10px;
    }
  }

  .maintenance-records {
    margin-top: 20px;
    
    h4 {
      margin-bottom: 10px;
    }
  }
}
</style> 