// 资产管理模块工具函数

/**
 * 生成资产编号
 * @param prefix 前缀，默认为ZC（资产）
 * @returns 资产编号
 */
export const generateAssetCode = (prefix: string = 'ZC') => {
  const timestamp = Date.now().toString().slice(-8)
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
  return `${prefix}${timestamp}${random}`
}

/**
 * 格式化金额
 * @param amount 金额数值
 * @param currency 货币符号，默认为¥
 * @returns 格式化后的金额字符串
 */
export const formatAmount = (amount: number, currency: string = '¥') => {
  if (amount === undefined || amount === null) return '--'
  return `${currency}${amount.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
}

/**
 * 获取状态标签类型
 * @param statusId 状态ID
 * @returns 对应的Element Plus标签类型
 */
export const getStatusTagType = (statusId: number): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
  const statusTypeMap: Record<number, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    1: 'success', // 在用
    2: 'info',    // 闲置
    3: 'warning', // 维修中
    4: 'warning', // 调拨中
    5: 'danger'   // 报废
  }
  return statusTypeMap[statusId] || 'info' // 返回一个默认值而不是空字符串
}

/**
 * 获取状态文本颜色
 * @param statusId 状态ID
 * @returns 对应的CSS颜色
 */
export const getStatusTextColor = (statusId: number): string => {
  const statusColorMap: Record<number, string> = {
    1: '#67C23A', // 在用
    2: '#909399', // 闲置
    3: '#E6A23C', // 维修中
    4: '#E6A23C', // 调拨中
    5: '#F56C6C'  // 报废
  }
  return statusColorMap[statusId] || '#909399'
}

/**
 * 是否是图片文件
 * @param fileName 文件名
 * @returns 是否是图片
 */
export const isImageFile = (fileName: string): boolean => {
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
  const lowerFileName = fileName.toLowerCase()
  return imageExtensions.some(ext => lowerFileName.endsWith(ext))
}

/**
 * 是否是Excel文件
 * @param fileName 文件名
 * @returns 是否是Excel文件
 */
export const isExcelFile = (fileName: string): boolean => {
  const excelExtensions = ['.xlsx', '.xls', '.csv']
  const lowerFileName = fileName.toLowerCase()
  return excelExtensions.some(ext => lowerFileName.endsWith(ext))
}

/**
 * 将对象数组转为CSV格式
 * @param data 对象数组
 * @param headers 表头配置
 * @returns CSV字符串
 */
export const objectsToCSV = (data: any[], headers: { label: string; key: string }[]): string => {
  if (!data || !data.length) return ''
  
  // 添加表头行
  const headerRow = headers.map(h => `"${h.label}"`).join(',')
  
  // 添加数据行
  const rows = data.map(item => {
    return headers.map(header => {
      // 处理嵌套属性，如 'assetType.name'
      const keys = header.key.split('.')
      let value = item
      for (const key of keys) {
        value = value ? value[key] : ''
      }
      // 如果值包含逗号或引号，需要进行特殊处理
      return `"${(value !== null && value !== undefined ? String(value) : '').replace(/"/g, '""')}"`
    }).join(',')
  }).join('\n')
  
  return `${headerRow}\n${rows}`
}

/**
 * 根据选择的字段过滤数据
 * @param data 原始数据
 * @param fields 字段配置
 * @returns 过滤后的数据
 */
export const filterDataByFields = (data: any[], fields: { value: string; checked: boolean }[]) => {
  const selectedFields = fields.filter(f => f.checked).map(f => f.value)
  
  return data.map(item => {
    const filteredItem: Record<string, any> = {}
    
    selectedFields.forEach(field => {
      // 处理嵌套属性，如 'assetType.name'
      const keys = field.split('.')
      let value = item
      let valid = true
      
      for (const key of keys) {
        if (value === undefined || value === null) {
          valid = false
          break
        }
        value = value[key]
      }
      
      if (valid) {
        filteredItem[field] = value
      } else {
        filteredItem[field] = ''
      }
    })
    
    return filteredItem
  })
}

/**
 * 下载Blob数据
 * @param blob 二进制数据
 * @param fileName 文件名
 */
export const downloadBlob = (blob: Blob, fileName: string) => {
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = fileName
  link.click()
  URL.revokeObjectURL(link.href)
} 