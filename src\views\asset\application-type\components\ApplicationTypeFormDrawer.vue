<template>
  <el-drawer
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    :title="title"
    size="50%"
    :destroy-on-close="true"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      class="drawer-form"
    >
      <el-form-item label="申请类型名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入申请类型名称" />
      </el-form-item>
      <el-form-item label="编码" prop="code">
        <el-input v-model="form.code" placeholder="请输入编码" :disabled="!!form.id" />
      </el-form-item>
      <el-form-item label="资产类型" prop="assetType">
        <el-select v-model="form.assetType" placeholder="请选择资产类型" style="width: 100%">
          <el-option
            v-for="item in assetTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="申请场景" prop="category">
        <el-select v-model="form.category" placeholder="请选择申请场景" style="width: 100%">
          <el-option
            v-for="item in categoryOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="启用状态" prop="enabled">
        <el-switch v-model="form.enabled" />
      </el-form-item>
      <el-form-item label="适用说明" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="4"
          placeholder="请输入适用说明"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, inject } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'

// 注释原API导入，使用从父组件注入的模拟API
// import {
//   addApplicationType,
//   updateApplicationType,
//   getApplicationType
// } from '@/api/asset/applicationType'

// 从父组件注入模拟API
const getApplicationType = inject('getApplicationType', async (id: string) => {
  console.warn('未从父组件获取到getApplicationType方法')
  return { data: {} }
})

const addApplicationType = inject('addApplicationType', async (data: any) => {
  console.warn('未从父组件获取到addApplicationType方法')
  return { code: 200 }
})

const updateApplicationType = inject('updateApplicationType', async (data: any) => {
  console.warn('未从父组件获取到updateApplicationType方法')
  return { code: 200 }
})

const props = defineProps<{
  visible: boolean
  id?: string
}>()

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}>()

// 资产类型选项
const assetTypeOptions = [
  { value: 'PRODUCTION', label: '生产类资产' },
  { value: 'ADMIN', label: '行政类资产' },
  { value: 'FRANCHISE', label: '特许资产' }
]

// 申请场景选项
const categoryOptions = [
  { value: 'ASSET_NEW', label: '资产新增申请' },
  { value: 'ASSET_SCRAP', label: '资产报废申请' },
  { value: 'ASSET_TRANSFER', label: '资产调拨申请' },
  { value: 'ASSET_MAINTENANCE', label: '资产维修申请' }
]

const formRef = ref<FormInstance>()

const form = reactive({
  id: '',
  name: '',
  code: '',
  assetType: '',
  category: '',
  enabled: true,
  description: ''
})

const rules = reactive({
  name: [{ required: true, message: '请输入申请类型名称', trigger: 'blur' }],
  code: [{ required: true, message: '请输入编码', trigger: 'blur' }],
  assetType: [{ required: true, message: '请选择资产类型', trigger: 'change' }],
  category: [{ required: true, message: '请选择申请场景', trigger: 'change' }]
})

const title = computed(() => (props.id ? '编辑申请类型' : '新增申请类型'))

// 获取详情
const getDetail = async (id: string) => {
  try {
    const res = await getApplicationType(id)
    Object.assign(form, res.data)
  } catch (error) {
    console.error('获取详情失败:', error)
  }
}

// 监听 visible 变化
watch(() => props.visible, (val) => {
  if (val && props.id) {
    getDetail(props.id)
  } else if (!val) {
    resetForm()
  }
})

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(form, {
    id: '',
    name: '',
    code: '',
    assetType: '',
    category: '',
    enabled: true,
    description: ''
  })
}

// 关闭抽屉
const handleClose = () => {
  emit('update:visible', false)
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (form.id) {
          await updateApplicationType(form)
          ElMessage.success('修改成功')
        } else {
          await addApplicationType(form)
          ElMessage.success('新增成功')
        }
        emit('success')
        handleClose()
      } catch (error) {
        console.error('提交失败:', error)
      }
    }
  })
}
</script>

<style scoped>
.drawer-form {
  padding: 20px;
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 20px;
}
</style> 