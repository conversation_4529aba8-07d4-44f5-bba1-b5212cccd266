<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Asset, QueryParams } from '@/types/asset'
import { ElMessage } from 'element-plus'

// API引入
const props = defineProps({
  multiple: {
    type: Boolean,
    default: true
  },
  modelValue: {
    type: Array as () => Asset[],
    default: () => []
  },
  disabled: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '选择资产'
  },
  filterByStatus: {
    type: Array as () => number[],
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue', 'select', 'cancel'])

// 表单数据
const dialogVisible = ref(false)
const searchParams = ref({
  pageNum: 1,
  pageSize: 10,
  keyword: '',
  assetTypeId: undefined,
  unitId: undefined,
  statusId: undefined,
  useStatus: undefined
})

// 加载状态
const loading = ref(false)
const total = ref(0)

// 资产列表
const assetList = ref<Asset[]>([])

// 已选择的资产ID列表
const selectedAssetIds = computed(() => {
  return props.modelValue.map(item => item.id)
})

// 选中行
const selectedRows = ref<Asset[]>([])

// 模拟获取资产列表
const getAssetList = async (params: QueryParams) => {
  loading.value = true
  try {
    // 模拟API请求
    setTimeout(() => {
      // 生成资产列表 Mock 数据
      const list: Asset[] = Array.from({ length: 20 }).map((_, index) => {
        const id = `ASSET-${Date.now()}-${index}`
        return {
          id,
          assetCode: `ZC${String(100000 + index).padStart(6, '0')}`,
          assetName: `测试资产${index + 1}`,
          assetType: {
            id: index % 3 === 0 ? 1 : (index % 3 === 1 ? 2 : 3),
            name: index % 3 === 0 ? '水泵设备' : (index % 3 === 1 ? '电气设备' : '办公设备')
          },
          model: `型号-${index}`,
          specification: `规格-${index}`,
          unitId: 1,
          unitName: '水投集团',
          departmentId: index % 2 === 0 ? 1 : 2,
          departmentName: index % 2 === 0 ? '运营部' : '技术部',
          statusId: index % 5 === 0 ? 1 : (index % 5 === 1 ? 2 : (index % 5 === 2 ? 3 : (index % 5 === 3 ? 4 : 5))),
          statusName: index % 5 === 0 ? '在用' : (index % 5 === 1 ? '闲置' : (index % 5 === 2 ? '维修中' : (index % 5 === 3 ? '报废' : '待验收'))),
          purchaseDate: `2023-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`,
          purchasePrice: Math.floor(Math.random() * 10000) + 1000,
          warrantyPeriod: `${Math.floor(Math.random() * 3) + 1}年`,
          manufacturer: `制造商-${index}`,
          supplier: `供应商-${index}`,
          location: `位置-${index}`,
          responsiblePerson: `负责人-${index}`,
          serialNumber: `SN${Date.now()}${index}`,
          useStatus: 1,
          useStatusName: '正常',
          remark: `备注信息-${index}`,
          createBy: 'admin',
          createTime: '2023-01-01 12:00:00',
          updateBy: 'admin',
          updateTime: '2023-01-02 12:00:00'
        }
      })

      // 筛选状态（如果有）
      let filteredList = [...list]
      if (props.filterByStatus && props.filterByStatus.length > 0) {
        filteredList = list.filter(item => props.filterByStatus.includes(item.statusId))
      }

      // 筛选关键字
      if (params.keyword) {
        filteredList = filteredList.filter(item => 
          item.assetCode.includes(params.keyword) || 
          item.assetName.includes(params.keyword) ||
          item.model?.includes(params.keyword) ||
          item.specification?.includes(params.keyword)
        )
      }

      assetList.value = filteredList.slice(
        (params.pageNum - 1) * params.pageSize,
        params.pageNum * params.pageSize
      )
      total.value = filteredList.length
      loading.value = false
    }, 500)
  } catch (error) {
    console.error('获取资产列表失败', error)
    ElMessage.error('获取资产列表失败')
    loading.value = false
  }
}

// 初始加载及分页改变时加载
watch(
  () => [searchParams.value.pageNum, searchParams.value.pageSize, searchParams.value.keyword],
  () => {
    getAssetList(searchParams.value)
  },
  { immediate: true }
)

// 重置查询
const resetSearch = () => {
  searchParams.value = {
    pageNum: 1,
    pageSize: 10,
    keyword: '',
    assetTypeId: undefined,
    unitId: undefined,
    statusId: undefined,
    useStatus: undefined
  }
}

// 选择处理
const handleSelectionChange = (rows: Asset[]) => {
  selectedRows.value = rows
}

// 确认选择
const confirmSelection = () => {
  if (props.multiple) {
    // 合并已选和新选，避免重复
    const selectedMap = new Map<string, Asset>()
    props.modelValue.forEach(item => selectedMap.set(item.id, item))
    selectedRows.value.forEach(item => selectedMap.set(item.id, item))
    emit('update:modelValue', Array.from(selectedMap.values()))
  } else {
    // 单选模式
    if (selectedRows.value.length > 0) {
      emit('update:modelValue', [selectedRows.value[0]])
    }
  }
  emit('select', Array.from(selectedRows.value))
  dialogVisible.value = false
}

// 取消选择
const cancelSelection = () => {
  dialogVisible.value = false
  emit('cancel')
}

// 移除已选中的资产
const removeSelected = (asset: Asset) => {
  const newSelected = props.modelValue.filter(item => item.id !== asset.id)
  emit('update:modelValue', newSelected)
}

// 打开选择器
const open = () => {
  dialogVisible.value = true
  selectedRows.value = [...props.modelValue]
  getAssetList(searchParams.value)
}

// 暴露方法
defineExpose({
  open
})
</script>

<template>
  <div class="asset-selector">
    <!-- 已选资产展示区域 -->
    <div class="selected-assets" v-if="modelValue.length > 0">
      <el-tag
        v-for="asset in modelValue"
        :key="asset.id"
        class="asset-tag"
        closable
        :disabled="disabled"
        @close="removeSelected(asset)"
      >
        {{ asset.assetCode }} - {{ asset.assetName }}
      </el-tag>
    </div>
    <div v-else class="no-selected">
      {{ multiple ? '未选择资产' : '未选择资产' }}
    </div>

    <!-- 选择按钮 -->
    <el-button
      type="primary"
      size="small"
      :disabled="disabled"
      @click="open"
    >
      选择资产
    </el-button>

    <!-- 资产选择对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="title"
      width="80%"
      destroy-on-close
      append-to-body
    >
      <div class="asset-dialog-content">
        <!-- 搜索区域 -->
        <div class="search-area">
          <el-form :inline="true" :model="searchParams">
            <el-form-item label="关键字">
              <el-input
                v-model="searchParams.keyword"
                placeholder="资产编码/名称/型号/规格"
                clearable
                @keyup.enter="searchParams.pageNum = 1; getAssetList(searchParams)"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="searchParams.pageNum = 1; getAssetList(searchParams)">
                搜索
              </el-button>
              <el-button @click="resetSearch">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 表格区域 -->
        <el-table
          v-loading="loading"
          :data="assetList"
          style="width: 100%"
          @selection-change="handleSelectionChange"
          :row-key="(row: Asset) => row.id"
          :default-sort="{ prop: 'assetCode', order: 'ascending' }"
        >
          <el-table-column type="selection" width="55" :reserve-selection="true" />
          <el-table-column prop="assetCode" label="资产编号" min-width="120" sortable />
          <el-table-column prop="assetName" label="资产名称" min-width="150" />
          <el-table-column prop="assetType.name" label="资产类型" min-width="120" />
          <el-table-column prop="model" label="型号" min-width="120" />
          <el-table-column prop="specification" label="规格" min-width="120" />
          <el-table-column prop="statusName" label="状态" min-width="100">
            <template #default="{ row }">
              <el-tag
                :type="
                  row.statusId === 1 
                    ? 'success' 
                    : row.statusId === 2 
                    ? 'warning' 
                    : row.statusId === 3 
                    ? 'info' 
                    : row.statusId === 4 
                    ? 'danger' 
                    : 'primary'
                "
              >
                {{ row.statusName }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="unitName" label="所属单位" min-width="120" />
          <el-table-column prop="departmentName" label="所属部门" min-width="120" />
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="searchParams.pageNum"
            v-model:page-size="searchParams.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="getAssetList(searchParams)"
            @current-change="getAssetList(searchParams)"
          />
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelSelection">取消</el-button>
          <el-button type="primary" @click="confirmSelection">确认</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.asset-selector {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;

  .selected-assets {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    min-height: 32px;
    padding: 4px 0;

    .asset-tag {
      margin-right: 5px;
      margin-bottom: 5px;
    }
  }

  .no-selected {
    flex: 1;
    color: #999;
    padding: 7px 0;
  }

  .asset-dialog-content {
    display: flex;
    flex-direction: column;
    gap: 16px;

    .search-area {
      margin-bottom: 20px;
    }

    .pagination-container {
      margin-top: 20px;
      text-align: right;
    }
  }
}
</style> 