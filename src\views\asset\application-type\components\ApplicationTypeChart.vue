<template>
  <el-card class="chart-card">
    <template #header>
      <div class="card-header">
        <span>申请类型统计</span>
      </div>
    </template>
    <div class="chart-container">
      <div ref="pieChartRef" class="chart"></div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, inject } from 'vue'
import * as echarts from 'echarts'
// 注释原来的API导入
// import { getApplicationTypeStats } from '@/api/asset/applicationType'

// 从父组件获取模拟API方法
const getApplicationTypeStats = inject('getApplicationTypeStats', async () => {
  return {
    data: {
      enabled: 8,
      disabled: 4,
      byCategory: [],
      byAssetType: []
    }
  }
})

const pieChartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null

// 初始化图表
const initChart = () => {
  if (!pieChartRef.value) return
  chart = echarts.init(pieChartRef.value)
  updateChart()
}

// 更新图表数据
const updateChart = async () => {
  if (!chart) return
  try {
    const res = await getApplicationTypeStats()
    const { enabled, disabled, byCategory, byAssetType } = res.data

    // 状态饼图配置
    const statusOption = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'horizontal',
        bottom: 10,
        data: ['启用', '停用']
      },
      series: [
        {
          name: '申请类型状态',
          type: 'pie',
          radius: ['50%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '18',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: [
            { value: enabled, name: '启用', itemStyle: { color: '#67C23A' } },
            { value: disabled, name: '停用', itemStyle: { color: '#909399' } }
          ]
        }
      ]
    }

    chart.setOption(statusOption)
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 监听窗口大小变化
const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  chart?.dispose()
})
</script>

<style scoped>
.chart-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 300px;
}

.chart {
  width: 100%;
  height: 100%;
}
</style> 