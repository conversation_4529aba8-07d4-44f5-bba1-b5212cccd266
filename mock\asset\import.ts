import { MockMethod } from 'vite-plugin-mock'
import { faker } from '@faker-js/faker/locale/zh_CN'
import { Random } from 'mockjs'
import type { ImportPreviewData, ImportHistoryData, ImportDetail } from '@/types/asset/import'
import { mock } from 'mockjs'
import { getRequestQuery, getRequestBody } from '../util'
import { isVaildKey } from '../_utils'

// 资产类型列表
const assetTypes = ['固定资产', '无形资产', '耗材资产']

// 资产分类列表
const assetCategories = {
  '固定资产': ['办公设备', '电子设备', '机械设备', '运输工具', '生产设备'],
  '无形资产': ['软件', '专利', '著作权', '商标', '特许权'],
  '耗材资产': ['办公用品', '工具', '备品备件', '低值易耗品']
}

// 部门列表
const departments = ['财务部', '人力资源部', '信息技术部', '生产部', '行政部', '市场部', '研发部']

// 资产状态列表
const assetStatuses = ['在用', '闲置', '报废', '维修中', '待验收']

// 导入类型名称映射
const importTypeNameMap = {
  fixed: '固定资产',
  construction: '在建工程',
  inventory: '存货',
  maintenance: '维修资产'
}

// 生成随机错误信息
const generateErrorMessage = () => {
  const errorTypes = [
    '资产编码不符合规范',
    '资产名称不能为空',
    '资产分类不存在',
    '部门信息不存在',
    '资产原值必须为正数',
    '购置日期格式错误',
    '责任人不存在',
    '使用年限必须为正整数'
  ]
  return errorTypes[Math.floor(Math.random() * errorTypes.length)]
}

// 生成预览数据
export const generatePreviewData = (count: number, errorRate = 0.2): ImportPreviewData => {
  const list = []
  let invalidCount = 0

  for (let i = 0; i < count; i++) {
    const assetType = assetTypes[Math.floor(Math.random() * assetTypes.length)]
    const categories = assetCategories[assetType]
    const isValid = Math.random() > errorRate

    if (!isValid) {
      invalidCount++
    }

    list.push({
      rowIndex: i + 1,
      assetCode: isValid ? `ZC${Random.id().substring(0, 8).toUpperCase()}` : undefined,
      assetName: faker.commerce.productName(),
      assetType,
      assetCategory: categories[Math.floor(Math.random() * categories.length)],
      department: departments[Math.floor(Math.random() * departments.length)],
      purchaseDate: faker.date.past({ years: 3 }).toISOString().split('T')[0],
      originValue: isValid ? parseFloat(faker.commerce.price({ min: 1000, max: 50000 })) : -100,
      status: assetStatuses[Math.floor(Math.random() * assetStatuses.length)],
      location: faker.location.streetAddress(),
      responsible: faker.person.fullName(),
      serialNumber: faker.string.alphanumeric(10).toUpperCase(),
      manufacturer: faker.company.name(),
      specModel: `${faker.commerce.productMaterial()}-${faker.number.int(1000)}`,
      usefulLife: isValid ? faker.number.int({ min: 1, max: 10 }) : undefined,
      errorMessage: isValid ? undefined : generateErrorMessage(),
      isValid
    })
  }

  return {
    totalCount: count,
    validCount: count - invalidCount,
    invalidCount,
    list
  }
}

// 生成导入历史数据
export const generateHistoryData = (count: number): ImportHistoryData => {
  const list = []

  for (let i = 0; i < count; i++) {
    const totalCount = faker.number.int({ min: 50, max: 500 })
    const failCount = faker.number.int({ min: 0, max: Math.floor(totalCount * 0.3) })
    const successCount = totalCount - failCount
    const status = failCount === 0 ? '成功' : (successCount === 0 ? '失败' : '部分成功')

    list.push({
      id: Random.id(),
      fileName: `资产导入_${faker.date.recent().toISOString().split('T')[0]}.xlsx`,
      importType: assetTypes[Math.floor(Math.random() * assetTypes.length)],
      importTime: faker.date.recent().toISOString().replace('T', ' ').substring(0, 19),
      importUser: faker.person.fullName(),
      totalCount,
      successCount,
      failCount,
      status,
      errorFileId: failCount > 0 ? Random.id() : undefined
    })
  }

  return {
    total: 100,
    list
  }
}

// 生成导入详情
export const generateImportDetail = (id: string): ImportDetail => {
  const totalCount = faker.number.int({ min: 50, max: 500 })
  const failCount = faker.number.int({ min: 0, max: Math.floor(totalCount * 0.3) })
  const successCount = totalCount - failCount
  const status = failCount === 0 ? '成功' : (successCount === 0 ? '失败' : '部分成功')
  
  const errorList = failCount > 0 ? Array.from({ length: failCount }, (_, index) => ({
    rowIndex: faker.number.int({ min: 1, max: totalCount }),
    assetName: faker.commerce.productName(),
    errorMessage: generateErrorMessage()
  })) : undefined

  return {
    id,
    fileName: `资产导入_${faker.date.recent().toISOString().split('T')[0]}.xlsx`,
    importType: assetTypes[Math.floor(Math.random() * assetTypes.length)],
    importTime: faker.date.recent().toISOString().replace('T', ' ').substring(0, 19),
    importUser: faker.person.fullName(),
    totalCount,
    successCount,
    failCount,
    status,
    errorList,
    errorFileId: failCount > 0 ? Random.id() : undefined
  }
}

// 生成导入资产预览数据
function generateImportPreviewData(fileId: string, importType: string) {
  const total = faker.number.int({ min: 50, max: 200 })
  const invalid = faker.number.int({ min: 0, max: Math.floor(total * 0.2) })
  const valid = total - invalid
  
  const list = Array.from({ length: 20 }).map((_, index) => {
    const hasError = index < invalid
    return {
      id: faker.string.uuid(),
      assetCode: faker.number.int({ min: 10000, max: 99999 }).toString(),
      assetName: faker.commerce.productName(),
      assetType: ['办公设备', '生产设备', '基础设施', '运输设备'][faker.number.int({ min: 0, max: 3 })],
      assetCategory: ['固定资产', '流动资产', '无形资产'][faker.number.int({ min: 0, max: 2 })],
      department: ['运营部', '财务部', '工程部', '市政部', '行政部'][faker.number.int({ min: 0, max: 4 })],
      purchaseDate: faker.date.past().toISOString().split('T')[0],
      price: parseFloat(faker.commerce.price({ min: 1000, max: 500000 })),
      status: ['在用', '闲置', '维修中'][faker.number.int({ min: 0, max: 2 })],
      hasError,
      errorMsg: hasError ? ['资产编码重复', '资产名称不能为空', '采购日期格式错误', '部门不存在', '资产类型不存在'][faker.number.int({ min: 0, max: 4 })] : undefined
    }
  })
  
  return {
    fileId,
    importType,
    total,
    valid,
    invalid,
    list
  }
}

// 生成导入历史数据
function generateImportHistoryData(count: number = 10) {
  return Array.from({ length: count }).map(() => {
    const totalCount = faker.number.int({ min: 50, max: 500 })
    const failCount = faker.number.int({ min: 0, max: Math.floor(totalCount * 0.3) })
    const successCount = totalCount - failCount
    const status = ['处理中', '已完成', '已失败'][faker.number.int({ min: 0, max: 2 })]
    const importType = ['设备资产', '基础设施', '无形资产', '流动资产'][faker.number.int({ min: 0, max: 3 })]
    
    return {
      id: faker.string.uuid(),
      importType,
      fileName: `${importType}导入_${faker.date.recent().toISOString().split('T')[0]}.xlsx`,
      totalCount,
      successCount,
      failCount,
      status,
      operatorName: faker.person.fullName(),
      importTime: faker.date.recent().toISOString(),
      errorFileId: failCount > 0 ? faker.string.uuid() : undefined
    }
  })
}

// 生成导入详情数据
function generateImportDetail(id: string) {
  const totalCount = faker.number.int({ min: 50, max: 500 })
  const failCount = faker.number.int({ min: 0, max: Math.floor(totalCount * 0.3) })
  const successCount = totalCount - failCount
  const status = ['处理中', '已完成', '已失败'][faker.number.int({ min: 0, max: 2 })]
  const importType = ['设备资产', '基础设施', '无形资产', '流动资产'][faker.number.int({ min: 0, max: 3 })]
  const errorFileId = failCount > 0 ? faker.string.uuid() : undefined
  
  const errorList = failCount > 0
    ? Array.from({ length: Math.min(failCount, 20) }).map((_, index) => ({
        id: faker.string.uuid(),
        rowNum: faker.number.int({ min: 2, max: 500 }),
        assetCode: faker.number.int({ min: 10000, max: 99999 }).toString(),
        assetName: faker.commerce.productName(),
        errorMsg: ['资产编码重复', '资产名称不能为空', '采购日期格式错误', '部门不存在', '资产类型不存在'][faker.number.int({ min: 0, max: 4 })]
      }))
    : undefined
  
  return {
    id,
    importType,
    fileName: `${importType}导入_${faker.date.recent().toISOString().split('T')[0]}.xlsx`,
    totalCount,
    successCount,
    failCount,
    status,
    operatorName: faker.person.fullName(),
    importTime: faker.date.recent().toISOString(),
    errorList,
    errorFileId
  }
}

// 随机生成导入的资产数据
const generateImportAssets = (count: number, errorCount = 0) => {
  const assets = []
  for (let i = 0; i < count; i++) {
    const hasError = i < errorCount
    assets.push({
      id: `asset_${mock('@guid')}`,
      assetCode: mock('@string("upper", 2)') + '-' + mock('@integer(10000, 99999)'),
      assetName: mock('@ctitle(4, 8)') + '设备',
      department: mock('@ctitle(3, 6)') + '部门',
      category: mock('@ctitle(2, 4)') + '设备',
      location: mock('@province') + mock('@city') + mock('@ctitle(3, 6)'),
      purchaseDate: mock('@date("yyyy-MM-dd")'),
      originalValue: mock('@float(1000, 100000, 2, 2)'),
      status: mock('@pick(["在用", "闲置", "报废", "维修"])'),
      errorMsg: hasError ? mock('@ctitle(5, 15)') + '格式错误' : '',
      hasError
    })
  }
  return assets
}

// 随机生成导入历史记录
const generateImportHistory = (count: number) => {
  const list = []
  for (let i = 0; i < count; i++) {
    const importType = mock('@pick(["fixed", "construction", "inventory", "maintenance"])')
    const totalCount = mock('@integer(10, 200)')
    const failCount = mock('@integer(0, 20)')
    const successCount = totalCount - failCount
    const status = ['处理中', '已完成', '已失败'][faker.number.int({ min: 0, max: 2 })]
    const hasErrorFile = failCount > 0

    list.push({
      id: `import_${mock('@guid')}`,
      importType,
      importTypeName: importTypeNameMap[importType as keyof typeof importTypeNameMap],
      fileName: `${importTypeNameMap[importType as keyof typeof importTypeNameMap]}导入_${mock('@date("yyyyMMdd")').substring(2)}.xlsx`,
      totalCount,
      successCount,
      failCount,
      status,
      errorFileId: hasErrorFile ? `error_${mock('@guid')}` : undefined,
      createdBy: mock('@cname'),
      createdTime: mock('@datetime("yyyy-MM-dd HH:mm:ss")')
    })
  }
  return list.sort((a, b) => new Date(b.createdTime).getTime() - new Date(a.createdTime).getTime())
}

// 生成错误详情列表
const generateErrorList = (count: number) => {
  const list = []
  for (let i = 0; i < count; i++) {
    list.push({
      id: `error_${mock('@guid')}`,
      row: mock('@integer(2, 100)'),
      column: mock('@pick(["资产编号", "资产名称", "部门", "类别", "地点", "购置日期", "原值", "状态"])'),
      value: mock('@ctitle(2, 6)'),
      errorMsg: mock('@ctitle(5, 15)') + '格式错误'
    })
  }
  return list.sort((a, b) => a.row - b.row)
}

// 导入历史数据存储
const importHistoryData = generateImportHistory(20)

export default [
  // 上传导入文件
  {
    url: '/api/asset/import/upload',
    method: 'post',
    response: () => {
      return {
        code: 200,
        message: '上传成功',
        data: {
          fileId: `file_${mock('@guid')}`
        }
      }
    }
  },

  // 获取导入预览数据
  {
    url: '/api/asset/import/preview',
    method: 'get',
    response: (request: any) => {
      const { fileId, importType } = getRequestQuery(request)
      if (!fileId || !importType) {
        return {
          code: 400,
          message: '参数不完整',
          data: null
        }
      }

      const totalCount = mock('@integer(5, 50)')
      const invalidCount = mock('@integer(0, Math.min(10, totalCount))')
      const validCount = totalCount - invalidCount

      return {
        code: 200,
        message: '获取预览成功',
        data: {
          fileId,
          totalCount,
          validCount,
          invalidCount,
          items: generateImportAssets(totalCount, invalidCount)
        }
      }
    }
  },

  // 确认导入
  {
    url: '/api/asset/import/confirm',
    method: 'post',
    response: (request: any) => {
      const { fileId, importType } = getRequestBody(request)
      if (!fileId || !importType) {
        return {
          code: 400,
          message: '参数不完整',
          data: null
        }
      }

      const totalCount = mock('@integer(5, 50)')
      const failCount = mock('@integer(0, Math.min(5, totalCount))')
      const successCount = totalCount - failCount
      const status = failCount > 0 && failCount === totalCount ? 'failed' : (failCount > 0 ? 'success' : 'success')
      const hasErrorFile = failCount > 0

      const newImport = {
        id: `import_${mock('@guid')}`,
        importType,
        importTypeName: importTypeNameMap[importType as keyof typeof importTypeNameMap],
        fileName: `${importTypeNameMap[importType as keyof typeof importTypeNameMap]}导入_${mock('@date("yyyyMMdd")').substring(2)}.xlsx`,
        totalCount,
        successCount,
        failCount,
        status,
        errorFileId: hasErrorFile ? `error_${mock('@guid')}` : undefined,
        createdBy: '当前用户',
        createdTime: new Date().toISOString().replace('T', ' ').substring(0, 19)
      }

      importHistoryData.unshift(newImport)

      return {
        code: 200,
        message: '导入成功',
        data: {
          importId: newImport.id
        }
      }
    }
  },

  // 获取导入历史记录
  {
    url: '/api/asset/import/history',
    method: 'get',
    response: (request: any) => {
      const { pageNum = 1, pageSize = 10, keyword = '' } = getRequestQuery(request)
      
      let filteredData = importHistoryData
      if (keyword) {
        filteredData = importHistoryData.filter(item => 
          item.fileName.includes(keyword) || 
          item.importTypeName.includes(keyword)
        )
      }
      
      const startIndex = (pageNum - 1) * pageSize
      const endIndex = startIndex + pageSize
      const paginatedData = filteredData.slice(startIndex, endIndex)
      
      return {
        code: 200,
        message: '获取导入历史成功',
        data: {
          total: filteredData.length,
          list: paginatedData
        }
      }
    }
  },

  // 获取导入详情
  {
    url: '/api/asset/import/detail/:id',
    method: 'get',
    response: (request: any) => {
      const id = request.url.split('/').pop()
      
      const importRecord = importHistoryData.find(item => item.id === id)
      if (!importRecord) {
        return {
          code: 404,
          message: '导入记录不存在',
          data: null
        }
      }
      
      const errorList = importRecord.failCount > 0 
        ? generateErrorList(importRecord.failCount) 
        : []
      
      return {
        code: 200,
        message: '获取导入详情成功',
        data: {
          ...importRecord,
          errorList
        }
      }
    }
  },

  // 获取导入模板
  {
    url: '/api/asset/import/template',
    method: 'get',
    response: (request: any) => {
      const { importType } = getRequestQuery(request)
      if (!importType) {
        return {
          code: 400,
          message: '参数不完整',
          data: null
        }
      }
      
      return {
        code: 200,
        message: '获取模板成功',
        data: {
          url: `/templates/${importType}_asset_import_template.xlsx`
        }
      }
    }
  },

  // 获取错误文件
  {
    url: '/api/asset/import/error-file/:id',
    method: 'get',
    response: (request: any) => {
      const errorFileId = request.url.split('/').pop()
      
      return {
        code: 200,
        message: '获取错误文件成功',
        data: {
          url: `/error-files/${errorFileId}.xlsx`
        }
      }
    }
  }
] as MockMethod[] 