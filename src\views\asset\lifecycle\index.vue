<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getMyProcessList } from '@/api/asset/lifecycle'
import { ApprovalStatus } from '@/types/asset/lifecycle'
import * as echarts from 'echarts'

// 路由
const router = useRouter()
const route = useRoute()

// 状态与数据
const loading = ref(false)
const activeTab = ref('overview')

// 各模块的标签页状态
const acceptanceTab = ref('pending')
const transferTab = ref('apply')
const idleTab = ref('idle')
const changeTab = ref('apply')
const scrapTab = ref('apply')
const maintenanceTab = ref('apply')  // 添加维修标签页状态

// 各模块的待办统计
const acceptancePendingCount = ref(0)
const transferPendingCount = ref(0)
const idlePendingCount = ref(0)
const returnPendingCount = ref(0)
const changePendingCount = ref(0)
const scrapPendingCount = ref(0)
const maintenancePendingCount = ref(0)  // 添加维修待办统计

// 图表相关引用
const statusChartRef = ref(null)
const valueChartRef = ref(null)
const processChartRef = ref(null)

// 资产状态统计
const assetStatusCount = reactive({
  total: 0,
  inUse: 0,
  idle: 0,
  transfer: 0,
  scrap: 0,
  maintenance: 0
})

// 资产价值统计
const assetValueStats = reactive({
  totalValue: 0,
  depreciation: 0,
  netValue: 0,
  monthlyDepreciation: 0
})

// 流程审批统计
const processStats = reactive({
  total: 0,
  pending: 0,
  processing: 0,
  approved: 0,
  rejected: 0
})

// 每月流程统计数据
interface MonthlyData {
  months: string[];
  typeData: {
    acceptance: number[];
    transfer: number[];
    idle: number[];
    return: number[];
    scrap: number[];
    change: number[];
    maintenance: number[];
  };
  valueData: {
    acquisition: number[];
    depreciation: number[];
    maintenance: number[];
  };
}
const monthlyStats = ref<MonthlyData>({
  months: [],
  typeData: {
    acceptance: [],
    transfer: [],
    idle: [],
    return: [],
    scrap: [],
    change: [],
    maintenance: []
  },
  valueData: {
    acquisition: [],
    depreciation: [],
    maintenance: []
  }
});

// 个人申请的流程列表
interface ProcessItem {
  id: string;
  processNo: string;
  processType: string;
  processName: string;
  title: string;
  icon: string;
  applyTime: string;
  status: ApprovalStatus;
  statusText: string;
  currentNode: string;
  currentHandler: string;
  assetType: string;
  assetValue: number;
  approvalLevel: string;
  statusTab: string;
}
const myProcessList = ref<ProcessItem[]>([])
const myProcessLoading = ref(false)

// 资产验收相关数据
interface AcceptanceItem {
  id: string;
  orderNo: string;
  assetName: string;
  assetCode: string;
  assetType: string;
  department: string;
  applyUser: string;
  applyTime: string;
  status: string;
  reviewUser: string;
  reviewTime: string;
}

const acceptanceList = ref<AcceptanceItem[]>([])
const acceptanceLoading = ref(false)

// 资产调拨相关数据
interface TransferItem {
  id: string;
  orderNo: string;
  assetName: string;
  assetCode: string;
  sourceDept: string;
  targetDept: string;
  applyUser: string;
  applyTime: string;
  status: string;
  approvalLevel: string;
}

const transferList = ref<TransferItem[]>([])
const transferLoading = ref(false)

// 资产闲置与回转相关数据
interface IdleItem {
  id: string;
  orderNo: string;
  assetName: string;
  assetCode: string;
  department: string;
  idleReason: string;
  applyUser: string;
  applyTime: string;
  status: string;
  estimatedTime: string;
}

const idleList = ref<IdleItem[]>([])
const idleLoading = ref(false)

interface ReturnItem {
  id: string;
  orderNo: string;
  assetName: string;
  assetCode: string;
  department: string;
  idleTime: string;
  returnReason: string;
  applyUser: string;
  applyTime: string;
  status: string;
}

const returnList = ref<ReturnItem[]>([])
const returnLoading = ref(false)

// 资产变更相关数据
interface ChangeItem {
  id: string;
  orderNo: string;
  assetName: string;
  assetCode: string;
  department: string;
  changeType: string;
  changeContent: string;
  applyUser: string;
  applyTime: string;
  status: string;
}

const changeList = ref<ChangeItem[]>([])
const changeLoading = ref(false)

// 资产报废相关数据
interface ScrapItem {
  id: string;
  orderNo: string;
  assetName: string;
  assetCode: string;
  department: string;
  scrapReason: string;
  originalValue: number;
  netValue: number;
  applyUser: string;
  applyTime: string;
  status: string;
}

const scrapList = ref<ScrapItem[]>([])
const scrapLoading = ref(false)

// 资产维修相关数据
interface MaintenanceItem {
  id: string;
  requestNo: string;
  assetName: string;
  assetCode: string;
  assetType: string;
  department: string;
  location: string;
  faultDescription: string;
  priority: string;
  requestUser: string;
  requestTime: string;
  status: string;
  assignedTo?: string;
  plannedEndTime?: string;
  actualEndTime?: string;
  maintenanceResult?: string;
  totalCost?: number;
}

const maintenanceList = ref<MaintenanceItem[]>([])
const maintenanceLoading = ref(false)

// 获取我的流程列表
const getMyProcesses = async () => {
  myProcessLoading.value = true
  try {
    // 模拟数据
    setTimeout(() => {
      const processes = []
      const processTypes = [
        { type: 'acceptance', name: '资产验收', icon: 'Check' },
        { type: 'transfer', name: '资产调拨', icon: 'Position' },
        { type: 'idle', name: '资产闲置', icon: 'Timer' },
        { type: 'return', name: '资产回转', icon: 'Refresh' },
        { type: 'scrap', name: '资产报废', icon: 'Delete' },
        { type: 'change', name: '资产变更', icon: 'Edit' },
        { type: 'maintenance', name: '资产维修', icon: 'Tools' }
      ]
      
      // 重置各模块待办计数
      acceptancePendingCount.value = 0
      transferPendingCount.value = 0
      idlePendingCount.value = 0
      returnPendingCount.value = 0
      changePendingCount.value = 0
      scrapPendingCount.value = 0
      maintenancePendingCount.value = 0
      
      for (let i = 0; i < 12; i++) {
        const randomTypeIndex = Math.floor(Math.random() * processTypes.length)
        const randomType = processTypes[randomTypeIndex]
        const statuses = [
          ApprovalStatus.PENDING,
          ApprovalStatus.APPROVED,
          ApprovalStatus.REJECTED,
          ApprovalStatus.PROCESSING
        ]
        const statusIndex = Math.floor(Math.random() * statuses.length)
        const status = statuses[statusIndex]
        
        // 统计各类型的待办数量
        if (status === ApprovalStatus.PENDING) {
          if (randomType.type === 'acceptance') acceptancePendingCount.value++
          else if (randomType.type === 'transfer') transferPendingCount.value++
          else if (randomType.type === 'idle') idlePendingCount.value++
          else if (randomType.type === 'return') returnPendingCount.value++
          else if (randomType.type === 'change') changePendingCount.value++
          else if (randomType.type === 'scrap') scrapPendingCount.value++
          else if (randomType.type === 'maintenance') maintenancePendingCount.value++
        }
        
        processes.push({
          id: `process-${Date.now()}-${i}`,
          processNo: `LC${String(10000 + i).padStart(5, '0')}`,
          processType: randomType.type,
          processName: randomType.name,
          title: `${randomType.name}申请-${i + 1}`,
          icon: randomType.icon,
          applyTime: `2023-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')} 12:00:00`,
          status,
          statusText: status === ApprovalStatus.APPROVED
            ? '已批准'
            : status === ApprovalStatus.REJECTED
            ? '已拒绝'
            : status === ApprovalStatus.PENDING
            ? '待审批'
            : '审批中',
          currentNode: '部门审批',
          currentHandler: '审批人',
          assetType: Math.random() > 0.5 ? '生产类' : '非生产类',
          assetValue: Math.floor(Math.random() * 1000000),
          approvalLevel: Math.random() > 0.7 ? '集团级' : '水厂级',
          statusTab: status === ApprovalStatus.PENDING ? 'pending' : status === ApprovalStatus.APPROVED ? 'completed' : 'processing'
        })
      }
      
      myProcessList.value = processes
      myProcessLoading.value = false
    }, 500)
    
    // 实际API调用
    // const { data } = await getMyProcessList({ pageNum: 1, pageSize: 10 })
    // myProcessList.value = data.rows
  } catch (error) {
    console.error('获取我的流程列表失败', error)
    ElMessage.error('获取我的流程列表失败')
  } finally {
    myProcessLoading.value = false
  }
}

// 加载统计数据
const loadStatistics = () => {
  loading.value = true
  try {
    // 模拟资产状态统计
    assetStatusCount.total = 1250
    assetStatusCount.inUse = 850
    assetStatusCount.idle = 150
    assetStatusCount.transfer = 100
    assetStatusCount.scrap = 100
    assetStatusCount.maintenance = 50
    
    // 模拟资产价值统计
    assetValueStats.totalValue = 50000000
    assetValueStats.depreciation = 15000000
    assetValueStats.netValue = 35000000
    assetValueStats.monthlyDepreciation = 500000
    
    // 模拟流程审批统计
    processStats.total = 125
    processStats.pending = 32
    processStats.processing = 18
    processStats.approved = 65
    processStats.rejected = 10
    
    // 模拟各模块的待办数量
    acceptancePendingCount.value = 12
    transferPendingCount.value = 8
    idlePendingCount.value = 5
    returnPendingCount.value = 3
    changePendingCount.value = 7
    scrapPendingCount.value = 4
    maintenancePendingCount.value = 15
    
    // 模拟每月流程数据
    const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
    const typeData = {
      acceptance: [],
      transfer: [],
      idle: [],
      return: [],
      scrap: [],
      change: [],
      maintenance: []
    }
    
    const valueData = {
      acquisition: [],
      depreciation: [],
      maintenance: []
    }
    
    for (let i = 0; i < 12; i++) {
      typeData.acceptance.push(Math.floor(Math.random() * 10) + 1)
      typeData.transfer.push(Math.floor(Math.random() * 8) + 1)
      typeData.idle.push(Math.floor(Math.random() * 7) + 1)
      typeData.return.push(Math.floor(Math.random() * 6) + 1)
      typeData.scrap.push(Math.floor(Math.random() * 5) + 1)
      typeData.change.push(Math.floor(Math.random() * 9) + 1)
      typeData.maintenance.push(Math.floor(Math.random() * 12) + 1)
      
      valueData.acquisition.push(Math.floor(Math.random() * 1000000))
      valueData.depreciation.push(Math.floor(Math.random() * 300000))
      valueData.maintenance.push(Math.floor(Math.random() * 200000))
    }
    
    monthlyStats.value = {
      months,
      typeData,
      valueData
    }
    
    // 渲染图表
    nextTick(() => {
      initCharts()
    })
    
    loading.value = false
  } catch (error) {
    console.error('加载统计数据失败', error)
    loading.value = false
  }
}

// 点击流程卡片
const handleProcessClick = (process) => {
  router.push({
    name: `asset${process.processType.charAt(0).toUpperCase() + process.processType.slice(1)}Detail`,
    params: { id: process.id }
  })
}

// 跳转到流程列表
const goToProcessList = (type) => {
  router.push({ name: `asset${type.charAt(0).toUpperCase() + type.slice(1)}` })
}

// 跳转到业务模块并定位tab
const goToModule = (module: string, tab: string, params = {}) => {
  router.push({ 
    name: `asset${module.charAt(0).toUpperCase() + module.slice(1)}`, 
    query: { tab, ...params } 
  })
}

// 点击图表区域时的处理
const handleChartClick = (params, chartType) => {
  if (params.seriesType === 'pie') {
    // 饼图点击处理
    const statusName = params.name
    if (statusName === '在用') {
      // 在用资产不跳转
      return
    } else if (statusName === '闲置') {
      goToModule('idle', 'idle')
    } else if (statusName === '调拨中') {
      goToModule('transfer', 'processing')
    } else if (statusName === '报废') {
      goToModule('scrap', 'completed')
    } else if (statusName === '维修中') {
      goToModule('maintenance', 'processing')
    }
  } else if (params.seriesType === 'bar' || params.seriesType === 'line') {
    // 柱状图或折线图点击处理
    const seriesName = params.seriesName
    const monthIndex = params.dataIndex // 月份索引
    
    // 根据系列名称决定跳转目标
    if (seriesName === '验收') {
      goToModule('acceptance', 'all', { month: monthIndex + 1 })
    } else if (seriesName === '调拨') {
      goToModule('transfer', 'all', { month: monthIndex + 1 })
    } else if (seriesName === '闲置') {
      goToModule('idle', 'idle', { month: monthIndex + 1 })
    } else if (seriesName === '回转') {
      goToModule('idle', 'return', { month: monthIndex + 1 })
    } else if (seriesName === '报废') {
      goToModule('scrap', 'all', { month: monthIndex + 1 })
    } else if (seriesName === '变更') {
      goToModule('change', 'all', { month: monthIndex + 1 })
    } else if (seriesName === '维修') {
      goToModule('maintenance', 'all', { month: monthIndex + 1 })
    }
  }
}

// 初始化图表
const initCharts = () => {
  // 资产状态分布图表
  const statusChart = echarts.init(document.getElementById('status-chart'))
  statusChartRef.value = statusChart
  
  const statusOption = {
    title: {
      text: '资产状态分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '资产状态',
        type: 'pie',
        radius: '50%',
        data: [
          { value: assetStatusCount.inUse, name: '在用' },
          { value: assetStatusCount.idle, name: '闲置' },
          { value: assetStatusCount.transfer, name: '调拨中' },
          { value: assetStatusCount.scrap, name: '报废' },
          { value: assetStatusCount.maintenance, name: '维修中' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  
  statusChart.setOption(statusOption)
  statusChart.on('click', (params) => handleChartClick(params, 'status'))
  
  // 资产价值趋势图表
  const valueChart = echarts.init(document.getElementById('value-chart'))
  valueChartRef.value = valueChart
  
  const valueOption = {
    title: {
      text: '资产价值趋势',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['资产原值', '累计折旧', '维修费用'],
      top: '30px'
    },
    xAxis: {
      type: 'category',
      data: monthlyStats.value.months
    },
    yAxis: {
      type: 'value',
      name: '金额（元）'
    },
    series: [
      {
        name: '资产原值',
        type: 'line',
        data: monthlyStats.value.valueData.acquisition
      },
      {
        name: '累计折旧',
        type: 'line',
        data: monthlyStats.value.valueData.depreciation
      },
      {
        name: '维修费用',
        type: 'line',
        data: monthlyStats.value.valueData.maintenance
      }
    ]
  }
  
  valueChart.setOption(valueOption)
  
  // 流程类型分布图表
  const processChart = echarts.init(document.getElementById('process-chart'))
  processChartRef.value = processChart
  
  const processOption = {
    title: {
      text: '流程类型分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['验收', '调拨', '闲置', '回转', '报废', '变更', '维修'],
      top: '30px'
    },
    xAxis: {
      type: 'category',
      data: monthlyStats.value.months
    },
    yAxis: {
      type: 'value',
      name: '数量'
    },
    series: [
      {
        name: '验收',
        type: 'bar',
        stack: 'total',
        data: monthlyStats.value.typeData.acceptance
      },
      {
        name: '调拨',
        type: 'bar',
        stack: 'total',
        data: monthlyStats.value.typeData.transfer
      },
      {
        name: '闲置',
        type: 'bar',
        stack: 'total',
        data: monthlyStats.value.typeData.idle
      },
      {
        name: '回转',
        type: 'bar',
        stack: 'total',
        data: monthlyStats.value.typeData.return
      },
      {
        name: '报废',
        type: 'bar',
        stack: 'total',
        data: monthlyStats.value.typeData.scrap
      },
      {
        name: '变更',
        type: 'bar',
        stack: 'total',
        data: monthlyStats.value.typeData.change
      },
      {
        name: '维修',
        type: 'bar',
        stack: 'total',
        data: monthlyStats.value.typeData.maintenance
      }
    ]
  }
  
  processChart.setOption(processOption)
  processChart.on('click', (params) => handleChartClick(params, 'process'))
  
  // 窗口大小变化时重新调整图表
  window.addEventListener('resize', () => {
    statusChart && statusChart.resize()
    valueChart && valueChart.resize()
    processChart && processChart.resize()
  })
}

// 处理函数
const handleAddAcceptance = () => {
  router.push({ name: 'assetAcceptanceAdd' })
}

const handleAddTransfer = () => {
  router.push({ name: 'assetTransferAdd' })
}

const handleAddIdle = () => {
  router.push({ name: 'assetIdleAdd' })
}

const handleAddReturn = () => {
  router.push({ name: 'assetReturnAdd' })
}

const handleAddChange = () => {
  router.push({ name: 'assetChangeAdd' })
}

const handleAddScrap = () => {
  router.push({ name: 'assetScrapAdd' })
}

const handleAddMaintenance = () => {
  router.push({ name: 'assetMaintenanceAdd' })
}

// 加载各模块的数据
const loadAllModuleData = () => {
  loadAcceptanceData()
  loadTransferData()
  loadIdleAndReturnData()
  loadChangeData()
  loadScrapData()
  loadMaintenanceData()
}

// 加载验收数据
const loadAcceptanceData = () => {
  acceptanceLoading.value = true
  setTimeout(() => {
    const data: AcceptanceItem[] = []
    const statusOptions = ['待验收', '验收中', '已验收', '验收不通过']
    const assetTypes = ['办公设备', '生产设备', '运输设备', '电子设备', '通讯设备']
    const departments = ['行政部', '生产部', '财务部', '市场部', '人力资源部', '研发部']
    
    for (let i = 0; i < 25; i++) {
      const status = statusOptions[Math.floor(Math.random() * statusOptions.length)]
      const reviewTime = status === '已验收' || status === '验收不通过' 
        ? `2023-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`
        : ''
      
      data.push({
        id: `acceptance-${i}`,
        orderNo: `YS${String(10000 + i).padStart(5, '0')}`,
        assetName: `${assetTypes[Math.floor(Math.random() * assetTypes.length)]}-${i + 1}`,
        assetCode: `ZC${String(20000 + i).padStart(6, '0')}`,
        assetType: assetTypes[Math.floor(Math.random() * assetTypes.length)],
        department: departments[Math.floor(Math.random() * departments.length)],
        applyUser: `申请人${i + 1}`,
        applyTime: `2023-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`,
        status,
        reviewUser: reviewTime ? `审核人${i + 1}` : '',
        reviewTime
      })
    }
    
    acceptanceList.value = data
    acceptanceLoading.value = false
  }, 500)
}

// 加载调拨数据
const loadTransferData = () => {
  transferLoading.value = true
  setTimeout(() => {
    const data: TransferItem[] = []
    const statusOptions = ['调拨申请', '调拨中', '调拨完成', '已拒绝']
    const departments = ['行政部', '生产部', '财务部', '市场部', '人力资源部', '研发部']
    const approvalLevels = ['部门级', '水厂级', '集团级']
    
    for (let i = 0; i < 25; i++) {
      const sourceDept = departments[Math.floor(Math.random() * departments.length)]
      let targetDept = sourceDept
      while (targetDept === sourceDept) {
        targetDept = departments[Math.floor(Math.random() * departments.length)]
      }
      
      data.push({
        id: `transfer-${i}`,
        orderNo: `DB${String(10000 + i).padStart(5, '0')}`,
        assetName: `资产-${i + 1}`,
        assetCode: `ZC${String(20000 + i).padStart(6, '0')}`,
        sourceDept,
        targetDept,
        applyUser: `申请人${i + 1}`,
        applyTime: `2023-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`,
        status: statusOptions[Math.floor(Math.random() * statusOptions.length)],
        approvalLevel: approvalLevels[Math.floor(Math.random() * approvalLevels.length)]
      })
    }
    
    transferList.value = data
    transferLoading.value = false
  }, 500)
}

// 加载闲置与回转数据
const loadIdleAndReturnData = () => {
  idleLoading.value = true
  returnLoading.value = true
  
  setTimeout(() => {
    const idleData: IdleItem[] = []
    const returnData: ReturnItem[] = []
    const idleStatusOptions = ['闲置申请', '审批中', '已闲置', '已拒绝']
    const returnStatusOptions = ['回转申请', '审批中', '已回转', '已拒绝']
    const departments = ['行政部', '生产部', '财务部', '市场部', '人力资源部', '研发部']
    const idleReasons = ['设备更新', '暂无需求', '季节性停用', '待维修', '备用设备']
    const returnReasons = ['需求恢复', '替代设备损坏', '项目重启', '季节性使用', '维修完成']
    
    // 闲置数据
    for (let i = 0; i < 20; i++) {
      idleData.push({
        id: `idle-${i}`,
        orderNo: `XZ${String(10000 + i).padStart(5, '0')}`,
        assetName: `资产-${i + 1}`,
        assetCode: `ZC${String(20000 + i).padStart(6, '0')}`,
        department: departments[Math.floor(Math.random() * departments.length)],
        idleReason: idleReasons[Math.floor(Math.random() * idleReasons.length)],
        applyUser: `申请人${i + 1}`,
        applyTime: `2023-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`,
        status: idleStatusOptions[Math.floor(Math.random() * idleStatusOptions.length)],
        estimatedTime: `${Math.floor(Math.random() * 12) + 1}个月`
      })
    }
    
    // 回转数据
    for (let i = 0; i < 15; i++) {
      returnData.push({
        id: `return-${i}`,
        orderNo: `HZ${String(10000 + i).padStart(5, '0')}`,
        assetName: `资产-${i + 1}`,
        assetCode: `ZC${String(20000 + i).padStart(6, '0')}`,
        department: departments[Math.floor(Math.random() * departments.length)],
        idleTime: `${Math.floor(Math.random() * 12) + 1}个月`,
        returnReason: returnReasons[Math.floor(Math.random() * returnReasons.length)],
        applyUser: `申请人${i + 1}`,
        applyTime: `2023-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`,
        status: returnStatusOptions[Math.floor(Math.random() * returnStatusOptions.length)]
      })
    }
    
    idleList.value = idleData
    returnList.value = returnData
    idleLoading.value = false
    returnLoading.value = false
  }, 500)
}

// 加载变更数据
const loadChangeData = () => {
  changeLoading.value = true
  setTimeout(() => {
    const data: ChangeItem[] = []
    const statusOptions = ['变更申请', '审批中', '已变更', '已拒绝']
    const departments = ['行政部', '生产部', '财务部', '市场部', '人力资源部', '研发部']
    const changeTypes = ['基本信息', '使用人', '使用部门', '资产状态', '折旧方式']
    
    for (let i = 0; i < 25; i++) {
      const changeType = changeTypes[Math.floor(Math.random() * changeTypes.length)]
      let changeContent = ''
      
      switch (changeType) {
        case '基本信息':
          changeContent = '修改资产名称/编号/规格型号'
          break
        case '使用人':
          changeContent = `变更使用人为：使用人${Math.floor(Math.random() * 20) + 1}`
          break
        case '使用部门':
          changeContent = `变更使用部门为：${departments[Math.floor(Math.random() * departments.length)]}`
          break
        case '资产状态':
          changeContent = '变更为在用/闲置/维修状态'
          break
        case '折旧方式':
          changeContent = '变更折旧方式为年限平均法/工作量法'
          break
      }
      
      data.push({
        id: `change-${i}`,
        orderNo: `BG${String(10000 + i).padStart(5, '0')}`,
        assetName: `资产-${i + 1}`,
        assetCode: `ZC${String(20000 + i).padStart(6, '0')}`,
        department: departments[Math.floor(Math.random() * departments.length)],
        changeType,
        changeContent,
        applyUser: `申请人${i + 1}`,
        applyTime: `2023-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`,
        status: statusOptions[Math.floor(Math.random() * statusOptions.length)]
      })
    }
    
    changeList.value = data
    changeLoading.value = false
  }, 500)
}

// 加载报废数据
const loadScrapData = () => {
  scrapLoading.value = true
  setTimeout(() => {
    const data: ScrapItem[] = []
    const statusOptions = ['报废申请', '审批中', '已报废', '已拒绝']
    const departments = ['行政部', '生产部', '财务部', '市场部', '人力资源部', '研发部']
    const scrapReasons = ['设备损坏', '技术落后', '超使用年限', '维修成本过高', '自然灾害']
    
    for (let i = 0; i < 25; i++) {
      const originalValue = Math.floor(Math.random() * 50000) + 5000
      const netValue = Math.floor(originalValue * (Math.random() * 0.5))
      
      data.push({
        id: `scrap-${i}`,
        orderNo: `BF${String(10000 + i).padStart(5, '0')}`,
        assetName: `资产-${i + 1}`,
        assetCode: `ZC${String(20000 + i).padStart(6, '0')}`,
        department: departments[Math.floor(Math.random() * departments.length)],
        scrapReason: scrapReasons[Math.floor(Math.random() * scrapReasons.length)],
        originalValue,
        netValue,
        applyUser: `申请人${i + 1}`,
        applyTime: `2023-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`,
        status: statusOptions[Math.floor(Math.random() * statusOptions.length)]
      })
    }
    
    scrapList.value = data
    scrapLoading.value = false
  }, 500)
}

// 加载维修数据
const loadMaintenanceData = () => {
  maintenanceLoading.value = true
  setTimeout(() => {
    const data: MaintenanceItem[] = []
    const statusOptions = ['维修申请', '已派工', '维修中', '维修完成', '已验收', '已驳回']
    const priorityOptions = ['低', '中', '高', '紧急']
    const assetTypes = ['办公设备', '生产设备', '运输设备', '电子设备', '通讯设备']
    const departments = ['行政部', '生产部', '财务部', '市场部', '人力资源部', '研发部']
    const locations = ['一号水厂', '二号水厂', '三号水厂', '总部大楼', '泵站']
    const faultReasons = [
      '设备无法启动', '异常噪音', '漏水', '控制面板错误', '功率下降',
      '电路故障', '机械卡顿', '过热', '压力异常', '信号丢失'
    ]
    
    for (let i = 0; i < 25; i++) {
      const status = statusOptions[Math.floor(Math.random() * statusOptions.length)]
      const assignedTo = status !== '维修申请' ? `维修员${Math.floor(Math.random() * 10) + 1}` : undefined
      const plannedEndTime = assignedTo ? `2023-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}` : undefined
      const actualEndTime = status === '维修完成' || status === '已验收' ? `2023-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}` : undefined
      const maintenanceResult = actualEndTime ? '已修复，恢复正常运行' : undefined
      const totalCost = actualEndTime ? Math.floor(Math.random() * 10000) + 200 : undefined
      
      data.push({
        id: `maintenance-${i}`,
        requestNo: `WX${String(10000 + i).padStart(5, '0')}`,
        assetName: `${assetTypes[Math.floor(Math.random() * assetTypes.length)]}-${i + 1}`,
        assetCode: `ZC${String(20000 + i).padStart(6, '0')}`,
        assetType: assetTypes[Math.floor(Math.random() * assetTypes.length)],
        department: departments[Math.floor(Math.random() * departments.length)],
        location: locations[Math.floor(Math.random() * locations.length)],
        faultDescription: faultReasons[Math.floor(Math.random() * faultReasons.length)],
        priority: priorityOptions[Math.floor(Math.random() * priorityOptions.length)],
        requestUser: `申请人${i + 1}`,
        requestTime: `2023-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`,
        status,
        assignedTo,
        plannedEndTime,
        actualEndTime,
        maintenanceResult,
        totalCost
      })
    }
    
    maintenanceList.value = data
    maintenanceLoading.value = false
    
    // 更新待办数量
    maintenancePendingCount.value = maintenanceList.value.filter(item => item.status === '维修申请').length
  }, 500)
}

// 页面挂载时加载数据
onMounted(() => {
  loadStatistics()
  getMyProcesses()
  loadAllModuleData()
  if (route.query.tab) {
    activeTab.value = route.query.tab as string
  }
})

// 处理详情
const handleDetail = (id) => {
  // 实现详情页面的跳转逻辑
  console.log('查看详情:', id)
}

const handleApprove = (row) => {
  // 处理审批逻辑
  console.log('审批:', row)
}
</script>

<template>
  <div class="asset-lifecycle">
    <el-tabs v-model="activeTab" class="lifecycle-tabs">
      <!-- 资产总览 -->
      <el-tab-pane label="资产总览" name="overview">
        <!-- 业务模块卡片 -->
        <el-row :gutter="20" class="stat-cards">
          <el-col :span="4">
            <el-card shadow="hover" @click="goToModule('acceptance', 'pending')" class="module-card">
          <template #header>
            <div class="card-header">
                  <span>资产验收</span>
            </div>
          </template>
              <div class="card-content">
                <div class="number">{{ acceptancePendingCount }}</div>
                <div class="label">待验收</div>
                </div>
            </el-card>
          </el-col>
          <el-col :span="4">
            <el-card shadow="hover" @click="goToModule('transfer', 'apply')" class="module-card">
              <template #header>
                <div class="card-header">
                  <span>资产调拨</span>
                </div>
              </template>
              <div class="card-content">
                <div class="number">{{ transferPendingCount }}</div>
                <div class="label">待审批</div>
              </div>
            </el-card>
            </el-col>
          <el-col :span="4">
            <el-card shadow="hover" @click="goToModule('idle', 'idle')" class="module-card">
              <template #header>
                <div class="card-header">
                  <span>资产闲置</span>
                </div>
              </template>
              <div class="card-content">
                <div class="number">{{ idlePendingCount }}</div>
                <div class="label">待审批</div>
                </div>
            </el-card>
          </el-col>
          <el-col :span="4">
            <el-card shadow="hover" @click="goToModule('idle', 'return')" class="module-card">
              <template #header>
                <div class="card-header">
                  <span>资产回转</span>
              </div>
              </template>
              <div class="card-content">
                <div class="number">{{ returnPendingCount }}</div>
                <div class="label">待回转</div>
              </div>
            </el-card>
            </el-col>
          <el-col :span="4">
            <el-card shadow="hover" @click="goToModule('change', 'apply')" class="module-card">
              <template #header>
                <div class="card-header">
                  <span>资产变更</span>
                </div>
              </template>
              <div class="card-content">
                <div class="number">{{ changePendingCount }}</div>
                <div class="label">待审批</div>
                </div>
            </el-card>
          </el-col>
          <el-col :span="4">
            <el-card shadow="hover" @click="goToModule('scrap', 'apply')" class="module-card">
              <template #header>
                <div class="card-header">
                  <span>资产报废</span>
              </div>
              </template>
              <div class="card-content">
                <div class="number">{{ scrapPendingCount }}</div>
                <div class="label">待审批</div>
              </div>
            </el-card>
            </el-col>
        </el-row>

        <!-- 添加维修模块卡片，调整布局为每行4个卡片 -->
        <el-row :gutter="20" class="stat-cards">
          <el-col :span="6">
            <el-card shadow="hover" @click="goToModule('maintenance', 'apply')" class="module-card">
              <template #header>
                <div class="card-header">
                  <span>资产维修</span>
                </div>
              </template>
              <div class="card-content">
                <div class="number">{{ maintenancePendingCount }}</div>
                <div class="label">待维修</div>
                </div>
            </el-card>
            </el-col>
        </el-row>

        <!-- 资产状态统计 -->
        <el-row :gutter="20" class="stat-cards">
          <el-col :span="6">
            <el-card shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>资产总数</span>
                </div>
              </template>
              <div class="card-content">
                <div class="number">{{ assetStatusCount.total }}</div>
                <div class="label">台/套</div>
                </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>在用资产</span>
              </div>
              </template>
              <div class="card-content">
                <div class="number">{{ assetStatusCount.inUse }}</div>
                <div class="label">台/套</div>
              </div>
            </el-card>
            </el-col>
          <el-col :span="6">
            <el-card shadow="hover" @click="goToModule('idle', 'idle')" class="module-card">
              <template #header>
                <div class="card-header">
                  <span>闲置资产</span>
                </div>
              </template>
              <div class="card-content">
                <div class="number">{{ assetStatusCount.idle }}</div>
                <div class="label">台/套</div>
              </div>
        </el-card>
      </el-col>
          <el-col :span="6">
            <el-card shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>维修资产</span>
          </div>
              </template>
              <div class="card-content">
                <div class="number">{{ assetStatusCount.maintenance }}</div>
                <div class="label">台/套</div>
              </div>
        </el-card>
      </el-col>
        </el-row>

        <!-- 图表区域 - 可点击跳转 -->
        <el-row :gutter="20" class="chart-row">
          <el-col :span="12">
            <el-card shadow="hover">
              <div id="status-chart" style="height: 300px"></div>
        </el-card>
      </el-col>
          <el-col :span="12">
            <el-card shadow="hover">
              <div id="process-chart" style="height: 300px"></div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 资产价值统计 -->
        <el-row :gutter="20" class="stat-cards">
          <el-col :span="6">
            <el-card shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>资产总值</span>
          </div>
              </template>
              <div class="card-content">
                <div class="number">¥{{ assetValueStats.totalValue.toLocaleString() }}</div>
                <div class="label">元</div>
              </div>
        </el-card>
      </el-col>
          <el-col :span="6">
            <el-card shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>累计折旧</span>
          </div>
              </template>
              <div class="card-content">
                <div class="number">¥{{ assetValueStats.depreciation.toLocaleString() }}</div>
                <div class="label">元</div>
              </div>
        </el-card>
      </el-col>
          <el-col :span="6">
            <el-card shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>资产净值</span>
          </div>
              </template>
              <div class="card-content">
                <div class="number">¥{{ assetValueStats.netValue.toLocaleString() }}</div>
                <div class="label">元</div>
              </div>
        </el-card>
      </el-col>
          <el-col :span="6">
            <el-card shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>本月折旧</span>
          </div>
              </template>
              <div class="card-content">
                <div class="number">¥{{ assetValueStats.monthlyDepreciation.toLocaleString() }}</div>
                <div class="label">元</div>
              </div>
        </el-card>
      </el-col>
    </el-row>
    
        <!-- 价值趋势图表 -->
        <el-row :gutter="20" class="chart-row">
          <el-col :span="24">
            <el-card shadow="hover">
              <div id="value-chart" style="height: 300px"></div>
        </el-card>
      </el-col>
    </el-row>
    
        <!-- 待办事项 -->
        <el-card class="todo-list" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>待办事项</span>
              <el-button type="primary" link>查看全部</el-button>
            </div>
          </template>
          <el-table
            v-loading="myProcessLoading"
            :data="myProcessList.filter(item => item.status === ApprovalStatus.PENDING)"
            style="width: 100%"
          >
            <el-table-column prop="processNo" label="流程编号" width="120" />
            <el-table-column prop="processName" label="流程类型" width="120">
              <template #default="{ row }">
                <el-tag>{{ row.processName }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="title" label="标题" />
            <el-table-column prop="assetType" label="资产类型" width="100">
              <template #default="{ row }">
                <el-tag :type="row.assetType === '生产类' ? 'success' : 'info'">
                  {{ row.assetType }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="approvalLevel" label="审批级别" width="100">
              <template #default="{ row }">
                <el-tag :type="row.approvalLevel === '集团级' ? 'danger' : 'warning'">
                  {{ row.approvalLevel }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="currentNode" label="当前节点" width="120" />
            <el-table-column prop="currentHandler" label="处理人" width="120" />
            <el-table-column label="操作" width="120" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" link @click="goToModule(row.processType, row.statusTab)">
                  处理
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>
      
      <!-- 资产验收 -->
      <el-tab-pane label="资产验收" name="acceptance">
        <div class="process-content">
          <div class="process-header">
            <h3>资产验收</h3>
            <el-button type="primary" @click="handleAddAcceptance">新增验收</el-button>
                </div>
          <el-tabs v-model="acceptanceTab">
            <el-tab-pane label="待验收" name="pending">
              <el-table
                v-loading="acceptanceLoading"
                :data="acceptanceList.filter(item => item.status === '待验收')"
                style="width: 100%"
                border
              >
                <el-table-column type="selection" width="55" />
                <el-table-column prop="orderNo" label="验收单号" width="120" />
                <el-table-column prop="assetName" label="资产名称" min-width="120" />
                <el-table-column prop="assetCode" label="资产编码" width="120" />
                <el-table-column prop="assetType" label="资产类型" width="120">
                  <template #default="{ row }">
                    <el-tag size="small">{{ row.assetType }}</el-tag>
              </template>
            </el-table-column>
                <el-table-column prop="department" label="使用部门" width="120" />
                <el-table-column prop="applyUser" label="申请人" width="100" />
                <el-table-column prop="applyTime" label="申请时间" width="120" />
            <el-table-column prop="status" label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag type="warning" size="small">{{ row.status }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="180" fixed="right">
                  <template #default>
                    <el-button type="primary" link>验收</el-button>
                    <el-button type="info" link @click="handleDetail(row.id)">查看</el-button>
                    <el-button type="danger" link>驳回</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="验收中" name="processing">
              <el-table
                v-loading="acceptanceLoading"
                :data="acceptanceList.filter(item => item.status === '验收中')"
                style="width: 100%"
                border
              >
                <el-table-column type="selection" width="55" />
                <el-table-column prop="orderNo" label="验收单号" width="120" />
                <el-table-column prop="assetName" label="资产名称" min-width="120" />
                <el-table-column prop="assetCode" label="资产编码" width="120" />
                <el-table-column prop="assetType" label="资产类型" width="120">
                  <template #default="{ row }">
                    <el-tag size="small">{{ row.assetType }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="department" label="使用部门" width="120" />
                <el-table-column prop="applyUser" label="申请人" width="100" />
                <el-table-column prop="applyTime" label="申请时间" width="120" />
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag type="info" size="small">{{ row.status }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="150" fixed="right">
                  <template #default>
                    <el-button type="primary" link @click="handleApprove(row)">审批</el-button>
                    <el-button type="info" link @click="handleDetail(row.id)">查看</el-button>
                    <el-button type="danger" link>驳回</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="已验收" name="completed">
              <el-table
                v-loading="acceptanceLoading"
                :data="acceptanceList.filter(item => item.status === '已验收')"
                style="width: 100%"
                border
              >
                <el-table-column type="selection" width="55" />
                <el-table-column prop="orderNo" label="验收单号" width="120" />
                <el-table-column prop="assetName" label="资产名称" min-width="120" />
                <el-table-column prop="assetCode" label="资产编码" width="120" />
                <el-table-column prop="assetType" label="资产类型" width="120">
                  <template #default="{ row }">
                    <el-tag size="small">{{ row.assetType }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="department" label="使用部门" width="120" />
                <el-table-column prop="applyUser" label="申请人" width="100" />
                <el-table-column prop="reviewUser" label="审核人" width="100" />
                <el-table-column prop="reviewTime" label="审核时间" width="120" />
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag type="success" size="small">{{ row.status }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="100" fixed="right">
                  <template #default>
                    <el-button type="info" link @click="handleDetail(row.id)">查看</el-button>
                    <el-button type="primary" link>打印</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-tab-pane>
      
      <!-- 资产调拨 -->
      <el-tab-pane label="资产调拨" name="transfer">
        <div class="process-content">
          <div class="process-header">
            <h3>资产调拨</h3>
            <el-button type="primary" @click="handleAddTransfer">新增调拨</el-button>
          </div>
          <el-tabs v-model="transferTab">
            <el-tab-pane label="调拨申请" name="apply">
              <el-table
                v-loading="transferLoading"
                :data="transferList.filter(item => item.status === '调拨申请')"
                style="width: 100%"
                border
              >
                <el-table-column type="selection" width="55" />
                <el-table-column prop="orderNo" label="调拨单号" width="120" />
                <el-table-column prop="assetName" label="资产名称" min-width="120" />
                <el-table-column prop="assetCode" label="资产编码" width="120" />
                <el-table-column prop="sourceDept" label="原使用部门" width="120" />
                <el-table-column prop="targetDept" label="目标部门" width="120" />
                <el-table-column prop="approvalLevel" label="审批级别" width="100">
                  <template #default="{ row }">
                    <el-tag :type="row.approvalLevel === '集团级' ? 'danger' : row.approvalLevel === '水厂级' ? 'warning' : 'info'" size="small">
                      {{ row.approvalLevel }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="applyUser" label="申请人" width="100" />
                <el-table-column prop="applyTime" label="申请时间" width="120" />
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag type="warning" size="small">{{ row.status }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="180" fixed="right">
                  <template #default>
                    <el-button type="primary" link>审批</el-button>
                    <el-button type="info" link @click="handleDetail(row.id)">查看</el-button>
                    <el-button type="danger" link>驳回</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="调拨中" name="processing">
              <el-table
                v-loading="transferLoading"
                :data="transferList.filter(item => item.status === '调拨中')"
                style="width: 100%"
                border
              >
                <el-table-column type="selection" width="55" />
                <el-table-column prop="orderNo" label="调拨单号" width="120" />
                <el-table-column prop="assetName" label="资产名称" min-width="120" />
                <el-table-column prop="assetCode" label="资产编码" width="120" />
                <el-table-column prop="sourceDept" label="原使用部门" width="120" />
                <el-table-column prop="targetDept" label="目标部门" width="120" />
                <el-table-column prop="approvalLevel" label="审批级别" width="100">
                  <template #default="{ row }">
                    <el-tag :type="row.approvalLevel === '集团级' ? 'danger' : row.approvalLevel === '水厂级' ? 'warning' : 'info'" size="small">
                      {{ row.approvalLevel }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="applyUser" label="申请人" width="100" />
                <el-table-column prop="applyTime" label="申请时间" width="120" />
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag type="info" size="small">{{ row.status }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="150" fixed="right">
                  <template #default>
                    <el-button type="primary" link>确认完成</el-button>
                    <el-button type="info" link @click="handleDetail(row.id)">查看</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="调拨完成" name="completed">
              <el-table
                v-loading="transferLoading"
                :data="transferList.filter(item => item.status === '调拨完成')"
                style="width: 100%"
                border
              >
                <el-table-column type="selection" width="55" />
                <el-table-column prop="orderNo" label="调拨单号" width="120" />
                <el-table-column prop="assetName" label="资产名称" min-width="120" />
                <el-table-column prop="assetCode" label="资产编码" width="120" />
                <el-table-column prop="sourceDept" label="原使用部门" width="120" />
                <el-table-column prop="targetDept" label="目标部门" width="120" />
                <el-table-column prop="approvalLevel" label="审批级别" width="100">
                  <template #default="{ row }">
                    <el-tag :type="row.approvalLevel === '集团级' ? 'danger' : row.approvalLevel === '水厂级' ? 'warning' : 'info'" size="small">
                      {{ row.approvalLevel }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="applyUser" label="申请人" width="100" />
                <el-table-column prop="applyTime" label="申请时间" width="120" />
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag type="success" size="small">{{ row.status }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="100" fixed="right">
                  <template #default>
                    <el-button type="info" link @click="handleDetail(row.id)">查看</el-button>
                    <el-button type="primary" link>打印</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-tab-pane>
      
      <!-- 资产闲置与回转 -->
      <el-tab-pane label="资产闲置与回转" name="idle">
        <div class="process-content">
          <div class="process-header">
            <h3>资产闲置与回转</h3>
            <div class="header-buttons">
              <el-button type="primary" @click="handleAddIdle">新增闲置</el-button>
              <el-button type="success" @click="handleAddReturn">新增回转</el-button>
            </div>
          </div>
          <el-tabs v-model="idleTab">
            <el-tab-pane label="闲置资产" name="idle">
              <el-table
                v-loading="idleLoading"
                :data="idleList.filter(item => item.status === '已闲置')"
                style="width: 100%"
                border
              >
                <el-table-column type="selection" width="55" />
                <el-table-column prop="orderNo" label="闲置单号" width="120" />
                <el-table-column prop="assetName" label="资产名称" min-width="120" />
                <el-table-column prop="assetCode" label="资产编码" width="120" />
                <el-table-column prop="department" label="所属部门" width="120" />
                <el-table-column prop="idleReason" label="闲置原因" width="120" />
                <el-table-column prop="estimatedTime" label="预计闲置时间" width="120" />
                <el-table-column prop="applyUser" label="申请人" width="100" />
                <el-table-column prop="applyTime" label="申请时间" width="120" />
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag type="info" size="small">{{ row.status }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="150" fixed="right">
                  <template #default>
                    <el-button type="success" link @click="handleAddReturn">申请回转</el-button>
                    <el-button type="info" link @click="handleDetail(row.id)">查看</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="回转申请" name="return">
              <!-- 回转申请列表 -->
            </el-tab-pane>
            <el-tab-pane label="回转中" name="processing">
              <!-- 回转中列表 -->
            </el-tab-pane>
            <el-tab-pane label="回转完成" name="completed">
              <!-- 回转完成列表 -->
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-tab-pane>
      
      <!-- 资产变更 -->
      <el-tab-pane label="资产变更" name="change">
        <div class="process-content">
          <div class="process-header">
            <h3>资产变更</h3>
            <el-button type="primary" @click="handleAddChange">新增变更</el-button>
          </div>
          <el-tabs v-model="changeTab">
            <el-tab-pane label="变更申请" name="apply">
              <el-table
                v-loading="changeLoading"
                :data="changeList.filter(item => item.status === '变更申请')"
                style="width: 100%"
                border
              >
                <el-table-column type="selection" width="55" />
                <el-table-column prop="orderNo" label="变更单号" width="120" />
                <el-table-column prop="assetName" label="资产名称" min-width="120" />
                <el-table-column prop="assetCode" label="资产编码" width="120" />
                <el-table-column prop="department" label="所属部门" width="120" />
                <el-table-column prop="changeType" label="变更类型" width="120">
                  <template #default="{ row }">
                    <el-tag size="small">{{ row.changeType }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="changeContent" label="变更内容" min-width="150" show-overflow-tooltip />
                <el-table-column prop="applyUser" label="申请人" width="100" />
                <el-table-column prop="applyTime" label="申请时间" width="120" />
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag type="warning" size="small">{{ row.status }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="180" fixed="right">
                  <template #default>
                    <el-button type="primary" link>审批</el-button>
                    <el-button type="info" link @click="handleDetail(row.id)">查看</el-button>
                    <el-button type="danger" link>驳回</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="变更中" name="processing">
              <el-table
                v-loading="changeLoading"
                :data="changeList.filter(item => item.status === '审批中')"
                style="width: 100%"
                border
              >
                <el-table-column type="selection" width="55" />
                <el-table-column prop="orderNo" label="变更单号" width="120" />
                <el-table-column prop="assetName" label="资产名称" min-width="120" />
                <el-table-column prop="assetCode" label="资产编码" width="120" />
                <el-table-column prop="department" label="所属部门" width="120" />
                <el-table-column prop="changeType" label="变更类型" width="120">
                  <template #default="{ row }">
                    <el-tag size="small">{{ row.changeType }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="changeContent" label="变更内容" min-width="150" show-overflow-tooltip />
                <el-table-column prop="applyUser" label="申请人" width="100" />
                <el-table-column prop="applyTime" label="申请时间" width="120" />
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag type="info" size="small">{{ row.status }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="150" fixed="right">
                  <template #default>
                    <el-button type="primary" link>确认完成</el-button>
                    <el-button type="info" link @click="handleDetail(row.id)">查看</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="变更完成" name="completed">
              <el-table
                v-loading="changeLoading"
                :data="changeList.filter(item => item.status === '已变更')"
                style="width: 100%"
                border
              >
                <el-table-column type="selection" width="55" />
                <el-table-column prop="orderNo" label="变更单号" width="120" />
                <el-table-column prop="assetName" label="资产名称" min-width="120" />
                <el-table-column prop="assetCode" label="资产编码" width="120" />
                <el-table-column prop="department" label="所属部门" width="120" />
                <el-table-column prop="changeType" label="变更类型" width="120">
                  <template #default="{ row }">
                    <el-tag size="small">{{ row.changeType }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="changeContent" label="变更内容" min-width="150" show-overflow-tooltip />
                <el-table-column prop="applyUser" label="申请人" width="100" />
                <el-table-column prop="applyTime" label="申请时间" width="120" />
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag type="success" size="small">{{ row.status }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="100" fixed="right">
                  <template #default>
                    <el-button type="info" link @click="handleDetail(row.id)">查看</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-tab-pane>
      
      <!-- 资产报废 -->
      <el-tab-pane label="资产报废" name="scrap">
        <div class="process-content">
          <div class="process-header">
            <h3>资产报废</h3>
            <el-button type="primary" @click="handleAddScrap">新增报废</el-button>
          </div>
          <el-tabs v-model="scrapTab">
            <el-tab-pane label="报废申请" name="apply">
              <el-table
                v-loading="scrapLoading"
                :data="scrapList.filter(item => item.status === '报废申请')"
                style="width: 100%"
                border
              >
                <el-table-column type="selection" width="55" />
                <el-table-column prop="orderNo" label="报废单号" width="120" />
                <el-table-column prop="assetName" label="资产名称" min-width="120" />
                <el-table-column prop="assetCode" label="资产编码" width="120" />
                <el-table-column prop="department" label="所属部门" width="120" />
                <el-table-column prop="scrapReason" label="报废原因" width="150" show-overflow-tooltip />
                <el-table-column prop="originalValue" label="原值" width="120">
                  <template #default="{ row }">
                    ¥{{ row.originalValue.toLocaleString() }}
                  </template>
                </el-table-column>
                <el-table-column prop="netValue" label="净值" width="120">
                  <template #default="{ row }">
                    ¥{{ row.netValue.toLocaleString() }}
                  </template>
                </el-table-column>
                <el-table-column prop="applyUser" label="申请人" width="100" />
                <el-table-column prop="applyTime" label="申请时间" width="120" />
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag type="warning" size="small">{{ row.status }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="180" fixed="right">
                  <template #default>
                    <el-button type="primary" link>审批</el-button>
                    <el-button type="info" link @click="handleDetail(row.id)">查看</el-button>
                    <el-button type="danger" link>驳回</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="报废中" name="processing">
              <el-table
                v-loading="scrapLoading"
                :data="scrapList.filter(item => item.status === '审批中')"
                style="width: 100%"
                border
              >
                <el-table-column type="selection" width="55" />
                <el-table-column prop="orderNo" label="报废单号" width="120" />
                <el-table-column prop="assetName" label="资产名称" min-width="120" />
                <el-table-column prop="assetCode" label="资产编码" width="120" />
                <el-table-column prop="department" label="所属部门" width="120" />
                <el-table-column prop="scrapReason" label="报废原因" width="150" show-overflow-tooltip />
                <el-table-column prop="originalValue" label="原值" width="120">
                  <template #default="{ row }">
                    ¥{{ row.originalValue.toLocaleString() }}
                  </template>
                </el-table-column>
                <el-table-column prop="netValue" label="净值" width="120">
                  <template #default="{ row }">
                    ¥{{ row.netValue.toLocaleString() }}
                  </template>
                </el-table-column>
                <el-table-column prop="applyUser" label="申请人" width="100" />
                <el-table-column prop="applyTime" label="申请时间" width="120" />
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag type="info" size="small">{{ row.status }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="150" fixed="right">
                  <template #default>
                    <el-button type="primary" link>确认报废</el-button>
                    <el-button type="info" link @click="handleDetail(row.id)">查看</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="报废完成" name="completed">
              <el-table
                v-loading="scrapLoading"
                :data="scrapList.filter(item => item.status === '已报废')"
                style="width: 100%"
                border
              >
                <el-table-column type="selection" width="55" />
                <el-table-column prop="orderNo" label="报废单号" width="120" />
                <el-table-column prop="assetName" label="资产名称" min-width="120" />
                <el-table-column prop="assetCode" label="资产编码" width="120" />
                <el-table-column prop="department" label="所属部门" width="120" />
                <el-table-column prop="scrapReason" label="报废原因" width="150" show-overflow-tooltip />
                <el-table-column prop="originalValue" label="原值" width="120">
                  <template #default="{ row }">
                    ¥{{ row.originalValue.toLocaleString() }}
                  </template>
                </el-table-column>
                <el-table-column prop="netValue" label="净值" width="120">
                  <template #default="{ row }">
                    ¥{{ row.netValue.toLocaleString() }}
                  </template>
                </el-table-column>
                <el-table-column prop="applyUser" label="申请人" width="100" />
                <el-table-column prop="applyTime" label="申请时间" width="120" />
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag type="success" size="small">{{ row.status }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="150" fixed="right">
                  <template #default>
                    <el-button type="info" link @click="handleDetail(row.id)">查看</el-button>
                    <el-button type="primary" link>打印</el-button>
                    <el-button type="warning" link>处置</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-tab-pane>
      
      <!-- 资产维修 -->
      <el-tab-pane label="资产维修" name="maintenance">
        <div class="process-content">
          <div class="process-header">
            <h3>资产维修</h3>
            <el-button type="primary" @click="handleAddMaintenance">新增维修申请</el-button>
          </div>
          <el-tabs v-model="maintenanceTab">
            <el-tab-pane label="维修申请" name="apply">
              <el-table
                v-loading="maintenanceLoading"
                :data="maintenanceList.filter(item => item.status === '维修申请')"
                style="width: 100%"
                border
              >
                <el-table-column type="selection" width="55" />
                <el-table-column prop="requestNo" label="维修单号" width="120" />
                <el-table-column prop="assetName" label="资产名称" min-width="120" />
                <el-table-column prop="assetCode" label="资产编码" width="120" />
                <el-table-column prop="assetType" label="资产类型" width="120">
                  <template #default="{ row }">
                    <el-tag size="small">{{ row.assetType }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="department" label="所属部门" width="120" />
                <el-table-column prop="location" label="位置" width="120" />
                <el-table-column prop="faultDescription" label="故障描述" width="200" show-overflow-tooltip />
                <el-table-column prop="priority" label="优先级" width="80">
              <template #default="{ row }">
                <el-tag
                      :type="row.priority === '紧急' ? 'danger' : row.priority === '高' ? 'warning' : row.priority === '中' ? 'success' : 'info'" 
                      size="small"
                >
                      {{ row.priority }}
                </el-tag>
              </template>
            </el-table-column>
                <el-table-column prop="requestUser" label="申请人" width="100" />
                <el-table-column prop="requestTime" label="申请时间" width="120" />
                <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                    <el-tag type="warning" size="small">{{ row.status }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="180" fixed="right">
                  <template #default="{ row }">
                    <el-button type="primary" link>审批</el-button>
                    <el-button type="success" link>派工</el-button>
                    <el-button type="info" link @click="handleDetail(row.id)">查看</el-button>
                    <el-button type="danger" link>驳回</el-button>
              </template>
            </el-table-column>
          </el-table>
            </el-tab-pane>
            <el-tab-pane label="已派工" name="assigned">
              <el-table
                v-loading="maintenanceLoading"
                :data="maintenanceList.filter(item => item.status === '已派工')"
                style="width: 100%"
                border
              >
                <el-table-column type="selection" width="55" />
                <el-table-column prop="requestNo" label="维修单号" width="120" />
                <el-table-column prop="assetName" label="资产名称" min-width="120" />
                <el-table-column prop="assetCode" label="资产编码" width="120" />
                <el-table-column prop="faultDescription" label="故障描述" width="200" show-overflow-tooltip />
                <el-table-column prop="priority" label="优先级" width="80">
                  <template #default="{ row }">
                    <el-tag 
                      :type="row.priority === '紧急' ? 'danger' : row.priority === '高' ? 'warning' : row.priority === '中' ? 'success' : 'info'" 
                      size="small"
                    >
                      {{ row.priority }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="assignedTo" label="维修人员" width="120" />
                <el-table-column prop="plannedEndTime" label="计划完成时间" width="120" />
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag type="info" size="small">{{ row.status }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="220" fixed="right">
                  <template #default="{ row }">
                    <el-button type="primary" link>开始维修</el-button>
                    <el-button type="success" link>添加进度记录</el-button>
                    <el-button type="info" link @click="handleDetail(row.id)">查看</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="维修中" name="processing">
              <el-table
                v-loading="maintenanceLoading"
                :data="maintenanceList.filter(item => item.status === '维修中')"
                style="width: 100%"
                border
              >
                <el-table-column type="selection" width="55" />
                <el-table-column prop="requestNo" label="维修单号" width="120" />
                <el-table-column prop="assetName" label="资产名称" min-width="120" />
                <el-table-column prop="assetCode" label="资产编码" width="120" />
                <el-table-column prop="faultDescription" label="故障描述" width="200" show-overflow-tooltip />
                <el-table-column prop="priority" label="优先级" width="80">
                  <template #default="{ row }">
                    <el-tag 
                      :type="row.priority === '紧急' ? 'danger' : row.priority === '高' ? 'warning' : row.priority === '中' ? 'success' : 'info'" 
                      size="small"
                    >
                      {{ row.priority }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="assignedTo" label="维修人员" width="120" />
                <el-table-column prop="plannedEndTime" label="计划完成时间" width="120" />
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag type="warning" size="small">{{ row.status }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="280" fixed="right">
                  <template #default="{ row }">
                    <el-button type="primary" link>完成维修</el-button>
                    <el-button type="success" link>添加进度记录</el-button>
                    <el-button type="warning" link>添加配件/费用</el-button>
                    <el-button type="info" link @click="handleDetail(row.id)">查看</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="维修完成" name="completed">
              <el-table
                v-loading="maintenanceLoading"
                :data="maintenanceList.filter(item => item.status === '维修完成')"
                style="width: 100%"
                border
              >
                <el-table-column type="selection" width="55" />
                <el-table-column prop="requestNo" label="维修单号" width="120" />
                <el-table-column prop="assetName" label="资产名称" min-width="120" />
                <el-table-column prop="assetCode" label="资产编码" width="120" />
                <el-table-column prop="faultDescription" label="故障描述" width="200" show-overflow-tooltip />
                <el-table-column prop="maintenanceResult" label="维修结果" width="200" show-overflow-tooltip />
                <el-table-column prop="assignedTo" label="维修人员" width="120" />
                <el-table-column prop="actualEndTime" label="完成时间" width="120" />
                <el-table-column prop="totalCost" label="维修费用" width="120">
                  <template #default="{ row }">
                    <span v-if="row.totalCost">¥{{ row.totalCost.toLocaleString() }}</span>
                    <span v-else>-</span>
                  </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag type="success" size="small">{{ row.status }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="180" fixed="right">
                  <template #default="{ row }">
                    <el-button type="primary" link>验收</el-button>
                    <el-button type="warning" link>驳回</el-button>
                    <el-button type="info" link @click="handleDetail(row.id)">查看</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="已验收" name="accepted">
              <el-table
                v-loading="maintenanceLoading"
                :data="maintenanceList.filter(item => item.status === '已验收')"
                style="width: 100%"
                border
              >
                <el-table-column type="selection" width="55" />
                <el-table-column prop="requestNo" label="维修单号" width="120" />
                <el-table-column prop="assetName" label="资产名称" min-width="120" />
                <el-table-column prop="assetCode" label="资产编码" width="120" />
                <el-table-column prop="faultDescription" label="故障描述" width="180" show-overflow-tooltip />
                <el-table-column prop="maintenanceResult" label="维修结果" width="180" show-overflow-tooltip />
                <el-table-column prop="assignedTo" label="维修人员" width="100" />
                <el-table-column prop="actualEndTime" label="完成时间" width="120" />
                <el-table-column prop="totalCost" label="维修费用" width="120">
                  <template #default="{ row }">
                    <span v-if="row.totalCost">¥{{ row.totalCost.toLocaleString() }}</span>
                    <span v-else>-</span>
                  </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag type="success" size="small">{{ row.status }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="180" fixed="right">
                  <template #default="{ row }">
                    <el-button type="info" link @click="handleDetail(row.id)">查看</el-button>
                    <el-button type="primary" link>打印</el-button>
                    <el-button type="success" link>导出</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<style lang="scss" scoped>
.asset-lifecycle {
  padding: 20px;
  
  .lifecycle-tabs {
    background: #fff;
    padding: 20px;
    border-radius: 4px;
  }
  
  .stat-cards {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .card-content {
      text-align: center;
      
      .number {
        font-size: 24px;
        font-weight: bold;
        color: #303133;
        margin-bottom: 8px;
    }
    
      .label {
        font-size: 14px;
        color: #909399;
    }
  }
  
    .module-card {
    cursor: pointer;
      transition: all .3s;
    
    &:hover {
      transform: translateY(-5px);
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      }
    }
    }
    
  .chart-row {
    margin-bottom: 20px;
  }
  
  .todo-list {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  .process-content {
    .process-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      h3 {
        margin: 0;
      font-size: 18px;
      font-weight: bold;
    }
    
      .header-buttons {
    display: flex;
        gap: 10px;
      }
    }
  }
}
</style> 