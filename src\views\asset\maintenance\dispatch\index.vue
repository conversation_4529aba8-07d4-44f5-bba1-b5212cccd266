<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { Search, Refresh, Edit, Check } from '@element-plus/icons-vue'
import request from '@/config/axios'

// 类型定义
interface RepairOrder {
  id: string
  requestId: string
  orderNo: string
  assetId: string
  assetCode: string
  assetName: string
  faultType: string
  faultDesc: string
  reportBy: string
  reportTime: string
  status: string
  priority: string
  assignee: string | null
  assignTime: string | null
  expectedCompleteTime: string
}

interface Employee {
  id: string
  name: string
  title: string
  department: string
  phone: string
  workload: number
  avatar?: string
}

// 状态变量
const loading = ref(false)
const drawerVisible = ref(false)

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  status: '',
  priority: '',
  keyword: '',
  dateRange: [] as string[]
})

// 表格数据
const tableData = ref<RepairOrder[]>([])
const total = ref(0)

// 派单表单
const formRef = ref<FormInstance>()
const form = reactive({
  id: '',
  requestId: '',
  assetId: '',
  assetCode: '',
  assetName: '',
  faultType: '',
  faultDesc: '',
  reportBy: '',
  priority: '',
  assigneeId: '',
  expectedCompleteTime: ''
})

// 表单验证规则
const rules = reactive<FormRules>({
  assigneeId: [
    { required: true, message: '请选择维修人员', trigger: 'change' }
  ],
  expectedCompleteTime: [
    { required: true, message: '请选择预计完成时间', trigger: 'change' }
  ]
})

// 维修人员列表对话框
const assignDialogVisible = ref(false)
const technicianList = ref<Employee[]>([])

// 状态映射
const statusMap = {
  pending: { text: '待派单', type: 'info' },
  assigned: { text: '已派单', type: 'warning' },
  processing: { text: '处理中', type: 'warning' },
  completed: { text: '已完成', type: 'success' },
  rejected: { text: '已拒绝', type: 'danger' }
}

// 优先级映射
const priorityMap = {
  high: { text: '高', type: 'danger' },
  normal: { text: '中', type: 'warning' },
  low: { text: '低', type: 'info' }
}

// 获取维修工单列表
const getList = async () => {
  loading.value = true
  try {
    // TODO: 调用API
    // 模拟API响应
    await new Promise(resolve => setTimeout(resolve, 300))
    
    // 生成模拟数据
    const mockData: RepairOrder[] = Array.from({ length: 15 }, (_, index) => ({
      id: `${index + 1}`,
      requestId: `REQ-${1000 + index}`,
      orderNo: `WO-${String(1000 + index).padStart(6, '0')}`,
      assetId: `${2000 + index}`,
      assetCode: `ZC${String(2000 + index).padStart(6, '0')}`,
      assetName: `测试设备${index + 1}`,
      faultType: ['hardware', 'software', 'network', 'power', 'other'][index % 5],
      faultDesc: `设备出现故障，需要维修处理 #${index + 1}`,
      reportBy: '张三',
      reportTime: new Date(Date.now() - Math.random() * 10 * 24 * 60 * 60 * 1000).toLocaleString(),
      status: ['pending', 'assigned', 'processing', 'completed', 'rejected'][index % 5],
      priority: ['high', 'normal', 'low'][index % 3],
      assignee: ['pending', 'rejected'].includes(['pending', 'assigned', 'processing', 'completed', 'rejected'][index % 5]) ? null : `维修员${index % 3 + 1}`,
      assignTime: ['pending', 'rejected'].includes(['pending', 'assigned', 'processing', 'completed', 'rejected'][index % 5]) ? null : new Date(Date.now() - Math.random() * 5 * 24 * 60 * 60 * 1000).toLocaleString(),
      expectedCompleteTime: new Date(Date.now() + Math.random() * 10 * 24 * 60 * 60 * 1000).toLocaleDateString()
    }))

    // 分页处理
    const start = (queryParams.pageNum - 1) * queryParams.pageSize
    const end = Math.min(start + queryParams.pageSize, mockData.length)
    tableData.value = mockData.slice(start, end)
    total.value = mockData.length
  } catch (error) {
    console.error('获取维修工单列表失败:', error)
    ElMessage.error('获取维修工单列表失败')
  } finally {
    loading.value = false
  }
}

// 获取可派单人员列表
const getTechnicianList = async () => {
  try {
    // 模拟API响应
    await new Promise(resolve => setTimeout(resolve, 300))
    
    // 生成模拟数据
    technicianList.value = [
      {
        id: '1',
        name: '李工',
        title: '高级维修工程师',
        department: '技术维修部',
        phone: '13900001111',
        workload: 3,
        avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
      },
      {
        id: '2',
        name: '王工',
        title: '维修工程师',
        department: '技术维修部',
        phone: '13900002222',
        workload: 1,
        avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
      },
      {
        id: '3',
        name: '张工',
        title: '初级维修工程师',
        department: '技术维修部',
        phone: '13900003333',
        workload: 5,
        avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
      }
    ]
  } catch (error) {
    console.error('获取维修人员列表失败:', error)
    ElMessage.error('获取维修人员列表失败')
  }
}

// 搜索
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置搜索
const handleReset = () => {
  queryParams.keyword = ''
  queryParams.status = ''
  queryParams.priority = ''
  queryParams.dateRange = []
  handleQuery()
}

// 打开派单抽屉
const handleAssign = (row: RepairOrder) => {
  form.id = row.id
  form.requestId = row.requestId
  form.assetId = row.assetId
  form.assetCode = row.assetCode
  form.assetName = row.assetName
  form.faultType = row.faultType
  form.faultDesc = row.faultDesc
  form.reportBy = row.reportBy
  form.priority = row.priority
  form.assigneeId = ''
  form.expectedCompleteTime = row.expectedCompleteTime
  
  drawerVisible.value = true
  getTechnicianList()
}

// 打开选择维修人员对话框
const openAssignDialog = () => {
  getTechnicianList()
  assignDialogVisible.value = true
}

// 提交派单表单
const submitForm = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        // TODO: 调用API保存派单信息
        await new Promise(resolve => setTimeout(resolve, 500))
        ElMessage.success('派单成功')
        drawerVisible.value = false
        getList()
      } catch (error) {
        console.error('派单失败:', error)
        ElMessage.error('派单失败')
      }
    }
  })
}

// 页面加载时获取数据
onMounted(() => {
  getList()
})
</script>

<template>
  <div class="dispatch-container">
    <!-- 搜索表单 -->
    <el-card class="search-card">
      <el-form :model="queryParams" ref="queryFormRef" :inline="true">
        <el-form-item label="关键词" prop="keyword">
          <el-input
            v-model="queryParams.keyword"
            placeholder="工单号/资产编号/名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择" clearable style="width: 200px">
            <el-option label="待派单" value="pending" />
            <el-option label="已派单" value="assigned" />
            <el-option label="处理中" value="processing" />
            <el-option label="已完成" value="completed" />
            <el-option label="已拒绝" value="rejected" />
          </el-select>
        </el-form-item>
        <el-form-item label="优先级" prop="priority">
          <el-select v-model="queryParams.priority" placeholder="请选择" clearable style="width: 200px">
            <el-option label="高" value="high" />
            <el-option label="中" value="normal" />
            <el-option label="低" value="low" />
          </el-select>
        </el-form-item>
        <el-form-item label="申请日期" prop="dateRange">
          <el-date-picker
            v-model="queryParams.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 表格 -->
    <el-card class="table-card">
      <template #header>
        <span>维修工单列表</span>
      </template>

      <el-table :data="tableData" v-loading="loading" border>
        <el-table-column prop="orderNo" label="工单号" width="120" />
        <el-table-column prop="assetCode" label="资产编号" width="120" />
        <el-table-column prop="assetName" label="资产名称" width="120" />
        <el-table-column prop="faultDesc" label="故障描述" min-width="180" show-overflow-tooltip />
        <el-table-column prop="reportTime" label="报修时间" width="150" />
        <el-table-column prop="reportBy" label="报修人" width="100" />
        <el-table-column label="优先级" width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="priorityMap[row.priority].type">
              {{ priorityMap[row.priority].text }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="statusMap[row.status].type">
              {{ statusMap[row.status].text }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="assignee" label="维修人员" width="100" />
        <el-table-column prop="expectedCompleteTime" label="预期完成" width="100" />
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button 
              v-if="row.status === 'pending'" 
              type="primary" 
              link 
              icon="Edit" 
              @click="handleAssign(row)"
            >派单</el-button>
            <el-button 
              v-else-if="row.status === 'assigned'" 
              type="success" 
              link 
              icon="Check"
            >跟进</el-button>
            <el-button 
              type="info" 
              link 
              icon="View"
            >详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页器 -->
      <el-pagination
        v-model:current-page="queryParams.pageNum"
        v-model:page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        class="pagination"
      />
    </el-card>

    <!-- 派单抽屉 -->
    <el-drawer
      v-model="drawerVisible"
      title="维修派单"
      direction="rtl"
      size="50%"
      destroy-on-close
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        class="dispatch-form"
      >
        <el-descriptions title="工单信息" :column="1" border>
          <el-descriptions-item label="资产编号">{{ form.assetCode }}</el-descriptions-item>
          <el-descriptions-item label="资产名称">{{ form.assetName }}</el-descriptions-item>
          <el-descriptions-item label="故障类型">
            {{ {
              hardware: '硬件故障',
              software: '软件故障',
              network: '网络故障',
              power: '电力故障',
              other: '其他故障'
            }[form.faultType] }}
          </el-descriptions-item>
          <el-descriptions-item label="故障描述">{{ form.faultDesc }}</el-descriptions-item>
          <el-descriptions-item label="报修人">{{ form.reportBy }}</el-descriptions-item>
          <el-descriptions-item label="优先级">
            <el-tag :type="priorityMap[form.priority].type">
              {{ priorityMap[form.priority].text }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>

        <div class="form-divider"></div>

        <h4>派单信息</h4>

        <el-form-item label="维修人员" prop="assigneeId">
          <div class="assignee-selector">
            <div v-if="form.assigneeId" class="selected-assignee">
              <div>{{ technicianList.find(t => t.id === form.assigneeId)?.name }}</div>
              <div>{{ technicianList.find(t => t.id === form.assigneeId)?.title }}</div>
            </div>
            <el-button type="primary" @click="openAssignDialog">选择维修人员</el-button>
          </div>
        </el-form-item>

        <el-form-item label="预计完成时间" prop="expectedCompleteTime">
          <el-date-picker
            v-model="form.expectedCompleteTime"
            type="date"
            placeholder="请选择预计完成时间"
          />
        </el-form-item>

        <el-form-item label="维修说明">
          <el-input
            type="textarea"
            :rows="3"
            placeholder="请输入维修说明或特别注意事项"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitForm">确认派单</el-button>
          <el-button @click="drawerVisible = false">取消</el-button>
        </el-form-item>
      </el-form>
    </el-drawer>

    <!-- 选择维修人员对话框 -->
    <el-dialog
      v-model="assignDialogVisible"
      title="选择维修人员"
      width="800px"
    >
      <el-table :data="technicianList" border style="width: 100%">
        <el-table-column label="头像" width="60">
          <template #default="{ row }">
            <el-avatar :size="40" :src="row.avatar" />
          </template>
        </el-table-column>
        <el-table-column prop="name" label="姓名" width="100" />
        <el-table-column prop="title" label="职位" width="150" />
        <el-table-column prop="department" label="部门" width="120" />
        <el-table-column prop="phone" label="联系电话" width="120" />
        <el-table-column label="当前工作量" width="120">
          <template #default="{ row }">
            <el-progress
              :percentage="Math.min(row.workload / 10 * 100, 100)"
              :status="row.workload > 8 ? 'exception' : row.workload > 5 ? 'warning' : 'success'"
            />
            <span>{{ row.workload }} 单</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="80">
          <template #default="{ row }">
            <el-button 
              type="primary" 
              link 
              @click="form.assigneeId = row.id; assignDialogVisible = false"
            >选择</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.dispatch-container {
  padding: 20px;
  
  .search-card {
    margin-bottom: 20px;
  }
  
  .table-card {
    .pagination {
      margin-top: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
  
  .dispatch-form {
    padding: 20px;
    
    .form-divider {
      height: 1px;
      background-color: #ebeef5;
      margin: 20px 0;
    }
    
    h4 {
      margin-bottom: 20px;
      font-weight: 500;
    }
    
    .assignee-selector {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .selected-assignee {
        flex: 1;
      }
    }
  }
}
</style> 