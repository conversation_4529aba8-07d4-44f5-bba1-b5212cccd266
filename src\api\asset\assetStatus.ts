import request from '@/config/axios'

// 查询资产状态列表
export const getAssetStatusList = (params: any) => {
  return request.get({ url: '/api/asset/status/list', params })
}

// 新增资产状态
export const addAssetStatus = (data: any) => {
  return request.post({ url: '/api/asset/status', data })
}

// 修改资产状态
export const updateAssetStatus = (data: any) => {
  return request.put({ url: '/api/asset/status', data })
}

// 删除资产状态
export const delAssetStatus = (id: number) => {
  return request.delete({ url: `/api/asset/status/${id}` })
}

// 批量删除资产状态
export const delAssetStatusBatch = (ids: number[]) => {
  return request.delete({ url: '/api/asset/status/batch', data: ids })
} 