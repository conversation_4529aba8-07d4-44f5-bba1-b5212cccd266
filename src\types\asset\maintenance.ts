// 资产维修与维保管理相关类型定义

// 维修状态枚举
export enum MaintenanceStatus {
  PENDING = 'pending',         // 待审批
  APPROVED = 'approved',       // 已批准（待派工）
  ASSIGNED = 'assigned',       // 已派工（待维修）
  PROCESSING = 'processing',   // 维修中
  COMPLETED = 'completed',     // 维修完成（待验收）
  ACCEPTED = 'accepted',       // 验收通过
  REJECTED = 'rejected',       // 验收不通过
  CANCELED = 'canceled'        // 已取消
}

// 维修优先级枚举
export enum MaintenancePriority {
  LOW = 'low',           // 低
  MEDIUM = 'medium',     // 中
  HIGH = 'high',         // 高
  URGENT = 'urgent'      // 紧急
}

// 维修类型枚举
export enum MaintenanceType {
  BREAKDOWN = 'breakdown',     // 故障维修
  PREVENTIVE = 'preventive',   // 预防性维修
  ROUTINE = 'routine',         // 日常维护
  CALIBRATION = 'calibration', // 校准
  OVERHAUL = 'overhaul'        // 大修
}

// 维修申请
export interface MaintenanceRequest {
  id: string;                            // 维修单ID
  requestNo: string;                     // 维修单号
  assetId: string;                       // 资产ID
  assetCode: string;                     // 资产编码
  assetName: string;                     // 资产名称
  assetType: string;                     // 资产类型
  department: string;                    // 部门
  location: string;                      // 位置
  faultDescription: string;              // 故障描述
  faultTime: string;                     // 故障发生时间
  maintenanceType: MaintenanceType;      // 维修类型
  priority: MaintenancePriority;         // 优先级
  attachments?: string[];                // 附件（图片、文档等）
  requestUser: string;                   // 申请人
  requestTime: string;                   // 申请时间
  status: MaintenanceStatus;             // 维修状态
  statusName?: string;                   // 状态名称
  reviewUser?: string;                   // 审批人
  reviewTime?: string;                   // 审批时间
  reviewComments?: string;               // 审批意见
  assignedTo?: string;                   // 指派给谁
  assignedTime?: string;                 // 指派时间
  plannedStartTime?: string;             // 计划开始时间
  plannedEndTime?: string;               // 计划结束时间
  actualStartTime?: string;              // 实际开始时间
  actualEndTime?: string;                // 实际结束时间
  maintenanceResult?: string;            // 维修结果
  acceptanceUser?: string;               // 验收人
  acceptanceTime?: string;               // 验收时间
  acceptanceComments?: string;           // 验收意见
  acceptanceResult?: boolean;            // 验收结果
  totalCost?: number;                    // 总费用
}

// 维修工单详情
export interface MaintenanceOrderDetail extends MaintenanceRequest {
  maintenanceCosts: MaintenanceCost[];       // 维修费用明细
  maintenanceParts: MaintenancePart[];       // 维修配件使用明细
  maintenanceRecords: MaintenanceRecord[];   // 维修记录
  statusLogs: MaintenanceStatusLog[];        // 状态变更日志
}

// 维修记录
export interface MaintenanceRecord {
  id: string;                     // 记录ID
  maintenanceId: string;          // 维修单ID
  operateUser: string;            // 操作人
  operateTime: string;            // 操作时间
  content: string;                // 维修内容
  attachments?: string[];         // 附件
}

// 维修状态日志
export interface MaintenanceStatusLog {
  id: string;                     // 日志ID
  maintenanceId: string;          // 维修单ID
  oldStatus?: MaintenanceStatus;  // 旧状态
  newStatus: MaintenanceStatus;   // 新状态
  operateUser: string;            // 操作人
  operateTime: string;            // 操作时间
  comments?: string;              // 备注
}

// 维修费用
export interface MaintenanceCost {
  id: string;                     // 费用ID
  maintenanceId: string;          // 维修单ID
  costType: string;               // 费用类型
  costName: string;               // 费用名称
  amount: number;                 // 金额
  createUser: string;             // 创建人
  createTime: string;             // 创建时间
  remarks?: string;               // 备注
}

// 维修配件
export interface MaintenancePart {
  id: string;                     // 配件ID
  maintenanceId: string;          // 维修单ID
  partName: string;               // 配件名称
  partCode?: string;              // 配件编码
  specification?: string;         // 规格型号
  quantity: number;               // 数量
  unitPrice: number;              // 单价
  totalPrice: number;             // 总价
  supplier?: string;              // 供应商
  remarks?: string;               // 备注
}

// 维修统计数据
export interface MaintenanceStatistics {
  total: number;                  // 总维修数
  pending: number;                // 待处理数
  processing: number;             // 处理中数
  completed: number;              // 已完成数
  acceptanceRate: number;         // 验收通过率
  averageResponseTime: number;    // 平均响应时间（小时）
  averageCompletionTime: number;  // 平均完成时间（小时）
  totalCost: number;              // 总费用
  byType: {                       // 按类型统计
    [key in MaintenanceType]: number;
  };
  byDepartment: {                 // 按部门统计
    name: string;
    count: number;
    cost: number;
  }[];
  byMonth: {                      // 月度统计
    month: string;
    count: number;
    cost: number;
  }[];
}

// 维保计划相关
export interface MaintenancePlan {
  id: string;                     // 计划ID
  planNo: string;                 // 计划编号
  planName: string;               // 计划名称
  assetType?: string;             // 资产类型
  department?: string;            // 部门
  cycleType: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly' | 'custom'; // 周期类型
  cycleValue: number;             // 周期值
  cycleUnit?: string;             // 周期单位（自定义周期时使用）
  maintenanceType: MaintenanceType; // 维护类型
  description: string;            // 描述
  tasks: MaintenancePlanTask[];   // 关联的任务
  startDate?: string;             // 开始日期
  endDate?: string;               // 结束日期
  enableNotification: boolean;    // 是否启用通知
  notificationDays: number;       // 提前通知天数
  status: 'active' | 'inactive';  // 状态
  createUser: string;             // 创建人
  createTime: string;             // 创建时间
  updateUser?: string;            // 更新人
  updateTime?: string;            // 更新时间
}

// 维保计划任务
export interface MaintenancePlanTask {
  id: string;                     // 任务ID
  planId: string;                 // 计划ID
  assetId: string;                // 资产ID
  assetCode: string;              // 资产编码
  assetName: string;              // 资产名称
  department: string;             // 部门
  responsibleUser?: string;       // 负责人
  scheduledTime: string;          // 计划执行时间
  actualTime?: string;            // 实际执行时间
  status: 'pending' | 'completed' | 'overdue' | 'canceled'; // 状态
  completionUser?: string;        // 完成人
  completionTime?: string;        // 完成时间
  remarks?: string;               // 备注
}

// 查询参数
export interface MaintenanceQueryParams {
  pageNum: number;
  pageSize: number;
  keyword?: string;
  assetId?: string;
  assetType?: string;
  departmentId?: string;
  status?: MaintenanceStatus | string;
  maintenanceType?: MaintenanceType | string;
  priority?: MaintenancePriority | string;
  timeRange?: [string, string];   // 时间范围
  requestUser?: string;           // 申请人
} 