<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { createMaintenance } from '@/api/asset/maintenance'
import { MaintenancePriority, MaintenanceType } from '@/types/asset/maintenance'

// 路由
const router = useRouter()

// 页面状态
const loading = ref(false)
const uploadLoading = ref(false)
const uploadFileList = ref([])

// 表单
const form = reactive({
  assetId: '',
  assetCode: '',
  assetName: '',
  assetType: '',
  department: '',
  location: '',
  faultDescription: '',
  faultTime: '',
  maintenanceType: '',
  priority: '',
  attachments: [] as string[]
})

// 表单规则
const rules = {
  assetId: [{ required: true, message: '请选择资产', trigger: 'change' }],
  faultDescription: [{ required: true, message: '请输入故障描述', trigger: 'blur' }],
  faultTime: [{ required: true, message: '请选择故障发生时间', trigger: 'change' }],
  maintenanceType: [{ required: true, message: '请选择维修类型', trigger: 'change' }],
  priority: [{ required: true, message: '请选择优先级', trigger: 'change' }]
}

// 资产选择
const showAssetSelect = ref(false)
const assetList = ref<Array<{
  id: string;
  code: string;
  name: string;
  type: string;
  department: string;
  location: string;
}>>([])
const assetQuery = ref('')
const assetLoading = ref(false)

// 搜索资产
const searchAssets = () => {
  assetLoading.value = true
  // 模拟数据
  setTimeout(() => {
    const mockAssets = [
      { id: 'asset-1', code: 'ZC202301001', name: '水泵电机', type: '生产设备', department: '生产部', location: '一号水厂' },
      { id: 'asset-2', code: 'ZC202301002', name: '空压机', type: '生产设备', department: '生产部', location: '二号水厂' },
      { id: 'asset-3', code: 'ZC202301003', name: '变频器', type: '电子设备', department: '生产部', location: '三号水厂' },
      { id: 'asset-4', code: 'ZC202301004', name: '检测仪器', type: '电子设备', department: '质检部', location: '总部大楼' },
      { id: 'asset-5', code: 'ZC202301005', name: '办公电脑', type: '办公设备', department: '行政部', location: '总部大楼' }
    ]
    
    if (assetQuery.value) {
      assetList.value = mockAssets.filter(asset => 
        asset.code.includes(assetQuery.value) || 
        asset.name.includes(assetQuery.value)
      )
    } else {
      assetList.value = mockAssets
    }
    
    assetLoading.value = false
  }, 500)
}

// 选择资产
const selectAsset = (asset) => {
  form.assetId = asset.id
  form.assetCode = asset.code
  form.assetName = asset.name
  form.assetType = asset.type
  form.department = asset.department
  form.location = asset.location
  showAssetSelect.value = false
}

// 打开资产选择器
const openAssetSelect = () => {
  assetQuery.value = ''
  showAssetSelect.value = true
  searchAssets()
}

// 处理上传
const handleUpload = (options): any => {
  const { file, onSuccess } = options
  uploadLoading.value = true
  
  // 模拟上传成功
  setTimeout(() => {
    form.attachments.push('file-path.jpg')
    uploadLoading.value = false
    ElMessage.success('上传成功')
    onSuccess && onSuccess('上传成功')
  }, 1000)
  
  return {} as XMLHttpRequest
}

// 处理上传错误
const handleUploadError = () => {
  uploadLoading.value = false
  ElMessage.error('上传失败')
}

// 处理文件移除
const handleRemove = (file, fileList) => {
  const index = form.attachments.indexOf(file.url)
  if (index > -1) {
    form.attachments.splice(index, 1)
  }
}

// 提交表单
const formRef = ref()
const submitForm = () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      
      // 模拟提交
      setTimeout(() => {
        ElMessage.success('维修申请提交成功')
        loading.value = false
        router.push({ name: 'AssetMaintenance' })
      }, 1000)
      
      // 实际API调用
      // try {
      //   await createMaintenance(form)
      //   ElMessage.success('维修申请提交成功')
      //   router.push({ name: 'AssetMaintenance' })
      // } catch (error) {
      //   console.error('提交失败', error)
      //   ElMessage.error('提交失败')
      // } finally {
      //   loading.value = false
      // }
    }
  })
}

// 取消并返回
const cancelAndReturn = () => {
  router.back()
}
</script>

<template>
  <div class="maintenance-add-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>新增维修申请</h3>
        </div>
      </template>
      
      <el-form 
        ref="formRef"
        :model="form" 
        :rules="rules"
        label-width="120px"
        label-position="right"
      >
        <!-- 资产信息 -->
        <el-divider content-position="left">资产信息</el-divider>
        
        <el-row :gutter="20">
          <el-col :span="18">
            <el-form-item label="资产" prop="assetId">
              <div class="asset-select">
                <el-input 
                  v-model="form.assetName" 
                  placeholder="点击选择资产" 
                  readonly 
                  @click="openAssetSelect"
                >
                  <template #append>
                    <el-button @click="openAssetSelect">
                      <el-icon><search /></el-icon>
                    </el-button>
                  </template>
                </el-input>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="资产编码">
              <el-input v-model="form.assetCode" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="资产类型">
              <el-input v-model="form.assetType" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所属部门">
              <el-input v-model="form.department" disabled />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="位置">
              <el-input v-model="form.location" disabled />
            </el-form-item>
          </el-col>
        </el-row>
        
        <!-- 维修信息 -->
        <el-divider content-position="left">维修信息</el-divider>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="维修类型" prop="maintenanceType">
              <el-select v-model="form.maintenanceType" placeholder="请选择维修类型" style="width: 100%">
                <el-option label="故障维修" value="故障维修" />
                <el-option label="预防性维修" value="预防性维修" />
                <el-option label="日常维护" value="日常维护" />
                <el-option label="校准" value="校准" />
                <el-option label="大修" value="大修" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="优先级" prop="priority">
              <el-select v-model="form.priority" placeholder="请选择优先级" style="width: 100%">
                <el-option label="低" value="低" />
                <el-option label="中" value="中" />
                <el-option label="高" value="高" />
                <el-option label="紧急" value="紧急" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障发生时间" prop="faultTime">
              <el-date-picker 
                v-model="form.faultTime" 
                type="datetime" 
                placeholder="选择时间" 
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="故障描述" prop="faultDescription">
              <el-input 
                v-model="form.faultDescription" 
                type="textarea" 
                :rows="4" 
                placeholder="请详细描述故障现象、可能原因等信息"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="附件">
              <el-upload
                action="#"
                :http-request="handleUpload"
                :on-error="handleUploadError"
                :on-remove="handleRemove"
                :file-list="uploadFileList"
                multiple
                :limit="5"
              >
                <el-button type="primary" :loading="uploadLoading">上传附件</el-button>
                <template #tip>
                  <div class="el-upload__tip">
                    支持jpg、png、pdf等格式文件，单个文件不超过10MB
                  </div>
                </template>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
        
        <!-- 操作按钮 -->
        <el-form-item>
          <el-button type="primary" @click="submitForm" :loading="loading">提交</el-button>
          <el-button @click="cancelAndReturn">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 资产选择对话框 -->
    <el-dialog
      v-model="showAssetSelect"
      title="选择资产"
      width="800px"
      destroy-on-close
    >
      <div class="asset-search">
        <el-input 
          v-model="assetQuery" 
          placeholder="资产编号/名称" 
          style="width: 300px"
          clearable
        >
          <template #append>
            <el-button @click="searchAssets">
              <el-icon><search /></el-icon>
            </el-button>
          </template>
        </el-input>
      </div>
      
      <el-table
        v-loading="assetLoading"
        :data="assetList"
        style="width: 100%; margin-top: 20px"
        border
        highlight-current-row
        @row-click="selectAsset"
      >
        <el-table-column prop="code" label="资产编码" width="120" />
        <el-table-column prop="name" label="资产名称" min-width="120" />
        <el-table-column prop="type" label="资产类型" width="120">
          <template #default="{ row }">
            <el-tag size="small">{{ row.type }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="department" label="所属部门" width="120" />
        <el-table-column prop="location" label="位置" width="120" />
        <el-table-column label="操作" width="80" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click.stop="selectAsset(row)">选择</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.maintenance-add-container {
  padding: 20px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
      margin: 0;
      font-size: 18px;
    }
  }
  
  .asset-select {
    width: 100%;
    
    .el-input-group__append {
      cursor: pointer;
    }
  }
  
  .asset-search {
    margin-bottom: 20px;
  }
}
</style> 