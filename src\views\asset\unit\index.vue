<template>
  <div class="app-container">
    <div class="flex h-full">
      <!-- 左侧：单位树形结构 -->
      <div class="unit-tree-container bg-white p-20px mr-20px" style="width: 280px;">
        <div class="mb-10px flex justify-between items-center">
          <h4 class="m-0 text-16px">单位树</h4>
          <el-button 
            type="primary" 
            icon="Plus" 
            circle 
            plain 
            size="small" 
            @click="handleAddTopUnit"
          />
        </div>
        <el-input
          v-model="filterText"
          placeholder="输入关键字过滤"
          clearable
          class="mb-10px"
        />
        <div class="tree-wrapper overflow-auto" style="height: calc(100% - 80px);">
          <el-tree
            ref="unitTreeRef"
            :data="unitTree"
            :props="defaultProps"
            :filter-node-method="filterNode"
            :highlight-current="true"
            :expand-on-click-node="false"
            node-key="id"
            default-expand-all
            @node-click="handleNodeClick"
          >
            <template #default="{ node, data }">
              <div class="custom-tree-node flex items-center justify-between w-full">
                <span class="text-ellipsis overflow-hidden">{{ node.label }}</span>
                <span class="operation-buttons">
                  <el-button
                    type="primary"
                    icon="Plus"
                    circle
                    plain
                    size="small"
                    class="ml-5px"
                    @click.stop="handleAddChildUnit(data)"
                  />
                  <el-button
                    type="primary"
                    icon="Edit"
                    circle
                    plain
                    size="small"
                    class="ml-5px"
                    @click.stop="handleEdit(data)"
                  />
                  <el-button
                    type="danger"
                    icon="Delete"
                    circle
                    plain
                    size="small"
                    class="ml-5px"
                    @click.stop="handleDelete(data)"
                  />
                </span>
              </div>
            </template>
          </el-tree>
        </div>
      </div>

      <!-- 右侧：单位详情 -->
      <div class="unit-detail-container flex-1">
        <!-- 无选择单位时显示的占位内容 -->
        <el-empty v-if="!currentUnit.id" description="请选择左侧单位进行查看" />
        
        <!-- 选择单位后显示详情 -->
        <el-card v-else class="box-card">
          <template #header>
            <div class="flex justify-between items-center">
              <span class="text-lg font-bold">{{ currentUnit.name }} 详情</span>
              <el-button 
                type="primary" 
                plain 
                @click="handleEdit(currentUnit)"
              >编辑</el-button>
            </div>
          </template>
          <div class="unit-info">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="单位名称">{{ currentUnit.name }}</el-descriptions-item>
              <el-descriptions-item label="单位编码">{{ currentUnit.code }}</el-descriptions-item>
              <el-descriptions-item label="单位层级">{{ getLevelText(currentUnit.level) }}</el-descriptions-item>
              <el-descriptions-item label="排序">{{ currentUnit.sort }}</el-descriptions-item>
              <el-descriptions-item label="创建人">{{ currentUnit.createBy }}</el-descriptions-item>
              <el-descriptions-item label="创建时间">{{ currentUnit.createTime }}</el-descriptions-item>
              <el-descriptions-item label="描述" :span="2">{{ currentUnit.description }}</el-descriptions-item>
            </el-descriptions>
          </div>
          
          <!-- 关联的资产信息（统计视图） -->
          <div class="asset-statistics mt-20px">
            <h4 class="text-16px">资产统计</h4>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-card shadow="hover" class="statistics-card">
                  <div class="flex items-center">
                    <el-icon class="text-primary text-24px mr-10px">
                      <Tools />
                    </el-icon>
                    <div>
                      <div class="text-24px font-bold">{{ assetCount.total }}</div>
                      <div class="text-gray-500">资产总数</div>
                    </div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="8">
                <el-card shadow="hover" class="statistics-card">
                  <div class="flex items-center">
                    <el-icon class="text-success text-24px mr-10px">
                      <Check />
                    </el-icon>
                    <div>
                      <div class="text-24px font-bold">{{ assetCount.inUse }}</div>
                      <div class="text-gray-500">在用资产</div>
                    </div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="8">
                <el-card shadow="hover" class="statistics-card">
                  <div class="flex items-center">
                    <el-icon class="text-warning text-24px mr-10px">
                      <Warning />
                    </el-icon>
                    <div>
                      <div class="text-24px font-bold">{{ assetCount.maintenance }}</div>
                      <div class="text-gray-500">维修资产</div>
                    </div>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 新增/编辑单位对话框 -->
    <el-dialog
      :title="formTitle"
      v-model="dialogVisible"
      width="500px"
      append-to-body
    >
      <el-form
        ref="unitFormRef"
        :model="unitForm"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="上级单位" prop="parentId" v-if="!isTopLevel">
          <el-cascader
            v-model="unitForm.parentId"
            :options="unitTreeOptions"
            :props="{ checkStrictly: true, label: 'name', value: 'id', emitPath: false }"
            placeholder="请选择上级单位"
            clearable
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="单位名称" prop="name">
          <el-input v-model="unitForm.name" placeholder="请输入单位名称" />
        </el-form-item>
        <el-form-item label="单位编码" prop="code">
          <el-input v-model="unitForm.code" placeholder="请输入单位编码" />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="unitForm.sort" :min="0" controls-position="right" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="unitForm.description"
            type="textarea"
            placeholder="请输入描述内容"
            rows="3"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElTree } from 'element-plus'
import { Check, Warning, Tools } from '@element-plus/icons-vue'
// 引入mock数据和方法
import { 
  Unit, 
  mockUnitData, 
  mockGetUnitTree, 
  mockAddUnit, 
  mockUpdateUnit, 
  mockDeleteUnit, 
  mockGetUnitDetail 
} from './mock'

// 树组件引用
const unitTreeRef = ref<InstanceType<typeof ElTree>>()
// 过滤文本
const filterText = ref('')
// 单位树数据
const unitTree = ref<Unit[]>([])
// 当前选中的单位
const currentUnit = ref<Partial<Unit>>({})
// 对话框显示状态
const dialogVisible = ref(false)
// 表单标题
const formTitle = ref('')
// 是否为顶级单位
const isTopLevel = ref(false)

// 默认树属性配置
const defaultProps = {
  children: 'children',
  label: 'name',
  isLeaf: (data: Unit) => !data.hasChildren
}

// 单位表单
const unitForm = reactive({
  id: undefined as number | undefined,
  parentId: undefined as number | null | undefined,
  name: '',
  code: '',
  level: 1,
  sort: 0,
  description: ''
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入单位名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入单位编码', trigger: 'blur' },
    { min: 2, max: 30, message: '长度在 2 到 30 个字符', trigger: 'blur' }
  ]
}

// 计算属性：单位树级联选择器选项
const unitTreeOptions = computed(() => {
  // 将树形结构转化为级联选择器需要的格式
  return unitTree.value.map(convertTreeNode)
})

// 工具函数：转换树节点为级联选择器格式
function convertTreeNode(node: Unit) {
  const { id, name, children } = node
  const result: any = { id, name }
  
  if (children && children.length > 0) {
    result.children = children.map(convertTreeNode)
  }
  
  return result
}

// 模拟资产统计数据
const assetCount = ref({
  total: 0,
  inUse: 0,
  maintenance: 0,
  idle: 0
})

// 工具函数：根据单位层级获取显示文本
function getLevelText(level: number) {
  const levelMap: Record<number, string> = {
    1: '集团级',
    2: '分公司/厂级',
    3: '部门级',
    4: '班组级'
  }
  return levelMap[level] || `${level}级`
}

// 树节点过滤方法
const filterNode = (value: string, data: Unit) => {
  if (!value) return true
  return data.name.includes(value) || data.code.includes(value)
}

// 节点点击事件处理
const handleNodeClick = async (data: Unit) => {
  currentUnit.value = data
  await updateAssetStatistics(data.id)
}

// 新增顶级单位
const handleAddTopUnit = () => {
  resetForm()
  isTopLevel.value = true
  formTitle.value = '新增顶级单位'
  unitForm.parentId = null
  unitForm.level = 1
  dialogVisible.value = true
}

// 新增子单位
const handleAddChildUnit = (data: Unit) => {
  resetForm()
  isTopLevel.value = false
  formTitle.value = '新增子单位'
  unitForm.parentId = data.id
  unitForm.level = data.level + 1
  dialogVisible.value = true
}

// 编辑单位
const handleEdit = (data: Unit) => {
  resetForm()
  isTopLevel.value = data.parentId === null
  formTitle.value = '编辑单位'
  
  unitForm.id = data.id
  unitForm.parentId = data.parentId
  unitForm.name = data.name
  unitForm.code = data.code
  unitForm.level = data.level
  unitForm.sort = data.sort
  unitForm.description = data.description
  
  dialogVisible.value = true
}

// 删除单位
const handleDelete = (data: Unit) => {
  ElMessageBox.confirm(
    `确定要删除单位"${data.name}"吗？如有下级单位将无法删除`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await mockDeleteUnit(data.id)
      ElMessage.success('删除成功')
      if (currentUnit.value.id === data.id) {
        currentUnit.value = {}
      }
      getUnitTree()
    } catch (error) {
      if (error instanceof Error) {
        ElMessage.error(error.message)
      } else {
        ElMessage.error('删除失败')
      }
    }
  }).catch(() => {})
}

// 表单重置
const resetForm = () => {
  unitForm.id = undefined
  unitForm.parentId = undefined
  unitForm.name = ''
  unitForm.code = ''
  unitForm.level = 1
  unitForm.sort = 0
  unitForm.description = ''
}

// 提交表单
const submitForm = async () => {
  const unitFormRef = ref()
  
  try {
    const result = unitForm.id 
      ? await mockUpdateUnit(unitForm)
      : await mockAddUnit(unitForm)
    
    ElMessage.success(`${unitForm.id ? '修改' : '新增'}成功`)
    dialogVisible.value = false
    getUnitTree()
    
    // 如果是编辑当前单位，更新当前单位信息
    if (unitForm.id && currentUnit.value.id === unitForm.id) {
      currentUnit.value = await mockGetUnitDetail(unitForm.id)
    }
  } catch (error) {
    if (error instanceof Error) {
      ElMessage.error(error.message)
    } else {
      ElMessage.error('操作失败')
    }
  }
}

// 获取单位树
const getUnitTree = async () => {
  try {
    unitTree.value = await mockGetUnitTree()
  } catch (error) {
    ElMessage.error('获取单位树失败')
  }
}

// 更新资产统计信息
const updateAssetStatistics = async (unitId: number) => {
  // 模拟获取资产统计数据
  // 这里应该是实际调用API来获取数据
  // 为了演示，这里生成随机数
  const total = Math.floor(Math.random() * 150) + 50
  const inUse = Math.floor(total * 0.8)
  const maintenance = Math.floor(total * 0.15)
  const idle = total - inUse - maintenance
  
  assetCount.value = {
    total,
    inUse,
    maintenance,
    idle
  }
}

// 监听过滤文本变化
watch(filterText, (val) => {
  unitTreeRef.value?.filter(val)
})

// 页面加载时获取单位树
onMounted(() => {
  getUnitTree()
})
</script>

<style lang="scss" scoped>
.app-container {
  height: calc(100vh - 170px);
  padding: 20px;
}

.unit-tree-container {
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.custom-tree-node {
  .operation-buttons {
    display: none;
  }
  
  &:hover .operation-buttons {
    display: inline-flex;
  }
}

.statistics-card {
  cursor: pointer;
  transition: all 0.3s;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }
}
</style> 