<template>
  <div class="app-container">
    <!-- 顶部统计卡片 -->
    <div class="stat-cards mb-20px">
      <el-row :gutter="20">
        <el-col :sm="24" :md="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon class="text-primary"><DataLine /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statistics.total }}</div>
                <div class="stat-label">资产总数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :sm="24" :md="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon class="text-success"><Check /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statistics.inUse }}</div>
                <div class="stat-label">在用资产</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :sm="24" :md="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon class="text-warning"><Tools /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statistics.maintenance }}</div>
                <div class="stat-label">维修中</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :sm="24" :md="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon class="text-danger"><Delete /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statistics.scrapped }}</div>
                <div class="stat-label">已报废</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索表单 -->
    <el-card class="search-card mb-20px">
      <el-form
        ref="queryFormRef"
        :model="queryParams"
        :inline="true"
        label-width="80px"
      >
        <el-form-item label="关键词" prop="keyword">
          <el-input
            v-model="queryParams.keyword"
            placeholder="资产编号/名称/规格型号"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="资产类型" prop="assetTypeId">
          <el-select
            v-model="queryParams.assetTypeId"
            placeholder="请选择资产类型"
            clearable
            style="width: 200px"
          >
            <el-option
              v-for="item in constants.assetTypes"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="statusId">
          <el-select
            v-model="queryParams.statusId"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="item in constants.statuses"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
              <el-tag
                :type="getStatusTagType(item.id)"
                disable-transitions
                size="small"
                style="margin-right: 5px"
              >
                {{ item.name }}
              </el-tag>
            </el-option>
          </el-select>
        </el-form-item>
        
        <template v-if="showAdvanced">
          <el-form-item label="所属单位" prop="unitId">
            <el-select
              v-model="queryParams.unitId"
              placeholder="请选择单位"
              clearable
              style="width: 200px"
              @change="handleUnitChange"
            >
              <el-option
                v-for="item in constants.units"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="所属部门" prop="departmentId">
            <el-select
              v-model="queryParams.departmentId"
              placeholder="请选择部门"
              clearable
              style="width: 200px"
              :disabled="!queryParams.unitId"
            >
              <el-option
                v-for="item in filteredDepartments"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="采购日期" prop="dateRange">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 240px"
            />
          </el-form-item>
          <el-form-item label="责任人" prop="responsiblePerson">
            <el-input
              v-model="queryParams.responsiblePerson"
              placeholder="请输入责任人"
              clearable
              style="width: 200px"
            />
          </el-form-item>
        </template>
        
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查 询</el-button>
          <el-button @click="resetQuery">重 置</el-button>
          <el-button
            type="text"
            @click="showAdvanced = !showAdvanced"
          >
            {{ showAdvanced ? '收起' : '展开' }}
            <el-icon>
              <component :is="showAdvanced ? 'ArrowUp' : 'ArrowDown'" />
            </el-icon>
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <div class="header-left">
            <el-button type="primary" @click="handleAdd">
              <el-icon><Plus /></el-icon> 新增资产
            </el-button>
            <el-button type="success" @click="handleImport">
              <el-icon><Upload /></el-icon> 导入
            </el-button>
            <el-button type="primary" @click="handleExport">
              <el-icon><Download /></el-icon> 导出
            </el-button>
            <el-button type="danger" :disabled="!selectedIds.length" @click="handleBatchDelete">
              <el-icon><Delete /></el-icon> 批量删除
            </el-button>
            <el-button type="primary" :disabled="!selectedIds.length" @click="handleBatchPrint">
              <el-icon><Printer /></el-icon> 批量打印
            </el-button>
          </div>
          <div class="header-right">
            <el-radio-group v-model="viewMode" size="small">
              <el-radio-button label="table">表格视图</el-radio-button>
              <el-radio-button label="card">卡片视图</el-radio-button>
            </el-radio-group>
          </div>
        </div>
      </template>

      <!-- 表格视图 -->
      <el-table
        v-if="viewMode === 'table'"
        ref="tableRef"
        v-loading="loading"
        :data="assetList"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" fixed="left" />
        <el-table-column type="index" width="50" label="序号" fixed="left" />
        <el-table-column prop="assetCode" label="资产编号" width="150" fixed="left" />
        <el-table-column prop="assetName" label="资产名称" width="150" show-overflow-tooltip />
        <el-table-column prop="assetType.name" label="资产类型" width="100" />
        <el-table-column prop="model" label="型号" width="120" show-overflow-tooltip />
        <el-table-column prop="specification" label="规格" width="120" show-overflow-tooltip />
        <el-table-column prop="unitName" label="所属单位" width="120" show-overflow-tooltip />
        <el-table-column prop="departmentName" label="所属部门" width="120" show-overflow-tooltip />
        <el-table-column prop="statusName" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.statusId)" size="small">
              {{ row.statusName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="purchaseDate" label="采购日期" width="100" />
        <el-table-column prop="purchasePrice" label="采购价格" width="120">
          <template #default="{ row }">
            {{ formatAmount(row.purchasePrice) }}
          </template>
        </el-table-column>
        <el-table-column prop="responsiblePerson" label="责任人" width="100" />
        <el-table-column prop="location" label="存放位置" width="150" show-overflow-tooltip />
        <el-table-column prop="attributesSummary" label="附加属性" width="180" show-overflow-tooltip />
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" text @click="handleView(row)">详情</el-button>
            <el-button type="primary" text @click="handleEdit(row)">编辑</el-button>
            <el-button type="danger" text @click="handleDelete(row)">删除</el-button>
            <el-dropdown trigger="click" @command="command => handleCommand(command, row)">
              <el-button type="primary" text>
                更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="qrcode">生成二维码</el-dropdown-item>
                  <el-dropdown-item command="print">打印标签</el-dropdown-item>
                  <el-dropdown-item command="transfer" divided>资产调拨</el-dropdown-item>
                  <el-dropdown-item command="repair">报修</el-dropdown-item>
                  <el-dropdown-item command="scrap">报废</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 卡片视图 -->
      <asset-card-view
        v-else
        :assets="assetList"
        @view="handleView"
        @edit="handleEdit"
        @delete="handleDelete"
        @qrcode="handleGenerateQrCode"
        @print="handlePrint"
        @transfer="handleTransfer"
        @repair="handleRepair"
        @scrap="handleScrap"
      />

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑资产抽屉 -->
    <el-drawer
      v-model="drawerVisible"
      :title="drawerTitle"
      size="70%"
      destroy-on-close
    >
      <asset-form
        :is-edit="isEdit"
        :asset-data="currentAsset"
        @submit="handleFormSubmit"
        @cancel="drawerVisible = false"
      />
    </el-drawer>

    <!-- 详情抽屉 -->
    <el-drawer
      v-model="detailDrawerVisible"
      title="资产详情"
      size="50%"
    >
      <el-descriptions
        v-if="currentAsset"
        :column="1"
        border
        class="asset-detail"
      >
        <el-descriptions-item label="资产编号">{{ currentAsset.assetCode }}</el-descriptions-item>
        <el-descriptions-item label="资产名称">{{ currentAsset.assetName }}</el-descriptions-item>
        <el-descriptions-item label="资产类型">{{ currentAsset.assetType?.name }}</el-descriptions-item>
        <el-descriptions-item label="型号">{{ currentAsset.model }}</el-descriptions-item>
        <el-descriptions-item label="规格">{{ currentAsset.specification }}</el-descriptions-item>
        <el-descriptions-item label="所属单位">{{ currentAsset.unitName }}</el-descriptions-item>
        <el-descriptions-item label="所属部门">{{ currentAsset.departmentName }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusTagType(currentAsset.statusId)">
            {{ currentAsset.statusName }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="采购日期">{{ currentAsset.purchaseDate }}</el-descriptions-item>
        <el-descriptions-item label="采购价格">{{ formatAmount(currentAsset.purchasePrice) }}</el-descriptions-item>
        <el-descriptions-item label="质保期">{{ currentAsset.warrantyPeriod || '--' }}</el-descriptions-item>
        <el-descriptions-item label="制造商">{{ currentAsset.manufacturer || '--' }}</el-descriptions-item>
        <el-descriptions-item label="供应商">{{ currentAsset.supplier || '--' }}</el-descriptions-item>
        <el-descriptions-item label="存放位置">{{ currentAsset.location || '--' }}</el-descriptions-item>
        <el-descriptions-item label="责任人">{{ currentAsset.responsiblePerson || '--' }}</el-descriptions-item>
        <el-descriptions-item label="序列号">{{ currentAsset.serialNumber || '--' }}</el-descriptions-item>
        <el-descriptions-item label="使用状态">{{ currentAsset.useStatusName }}</el-descriptions-item>
        <el-descriptions-item label="创建人">{{ currentAsset.createBy }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ currentAsset.createTime }}</el-descriptions-item>
        <el-descriptions-item v-if="currentAsset.updateBy" label="更新人">{{ currentAsset.updateBy }}</el-descriptions-item>
        <el-descriptions-item v-if="currentAsset.updateTime" label="更新时间">{{ currentAsset.updateTime }}</el-descriptions-item>
        <el-descriptions-item v-if="currentAsset.remark" label="备注">{{ currentAsset.remark }}</el-descriptions-item>

        <!-- 附加属性 -->
        <template v-if="currentAsset.attributes">
          <el-descriptions-item v-for="(value, key) in currentAsset.attributes" :key="key" :label="key">
            {{ value }}
          </el-descriptions-item>
        </template>
      </el-descriptions>

      <!-- 二维码 -->
      <div v-if="currentAssetQrCode" class="qrcode-preview">
        <h3>资产二维码</h3>
        <QrCodeCard
          ref="qrCodeCardRef"
          :asset-data="currentAssetQrCode" 
          :is-preview="true"
          :template-type="qrCodeSettings.templateType"
        />
        <div class="qrcode-actions">
          <el-button type="primary" @click="handlePrintQrCode">
            <el-icon><Printer /></el-icon> 打印
          </el-button>
        </div>
      </div>
    </el-drawer>

    <!-- 导出字段选择对话框 -->
    <el-dialog
      v-model="exportDialogVisible"
      title="选择导出字段"
      width="500px"
    >
      <export-field-selector
        v-model:fields="exportFields"
        @change="handleExportFieldsChange"
      />
      <template #footer>
        <el-button @click="exportDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmExport">确认导出</el-button>
      </template>
    </el-dialog>

    <!-- 批量打印对话框 -->
    <el-dialog
      v-model="batchPrintDialogVisible"
      title="批量打印资产标签"
      width="600px"
    >
      <div class="print-settings">
        <h3>打印设置</h3>
        <el-form :model="qrCodeSettings" label-width="100px">
          <el-form-item label="模板类型">
            <el-radio-group v-model="qrCodeSettings.templateType">
              <el-radio-button label="A4">A4纸张</el-radio-button>
              <el-radio-button label="thermal">热敏纸</el-radio-button>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>

      <div class="print-preview">
        <h3>预览</h3>
        <div class="qrcode-list" v-if="batchQrCodes.length">
          <QrCodeCard
            v-for="qrCode in batchQrCodes.slice(0, 2)"
            :key="qrCode.id"
            :asset-data="qrCode"
            :is-preview="true"
            :template-type="qrCodeSettings.templateType"
          />
          <div v-if="batchQrCodes.length > 2" class="more-indicator">
            还有 {{ batchQrCodes.length - 2 }} 个标签...
          </div>
        </div>
      </div>

      <template #footer>
        <el-button @click="batchPrintDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmBatchPrint">确认打印</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Upload, Download, Delete, Printer, DataLine, Check, Tools, ArrowDown, ArrowUp } from '@element-plus/icons-vue'
import AssetCardView from './components/AssetCardView.vue'
import AssetForm from './components/AssetForm.vue'
import QrCodeCard from './components/QrCodeCard.vue'
import ExportFieldSelector from './components/ExportFieldSelector.vue'
import { getStatusTagType, formatAmount, objectsToCSV, filterDataByFields, downloadBlob } from './utils'
import { CONSTANTS, mockGetAssetList, mockGetAssetDetail, mockAddAsset, mockUpdateAsset, mockDeleteAsset, mockBatchDeleteAsset, mockGenerateQrCode, mockBatchGenerateQrCode } from './mock'
import { exportFieldOptions } from './mock'
import type { AssetListItem, Asset, AssetQrCode, ExportField, PrintTemplateType } from '@/types/asset'

// 常量数据
const constants = CONSTANTS

// 加载状态
const loading = ref(false)

// 高级搜索显示状态
const showAdvanced = ref(false)

// 资产列表数据
const assetList = ref<AssetListItem[]>([])

// 统计数据
const statistics = ref({
  total: 0,
  inUse: 0,
  maintenance: 0,
  scrapped: 0
})

// 总数
const total = ref(0)

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  keyword: '',
  assetTypeId: undefined as number | undefined,
  statusId: undefined as number | undefined,
  unitId: undefined as number | undefined,
  departmentId: undefined as number | undefined,
  responsiblePerson: '',
  startDate: '',
  endDate: ''
})

// 日期范围
const dateRange = ref<string[]>([])

// 表格引用
const tableRef = ref()

// 选中的行ID
const selectedIds = ref<string[]>([])

// 视图模式：表格/卡片
const viewMode = ref<'table' | 'card'>('table')

// 抽屉可见性
const drawerVisible = ref(false)

// 抽屉标题
const drawerTitle = ref('')

// 是否为编辑模式
const isEdit = ref(false)

// 当前操作的资产
const currentAsset = ref<Asset | null>(null)

// 详情抽屉可见性
const detailDrawerVisible = ref(false)

// 当前资产二维码
const currentAssetQrCode = ref<AssetQrCode | null>(null)

// 二维码卡片组件引用
const qrCodeCardRef = ref()

// 导出对话框可见性
const exportDialogVisible = ref(false)

// 导出字段列表
const exportFields = ref<ExportField[]>(JSON.parse(JSON.stringify(exportFieldOptions)))

// 批量打印对话框可见性
const batchPrintDialogVisible = ref(false)

// 批量打印的二维码列表
const batchQrCodes = ref<AssetQrCode[]>([])

// 二维码打印设置
const qrCodeSettings = reactive({
  templateType: 'A4' as PrintTemplateType,
  showMoreInfo: false
})

// 根据选择的单位过滤部门
const filteredDepartments = computed(() => {
  if (!queryParams.unitId) return []
  return constants.departments.filter(dept => dept.unitId === queryParams.unitId)
})

// 获取资产列表
const getList = async () => {
  loading.value = true
  try {
    // 处理日期范围
    if (dateRange.value && dateRange.value.length === 2) {
      [queryParams.startDate, queryParams.endDate] = dateRange.value
    } else {
      queryParams.startDate = ''
      queryParams.endDate = ''
    }
    
    const res = await mockGetAssetList(queryParams)
    assetList.value = res.rows
    total.value = res.total
    
    // 更新统计信息
    updateStatistics()
  } catch (error) {
    ElMessage.error('获取资产列表失败')
  } finally {
    loading.value = false
  }
}

// 更新统计信息
const updateStatistics = () => {
  // 模拟获取统计数据
  statistics.value = {
    total: total.value,
    inUse: Math.floor(total.value * 0.7),
    maintenance: Math.floor(total.value * 0.2),
    scrapped: Math.floor(total.value * 0.1)
  }
}

// 查询
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置查询
const resetQuery = () => {
  const queryFormRef = ref()
  queryFormRef.value?.resetFields()
  dateRange.value = []
  queryParams.pageNum = 1
  queryParams.keyword = ''
  queryParams.assetTypeId = undefined
  queryParams.statusId = undefined
  queryParams.unitId = undefined
  queryParams.departmentId = undefined
  queryParams.responsiblePerson = ''
  queryParams.startDate = ''
  queryParams.endDate = ''
  getList()
}

// 单位变更处理
const handleUnitChange = () => {
  queryParams.departmentId = undefined
}

// 每页条数变化
const handleSizeChange = (size: number) => {
  queryParams.pageSize = size
  getList()
}

// 页码变化
const handleCurrentChange = (page: number) => {
  queryParams.pageNum = page
  getList()
}

// 表格选择变化
const handleSelectionChange = (selection: AssetListItem[]) => {
  selectedIds.value = selection.map(item => item.id)
}

// 新增资产
const handleAdd = () => {
  drawerTitle.value = '新增资产'
  isEdit.value = false
  currentAsset.value = null
  drawerVisible.value = true
}

// 编辑资产
const handleEdit = async (row: AssetListItem) => {
  try {
    const res = await mockGetAssetDetail(row.id)
    currentAsset.value = res
    drawerTitle.value = '编辑资产'
    isEdit.value = true
    drawerVisible.value = true
  } catch (error) {
    ElMessage.error('获取资产详情失败')
  }
}

// 查看资产详情
const handleView = async (row: AssetListItem) => {
  try {
    const res = await mockGetAssetDetail(row.id)
    currentAsset.value = res
    
    // 生成二维码
    const qrCode = await mockGenerateQrCode(row.id)
    currentAssetQrCode.value = qrCode
    
    detailDrawerVisible.value = true
  } catch (error) {
    ElMessage.error('获取资产详情失败')
  }
}

// 删除资产
const handleDelete = (row: AssetListItem) => {
  ElMessageBox.confirm(`确定要删除资产"${row.assetName}"吗？`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await mockDeleteAsset(row.id)
      ElMessage.success('删除成功')
      getList()
    } catch (error) {
      ElMessage.error('删除失败')
    }
  }).catch(() => {})
}

// 批量删除
const handleBatchDelete = () => {
  if (!selectedIds.value.length) {
    ElMessage.warning('请选择要删除的资产')
    return
  }
  
  ElMessageBox.confirm(`确定要删除选中的 ${selectedIds.value.length} 个资产吗？`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await mockBatchDeleteAsset(selectedIds.value)
      ElMessage.success('批量删除成功')
      getList()
    } catch (error) {
      ElMessage.error('批量删除失败')
    }
  }).catch(() => {})
}

// 表单提交
const handleFormSubmit = async (formData: any) => {
  try {
    if (isEdit.value) {
      await mockUpdateAsset(formData)
      ElMessage.success('修改成功')
    } else {
      await mockAddAsset(formData)
      ElMessage.success('添加成功')
    }
    drawerVisible.value = false
    getList()
  } catch (error) {
    if (error instanceof Error) {
      ElMessage.error(error.message)
    } else {
      ElMessage.error('操作失败')
    }
  }
}

// 处理更多操作命令
const handleCommand = (command: string, row: AssetListItem) => {
  switch(command) {
    case 'qrcode':
      handleGenerateQrCode(row)
      break
    case 'print':
      handlePrint(row)
      break
    case 'transfer':
      handleTransfer(row)
      break
    case 'repair':
      handleRepair(row)
      break
    case 'scrap':
      handleScrap(row)
      break
  }
}

// 生成二维码
const handleGenerateQrCode = async (row: AssetListItem) => {
  try {
    const res = await mockGetAssetDetail(row.id)
    currentAsset.value = res
    
    const qrCode = await mockGenerateQrCode(row.id)
    currentAssetQrCode.value = qrCode
    
    detailDrawerVisible.value = true
    
    // 滚动到二维码位置
    await nextTick()
    const qrcodeElement = document.querySelector('.qrcode-preview')
    qrcodeElement?.scrollIntoView({ behavior: 'smooth' })
  } catch (error) {
    ElMessage.error('生成二维码失败')
  }
}

// 打印单个资产标签
const handlePrint = async (row: AssetListItem) => {
  try {
    const qrCode = await mockGenerateQrCode(row.id)
    currentAssetQrCode.value = qrCode
    
    await nextTick()
    qrCodeCardRef.value?.printCard()
  } catch (error) {
    ElMessage.error('打印标签失败')
  }
}

// 打印预览中的二维码
const handlePrintQrCode = () => {
  qrCodeCardRef.value?.printCard()
}

// 资产调拨
const handleTransfer = (row: AssetListItem) => {
  ElMessage.info('暂未实现资产调拨功能')
}

// 资产报修
const handleRepair = (row: AssetListItem) => {
  ElMessage.info('暂未实现资产报修功能')
}

// 资产报废
const handleScrap = (row: AssetListItem) => {
  ElMessage.info('暂未实现资产报废功能')
}

// 导入资产
const handleImport = () => {
  ElMessage.info('暂未实现导入功能')
}

// 导出资产
const handleExport = () => {
  exportDialogVisible.value = true
}

// 导出字段变更
const handleExportFieldsChange = (fields: ExportField[]) => {
  exportFields.value = fields
}

// 确认导出
const confirmExport = async () => {
  try {
    // 获取所有数据（实际应该调用API进行服务器端导出）
    const allParams = { ...queryParams, pageNum: 1, pageSize: 1000 }
    const res = await mockGetAssetList(allParams)
    
    // 创建CSV内容
    const selectedFields = exportFields.value.filter(f => f.checked)
    const headers = selectedFields.map(f => ({ label: f.label, key: f.value }))
    const filteredData = filterDataByFields(res.rows, exportFields.value)
    const csvContent = objectsToCSV(filteredData, headers)
    
    // 创建Blob并下载
    const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' })
    const fileName = `资产台账_${new Date().toISOString().split('T')[0]}.csv`
    downloadBlob(blob, fileName)
    
    exportDialogVisible.value = false
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

// 批量打印
const handleBatchPrint = async () => {
  if (!selectedIds.value.length) {
    ElMessage.warning('请选择要打印的资产')
    return
  }
  
  try {
    batchQrCodes.value = await mockBatchGenerateQrCode(selectedIds.value)
    batchPrintDialogVisible.value = true
  } catch (error) {
    ElMessage.error('获取二维码数据失败')
  }
}

// 确认批量打印
const confirmBatchPrint = () => {
  // 创建打印窗口
  const printWindow = window.open('', '_blank')
  if (!printWindow) {
    ElMessage.error('请允许弹出窗口')
    return
  }
  
  // 使用DOM API创建内容
  const doc = printWindow.document
  doc.open()
  
  // 创建HTML结构
  const html = doc.createElement('html')
  
  // 创建head
  const head = doc.createElement('head')
  const title = doc.createElement('title')
  title.textContent = '资产标签打印'
  
  const style = doc.createElement('style')
  style.textContent = `
    @media print {
      body {
        margin: 0;
        padding: 10px;
      }
      .print-container {
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;
      }
      .page-break {
        break-after: page;
      }
    }
  `
  
  head.appendChild(title)
  head.appendChild(style)
  
  // 创建body
  const body = doc.createElement('body')
  const container = doc.createElement('div')
  container.className = 'print-container'
  
  // 添加卡片
  batchQrCodes.value.forEach(qrCode => {
    const card = doc.createElement('div')
    card.className = 'qr-code-card'
    card.style.cssText = `
      border: 1px solid #ddd;
      border-radius: 8px;
      padding: 12px;
      width: ${qrCodeSettings.templateType === 'thermal' ? '76mm' : '85mm'};
      margin: 10px;
      display: inline-block;
      box-sizing: border-box;
      font-family: Arial, sans-serif;
    `
    
    // 卡片头部
    const header = doc.createElement('div')
    header.style.cssText = `
      border-bottom: 1px solid #eee;
      padding-bottom: 8px;
      margin-bottom: 10px;
      text-align: center;
    `
    
    const title = doc.createElement('h2')
    title.textContent = qrCode.assetName
    title.style.cssText = `
      margin: 0;
      font-size: 16px;
      font-weight: bold;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    `
    
    const company = doc.createElement('div')
    company.textContent = '水投集团'
    company.style.cssText = `
      font-size: 12px;
      color: #666;
      margin-top: 2px;
    `
    
    header.appendChild(title)
    header.appendChild(company)
    
    // 卡片内容
    const content = doc.createElement('div')
    content.style.cssText = `
      display: flex;
      align-items: center;
    `
    
    // 二维码图片
    const qrWrapper = doc.createElement('div')
    qrWrapper.style.cssText = `
      flex: 0 0 auto;
      margin-right: 10px;
    `
    
    const img = doc.createElement('img')
    img.src = qrCode.qrCodeUrl
    img.style.cssText = `
      width: ${qrCodeSettings.templateType === 'thermal' ? '90px' : '100px'};
      height: ${qrCodeSettings.templateType === 'thermal' ? '90px' : '100px'};
    `
    
    qrWrapper.appendChild(img)
    
    // 资产信息
    const info = doc.createElement('div')
    info.style.cssText = `
      flex: 1;
      font-size: 12px;
    `
    
    // 资产编号
    const codeItem = doc.createElement('div')
    codeItem.style.cssText = 'margin-bottom: 3px;'
    
    const codeLabel = doc.createElement('span')
    codeLabel.textContent = '资产编号：'
    codeLabel.style.cssText = 'color: #666;'
    
    const codeValue = doc.createElement('span')
    codeValue.textContent = qrCode.assetCode
    codeValue.style.cssText = 'font-weight: bold;'
    
    codeItem.appendChild(codeLabel)
    codeItem.appendChild(codeValue)
    
    // 使用单位
    const unitItem = doc.createElement('div')
    unitItem.style.cssText = 'margin-bottom: 3px;'
    
    const unitLabel = doc.createElement('span')
    unitLabel.textContent = '使用单位：'
    unitLabel.style.cssText = 'color: #666;'
    
    const unitValue = doc.createElement('span')
    unitValue.textContent = qrCode.unitName
    unitValue.style.cssText = 'font-weight: bold;'
    
    unitItem.appendChild(unitLabel)
    unitItem.appendChild(unitValue)
    
    info.appendChild(codeItem)
    info.appendChild(unitItem)
    
    // 责任人（可选）
    if (qrCode.responsiblePerson) {
      const personItem = doc.createElement('div')
      personItem.style.cssText = 'margin-bottom: 3px;'
      
      const personLabel = doc.createElement('span')
      personLabel.textContent = '责任人：'
      personLabel.style.cssText = 'color: #666;'
      
      const personValue = doc.createElement('span')
      personValue.textContent = qrCode.responsiblePerson
      personValue.style.cssText = 'font-weight: bold;'
      
      personItem.appendChild(personLabel)
      personItem.appendChild(personValue)
      info.appendChild(personItem)
    }
    
    content.appendChild(qrWrapper)
    content.appendChild(info)
    
    // 卡片底部
    const footer = doc.createElement('div')
    footer.style.cssText = `
      text-align: center;
      margin-top: 10px;
      font-size: 10px;
      color: #999;
    `
    
    const footerText = doc.createElement('span')
    footerText.textContent = '扫码查看资产详情'
    footer.appendChild(footerText)
    
    // 组装卡片
    card.appendChild(header)
    card.appendChild(content)
    card.appendChild(footer)
    
    container.appendChild(card)
  })
  
  body.appendChild(container)
  
  // 添加打印脚本
  const script = doc.createElement('script')
  script.textContent = `
    window.onload = function() {
      window.print();
      setTimeout(function() { window.close(); }, 100);
    };
  `
  
  body.appendChild(script)
  html.appendChild(head)
  html.appendChild(body)
  doc.appendChild(html)
  
  doc.close()
  batchPrintDialogVisible.value = false
}

// 初始化
onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  
  .stat-cards {
    margin-bottom: 20px;
    
    .stat-card {
      transition: all 0.3s;
      
      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      }
      
      .stat-content {
        display: flex;
        align-items: center;
        
        .stat-icon {
          font-size: 40px;
          margin-right: 15px;
          
          .text-primary {
            color: #409EFF;
          }
          
          .text-success {
            color: #67C23A;
          }
          
          .text-warning {
            color: #E6A23C;
          }
          
          .text-danger {
            color: #F56C6C;
          }
        }
        
        .stat-info {
          .stat-value {
            font-size: 24px;
            font-weight: bold;
            line-height: 1;
            margin-bottom: 5px;
          }
          
          .stat-label {
            font-size: 14px;
            color: #909399;
          }
        }
      }
    }
  }
  
  .search-card {
    margin-bottom: 20px;
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .header-left {
      display: flex;
      gap: 10px;
    }
  }
  
  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
  
  .asset-detail {
    margin: 20px;
  }
  
  .qrcode-preview {
    margin: 20px;
    
    h3 {
      margin-bottom: 15px;
    }
    
    .qrcode-actions {
      margin-top: 15px;
      text-align: center;
    }
  }
  
  .print-settings, .print-preview {
    margin-bottom: 20px;
    
    h3 {
      margin-bottom: 15px;
      font-size: 16px;
    }
  }
  
  .qrcode-list {
    display: flex;
    flex-wrap: wrap;
    
    .more-indicator {
      margin: 10px;
      font-style: italic;
      color: #909399;
    }
  }
}
</style> 