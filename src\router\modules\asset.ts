import { RouteRecordRaw } from 'vue-router'

const assetRoutes: RouteRecordRaw = {
  path: '/asset',
  component: () => import('@/layout/Layout.vue'),
  meta: {
    title: '资产管理',
    icon: 'ep:goods',
    // roles: ['admin', 'asset_manager']
  },
  children: [
    // 1. 资产配置管理
    {
      path: 'config',
      component: () => import('@/layout/blank.vue'),
      meta: { title: '资产配置管理' },
      children: [
        {
          path: 'type',
          component: () => import('@/views/asset/type/AssetType.vue'),
          meta: { title: '资产类型管理' }
        },
        {
          path: 'department',
          component: () => import('@/views/asset/unit/index.vue'),
          meta: { title: '使用单位设置' }
        },
        {
          path: 'template',
          component: () => import('@/views/asset/attribute-template/index.vue'),
          meta: { title: '属性模板设置' }
        }
        // {
        //   path: 'code-rule',
        //   component: () => import('@/views/asset/config/code-rule/index.vue'),
        //   meta: { title: '编号规则设置' }
        // }
      ]
    },
    
    // 2. 资产台账中心
    {
      path: 'ledger',
      component: () => import('@/layout/blank.vue'),
      meta: { title: '资产台账中心' },
      children: [
        {
          path: 'overview',
          component: () => import('@/views/asset/ledger/overview/index.vue'),
          meta: { title: '资产总览' }
        },
        {
          path: 'list',
          component: () => import('@/views/asset/ledger/list/index.vue'),
          meta: { title: '资产明细' }
        // },
        // {
        //   path: 'label',
        //   component: () => import('@/views/asset/ledger/label/index.vue'),
        //   meta: { title: '标签打印' }
        }
      ]
    },
    
    // 3. 资产生命周期管理
    {
      path: 'lifecycle',
      component: () => import('@/layout/blank.vue'),
      meta: { title: '生命周期管理' },
      children: [
        {
          path: 'acceptance',
          component: () => import('@/views/asset/lifecycle/acceptance/index.vue'),
          meta: { title: '资产验收' }
        },
        // {
        //   path: 'storage',
        //   component: () => import('@/views/asset/lifecycle/storage/index.vue'),
        //   meta: { title: '入库登记' }
        // },
        {
          path: 'transfer',
          component: () => import('@/views/asset/lifecycle/transfer/index.vue'),
          meta: { title: '资产调拨' }
        },
        {
          path: 'idle',
          component: () => import('@/views/asset/lifecycle/idle/index.vue'),
          meta: { title: '闲置处理' }
        },
        {
          path: 'change',
          component: () => import('@/views/asset/lifecycle/change/index.vue'),
          meta: { title: '变更登记' }
        },
        {
          path: 'return',
          component: () => import('@/views/asset/lifecycle/return/index.vue'),
          meta: { title: '退库处理' }
        },
        {
          path: 'scrap',
          component: () => import('@/views/asset/lifecycle/scrap/index.vue'),
          meta: { title: '报废处理' }
        }
      ]
    },
    
    // 4. 资产运维与维保
    {
      path: 'maintenance',
      component: () => import('@/layout/blank.vue'),
      meta: { title: '运维维保' },
      children: [
        {
          path: 'repair',
          component: () => import('@/views/asset/maintenance/repair/index.vue'),
          meta: { title: '报修申请' }
        },
        {
          path: 'dispatch',
          component: () => import('@/views/asset/maintenance/dispatch/index.vue'),
          meta: { title: '派单处理' }
        },
        {
          path: 'record',
          component: () => import('@/views/asset/maintenance/record/index.vue'),
          meta: { title: '维修记录' }
        },
        {
          path: 'plan',
          component: () => import('@/views/asset/maintenance/plan/index.vue'),
          meta: { title: '维保计划' }
        },
        {
          path: 'performance',
          component: () => import('@/views/asset/maintenance/performance/index.vue'),
          meta: { title: '维修绩效' }
        }
      ]
    },
    
    // 5. 统计与报表分析
    // {
    //   path: 'statistics',
    //   component: () => import('@/layout/blank.vue'),
    //   meta: { title: '统计报表' },
    //   children: [
    //     {
    //       path: 'summary',
    //       component: () => import('@/views/asset/statistics/summary/index.vue'),
    //       meta: { title: '资产汇总' }
    //     },
    //     {
    //       path: 'depreciation',
    //       component: () => import('@/views/asset/statistics/depreciation/index.vue'),
    //       meta: { title: '折旧分析' }
    //     },
    //     {
    //       path: 'maintenance',
    //       component: () => import('@/views/asset/statistics/maintenance/index.vue'),
    //       meta: { title: '运维统计' }
    //     },
    //     {
    //       path: 'lifecycle',
    //       component: () => import('@/views/asset/statistics/lifecycle/index.vue'),
    //       meta: { title: '生命周期分析' }
    //     }
    //   ]
    // }
  ]
}

export default assetRoutes 