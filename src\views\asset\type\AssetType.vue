<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch">
      <el-form-item label="资产类型名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入资产类型名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="资产类型编码" prop="code">
        <el-input
          v-model="queryParams.code"
          placeholder="请输入资产类型编码"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['asset:type:add']"
        >新增</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @query-table="getList" />
    </el-row>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="typeList">
      <el-table-column label="资产类型名称" align="center" prop="name" />
      <el-table-column label="资产类型编码" align="center" prop="code" />
      <el-table-column label="描述" align="center" prop="description" :show-overflow-tooltip="true" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            type="text"
            icon="Edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <attribute-template-button :row="scope.row" />
          <el-button
            type="text"
            icon="Delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改资产类型对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="assetTypeRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="资产类型名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入资产类型名称" />
        </el-form-item>
        <el-form-item label="资产类型编码" prop="code">
          <el-input v-model="form.code" placeholder="请输入资产类型编码" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入描述" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import AttributeTemplateButton from './AttributeTemplateButton.vue'
// 注释原API引入
// import { getAssetTypeList, addAssetType, updateAssetType, delAssetType } from '@/api/asset/assetType'

const loading = ref(true)
const showSearch = ref(true)
const total = ref(0)
const open = ref(false)
const title = ref('')
const typeList = ref([])

// 模拟数据 - 资产类型列表
const mockAssetTypes = ref([
  { 
    id: 1, 
    name: '生产类资产', 
    code: 'PRODUCTION', 
    description: '包括水厂设备、管网设施、泵站设备等用于水务生产和处理的资产' 
  },
  { 
    id: 2, 
    name: '行政类资产', 
    code: 'ADMIN', 
    description: '包括办公楼、车辆、办公设备等行政管理相关的资产' 
  },
  { 
    id: 3, 
    name: '特许资产', 
    code: 'FRANCHISE', 
    description: '特许经营权等无形资产，包括水务特许经营权、污水处理特许经营权等' 
  },
  { 
    id: 4, 
    name: '基础设施资产', 
    code: 'INFRASTRUCTURE', 
    description: '水厂建筑物、管网、泵站等基础设施资产' 
  },
  { 
    id: 5, 
    name: '机器设备', 
    code: 'MACHINERY', 
    description: '制水设备、输水设备、检测设备等机器设备类资产' 
  },
  { 
    id: 6, 
    name: '运输设备', 
    code: 'TRANSPORT', 
    description: '公务用车、工程车辆、应急抢修车辆等运输类资产' 
  },
  { 
    id: 7, 
    name: '电子设备', 
    code: 'ELECTRONIC', 
    description: '电脑、打印机、监控设备、通信设备等电子类资产' 
  },
  { 
    id: 8, 
    name: '软件系统', 
    code: 'SOFTWARE', 
    description: '管理信息系统、财务系统、GIS系统等软件类资产' 
  },
  { 
    id: 9, 
    name: '土地资产', 
    code: 'LAND', 
    description: '厂区用地、办公楼用地等土地类资产' 
  },
  { 
    id: 10, 
    name: '房屋建筑', 
    code: 'BUILDING', 
    description: '办公楼、厂房、仓库等房屋建筑类资产' 
  }
])

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  name: undefined,
  code: undefined
})

// 表单参数
const form = reactive({
  id: undefined,
  name: undefined,
  code: undefined,
  description: undefined
})

// 表单校验规则
const rules = reactive({
  name: [{ required: true, message: '资产类型名称不能为空', trigger: 'blur' }],
  code: [{ required: true, message: '资产类型编码不能为空', trigger: 'blur' }]
})

/** 查询资产类型列表 - 模拟API */
const getList = async () => {
  loading.value = true
  try {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 300))
    
    // 根据查询条件过滤数据
    let filteredList = [...mockAssetTypes.value]
    
    // 按名称过滤
    if (queryParams.name) {
      filteredList = filteredList.filter(
        item => item.name.toLowerCase().includes(queryParams.name.toLowerCase())
      )
    }
    
    // 按编码过滤
    if (queryParams.code) {
      filteredList = filteredList.filter(
        item => item.code.toLowerCase().includes(queryParams.code.toLowerCase())
      )
    }
    
    // 计算总数
    total.value = filteredList.length
    
    // 分页处理
    const start = (queryParams.pageNum - 1) * queryParams.pageSize
    const end = start + queryParams.pageSize
    typeList.value = filteredList.slice(start, end)
  } finally {
    loading.value = false
  }
}

/** 取消按钮 */
const cancel = () => {
  open.value = false
  reset()
}

/** 表单重置 */
const reset = () => {
  form.id = undefined
  form.name = undefined
  form.code = undefined
  form.description = undefined
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryParams.name = undefined
  queryParams.code = undefined
  handleQuery()
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset()
  open.value = true
  title.value = '添加资产类型'
}

/** 修改按钮操作 */
const handleUpdate = (row: any) => {
  reset()
  const id = row.id
  form.id = id
  form.name = row.name
  form.code = row.code
  form.description = row.description
  open.value = true
  title.value = '修改资产类型'
}

/** 提交按钮 - 模拟API */
const submitForm = async () => {
  const assetTypeRef = ref()
  
  // 简化版表单验证
  if (!form.name || !form.code) {
    ElMessage.error('请填写必填项')
    return
  }
  
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 300))
  
  if (form.id) {
    // 修改资产类型
    const index = mockAssetTypes.value.findIndex(item => item.id === form.id)
    if (index !== -1) {
      mockAssetTypes.value[index] = { ...form }
      ElMessage.success('修改成功')
    }
  } else {
    // 新增资产类型
    const newId = Math.max(...mockAssetTypes.value.map(item => item.id)) + 1
    mockAssetTypes.value.push({ 
      id: newId, 
      name: form.name, 
      code: form.code, 
      description: form.description 
    })
    ElMessage.success('新增成功')
  }
  
  open.value = false
  getList()
}

/** 删除按钮操作 - 模拟API */
const handleDelete = (row: any) => {
  ElMessageBox.confirm('是否确认删除资产类型编号为"' + row.id + '"的数据项?', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 300))
    
    const index = mockAssetTypes.value.findIndex(item => item.id === row.id)
    if (index !== -1) {
      mockAssetTypes.value.splice(index, 1)
      ElMessage.success('删除成功')
      getList()
    }
  })
}

onMounted(() => {
  getList()
})
</script> 