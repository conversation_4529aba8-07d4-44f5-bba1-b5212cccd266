<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { Search, Refresh, Plus, Edit, Delete, View } from '@element-plus/icons-vue'
import request from '@/config/axios'

// 类型定义
interface RepairRequest {
  id: string
  assetId: string
  assetCode: string
  assetName: string
  faultType: string
  faultDesc: string
  reportBy: string
  reportTime: string
  contactInfo: string
  status: string
  priority: string
  expectedTime: string
  images: string[]
}

interface Asset {
  id: string
  code: string
  name: string
  type: string
  department: string
  location: string
}

// 状态变量
const loading = ref(false)
const drawerVisible = ref(false)
const drawerTitle = ref('新增维修申请')
const isEdit = ref(false)

// 表单相关
const formRef = ref<FormInstance>()
const form = reactive({
  id: '',
  assetId: '',
  assetCode: '',
  assetName: '',
  faultType: '',
  faultDesc: '',
  reportBy: '当前用户',
  contactInfo: '',
  priority: 'normal',
  expectedTime: '',
  images: [] as string[]
})

// 表单验证规则
const rules = reactive<FormRules>({
  assetId: [
    { required: true, message: '请选择需要维修的资产', trigger: 'change' }
  ],
  faultType: [
    { required: true, message: '请选择故障类型', trigger: 'change' }
  ],
  faultDesc: [
    { required: true, message: '请输入故障描述', trigger: 'blur' }
  ],
  contactInfo: [
    { required: true, message: '请输入联系方式', trigger: 'blur' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ]
})

// 资产选择对话框
const assetSelectDialogVisible = ref(false)
const selectedAsset = ref<Asset | null>(null)
const assetList = ref<Asset[]>([])

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  status: '',
  keyword: '',
  dateRange: [] as string[]
})

// 表格数据
const tableData = ref<RepairRequest[]>([])
const total = ref(0)

// 上传相关
const uploadUrl = '/api/file/upload'
const uploadHeaders = {
  Authorization: 'Bearer token'
}

// 故障类型选项
const faultTypeOptions = [
  { value: 'hardware', label: '硬件故障' },
  { value: 'software', label: '软件故障' },
  { value: 'network', label: '网络故障' },
  { value: 'power', label: '电力故障' },
  { value: 'other', label: '其他故障' }
]

// 优先级选项
const priorityOptions = [
  { value: 'high', label: '高' },
  { value: 'normal', label: '中' },
  { value: 'low', label: '低' }
]

// 获取维修申请列表
const getList = async () => {
  loading.value = true
  try {
    // TODO: 调用API
    // 模拟API响应
    await new Promise(resolve => setTimeout(resolve, 300))
    
    // 生成模拟数据
    const mockData: RepairRequest[] = Array.from({ length: 15 }, (_, index) => ({
      id: `${index + 1}`,
      assetId: `${1000 + index}`,
      assetCode: `ZC${String(1000 + index).padStart(6, '0')}`,
      assetName: `测试设备${index + 1}`,
      faultType: faultTypeOptions[index % faultTypeOptions.length].value,
      faultDesc: `设备出现${faultTypeOptions[index % faultTypeOptions.length].label}，需要维修处理`,
      reportBy: '张三',
      reportTime: new Date(Date.now() - Math.random() * 10 * 24 * 60 * 60 * 1000).toLocaleString(),
      contactInfo: '13800138000',
      status: ['pending', 'processing', 'completed', 'rejected'][index % 4],
      priority: priorityOptions[index % priorityOptions.length].value,
      expectedTime: new Date(Date.now() + Math.random() * 10 * 24 * 60 * 60 * 1000).toLocaleDateString(),
      images: []
    }))

    // 分页处理
    const start = (queryParams.pageNum - 1) * queryParams.pageSize
    const end = Math.min(start + queryParams.pageSize, mockData.length)
    tableData.value = mockData.slice(start, end)
    total.value = mockData.length
  } catch (error) {
    console.error('获取维修申请列表失败:', error)
    ElMessage.error('获取维修申请列表失败')
  } finally {
    loading.value = false
  }
}

// 获取资产列表
const getAssetList = async () => {
  try {
    // 模拟API响应
    await new Promise(resolve => setTimeout(resolve, 300))
    
    // 生成模拟数据
    assetList.value = Array.from({ length: 10 }, (_, index) => ({
      id: `${1000 + index}`,
      code: `ZC${String(1000 + index).padStart(6, '0')}`,
      name: `测试设备${index + 1}`,
      type: ['生产设备', '办公设备', '运输设备'][index % 3],
      department: ['生产部', '行政部', '技术部'][index % 3],
      location: ['一号车间', '二号车间', '办公区'][index % 3]
    }))
  } catch (error) {
    console.error('获取资产列表失败:', error)
    ElMessage.error('获取资产列表失败')
  }
}

// 选择资产
const selectAsset = (asset: Asset) => {
  selectedAsset.value = asset
  form.assetId = asset.id
  form.assetCode = asset.code
  form.assetName = asset.name
  assetSelectDialogVisible.value = false
}

// 打开资产选择对话框
const openAssetSelectDialog = () => {
  getAssetList()
  assetSelectDialogVisible.value = true
}

// 上传文件前验证
const beforeUpload = (file: File) => {
  const isImage = ['image/jpeg', 'image/png', 'image/gif'].includes(file.type)
  const isLt5M = file.size / 1024 / 1024 < 5
  
  if (!isImage) {
    ElMessage.error('上传文件只能是图片格式!')
  }
  if (!isLt5M) {
    ElMessage.error('上传图片大小不能超过 5MB!')
  }
  
  return isImage && isLt5M
}

// 文件上传成功
const handleUploadSuccess = (response: any) => {
  form.images.push(response.data)
  ElMessage.success('上传成功')
}

// 移除上传的图片
const handleRemove = (index: number) => {
  form.images.splice(index, 1)
}

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
  form.id = ''
  form.assetId = ''
  form.assetCode = ''
  form.assetName = ''
  form.images = []
  selectedAsset.value = null
}

// 打开新增对话框
const handleAdd = () => {
  resetForm()
  isEdit.value = false
  drawerTitle.value = '新增维修申请'
  drawerVisible.value = true
}

// 查看详情
const handleView = (row: RepairRequest) => {
  // 直接导航到详情页或者打开详情抽屉
  ElMessage.info('查看详情功能开发中')
}

// 搜索
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置搜索
const handleReset = () => {
  queryParams.keyword = ''
  queryParams.status = ''
  queryParams.dateRange = []
  handleQuery()
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      // TODO: 调用保存API
      await new Promise(resolve => setTimeout(resolve, 500))
      ElMessage.success('提交成功')
      drawerVisible.value = false
      getList()
    }
  })
}

// 页面加载时获取数据
onMounted(() => {
  getList()
})
</script>

<template>
  <div class="repair-request-container">
    <!-- 搜索表单 -->
    <el-card class="search-card">
      <el-form :model="queryParams" ref="queryFormRef" :inline="true">
        <el-form-item label="关键词" prop="keyword">
          <el-input
            v-model="queryParams.keyword"
            placeholder="资产编号/名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择" clearable style="width: 200px">
            <el-option label="待处理" value="pending" />
            <el-option label="处理中" value="processing" />
            <el-option label="已完成" value="completed" />
            <el-option label="已拒绝" value="rejected" />
          </el-select>
        </el-form-item>
        <el-form-item label="申请日期" prop="dateRange">
          <el-date-picker
            v-model="queryParams.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>维修申请列表</span>
          <el-button type="primary" icon="Plus" @click="handleAdd">新增维修申请</el-button>
        </div>
      </template>

      <!-- 表格 -->
      <el-table :data="tableData" v-loading="loading" border style="width: 100%">
        <el-table-column prop="assetCode" label="资产编号" min-width="120" />
        <el-table-column prop="assetName" label="资产名称" min-width="120" />
        <el-table-column prop="faultDesc" label="故障描述" min-width="180" show-overflow-tooltip />
        <el-table-column prop="reportTime" label="申请时间" min-width="150" />
        <el-table-column prop="reportBy" label="申请人" min-width="100" />
        <el-table-column label="优先级" min-width="100">
          <template #default="{ row }">
            <el-tag 
              :type="row.priority === 'high' ? 'danger' : row.priority === 'normal' ? 'warning' : 'info'"
            >
              {{ row.priority === 'high' ? '高' : row.priority === 'normal' ? '中' : '低' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" min-width="100">
          <template #default="{ row }">
            <el-tag
              :type="
                row.status === 'pending' ? 'info' :
                row.status === 'processing' ? 'warning' :
                row.status === 'completed' ? 'success' :
                'danger'
              "
            >
              {{ row.status === 'pending' ? '待处理' :
                 row.status === 'processing' ? '处理中' :
                 row.status === 'completed' ? '已完成' : '已拒绝' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="150">
          <template #default="{ row }">
            <el-button type="primary" link icon="View" @click="handleView(row)">查看</el-button>
            <el-button 
              v-if="row.status === 'pending'" 
              type="primary" 
              link 
              icon="Edit"
            >编辑</el-button>
            <el-button 
              v-if="row.status === 'pending'" 
              type="danger" 
              link 
              icon="Delete"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页器 -->
      <el-pagination
        v-model:current-page="queryParams.pageNum"
        v-model:page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        class="pagination"
      />
    </el-card>

    <!-- 新增/编辑抽屉 -->
    <el-drawer
      v-model="drawerVisible"
      :title="drawerTitle"
      direction="rtl"
      size="50%"
      destroy-on-close
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        class="repair-form"
      >
        <el-form-item label="资产信息" prop="assetId">
          <div class="asset-selector">
            <div v-if="selectedAsset" class="selected-asset">
              <div>{{ selectedAsset.code }} - {{ selectedAsset.name }}</div>
              <div>{{ selectedAsset.type }} | {{ selectedAsset.department }}</div>
            </div>
            <el-button type="primary" @click="openAssetSelectDialog">选择资产</el-button>
          </div>
        </el-form-item>

        <el-form-item label="故障类型" prop="faultType">
          <el-select v-model="form.faultType" placeholder="请选择故障类型">
            <el-option
              v-for="item in faultTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="故障描述" prop="faultDesc">
          <el-input
            v-model="form.faultDesc"
            type="textarea"
            :rows="4"
            placeholder="请详细描述故障现象、发生时间和可能原因"
          />
        </el-form-item>

        <el-form-item label="优先级" prop="priority">
          <el-radio-group v-model="form.priority">
            <el-radio label="high">高</el-radio>
            <el-radio label="normal">中</el-radio>
            <el-radio label="low">低</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="期望完成时间" prop="expectedTime">
          <el-date-picker
            v-model="form.expectedTime"
            type="date"
            placeholder="请选择期望完成日期"
          />
        </el-form-item>

        <el-form-item label="联系方式" prop="contactInfo">
          <el-input v-model="form.contactInfo" placeholder="请输入手机号码或其他联系方式" />
        </el-form-item>

        <el-form-item label="故障图片">
          <el-upload
            action=""
            :http-request="() => {}"
            list-type="picture-card"
            :before-upload="beforeUpload"
            :on-success="handleUploadSuccess"
          >
            <el-icon><Plus /></el-icon>
          </el-upload>
          <div class="upload-tip">
            提示：可上传故障现场图片，支持 JPG、PNG 格式，单个文件不超过 5MB
          </div>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitForm">提交</el-button>
          <el-button @click="drawerVisible = false">取消</el-button>
        </el-form-item>
      </el-form>
    </el-drawer>

    <!-- 资产选择对话框 -->
    <el-dialog
      v-model="assetSelectDialogVisible"
      title="选择资产"
      width="800px"
    >
      <el-table :data="assetList" border style="width: 100%">
        <el-table-column prop="code" label="资产编号" min-width="120" />
        <el-table-column prop="name" label="资产名称" min-width="120" />
        <el-table-column prop="type" label="类型" min-width="100" />
        <el-table-column prop="department" label="部门" min-width="100" />
        <el-table-column prop="location" label="位置" min-width="100" />
        <el-table-column label="操作" fixed="right" width="80">
          <template #default="{ row }">
            <el-button type="primary" link @click="selectAsset(row)">选择</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.repair-request-container {
  padding: 20px;
  
  .search-card {
    margin-bottom: 20px;
  }
  
  .table-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .pagination {
      margin-top: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
  
  .repair-form {
    padding: 20px;
    
    .asset-selector {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .selected-asset {
        flex: 1;
      }
    }
    
    .upload-tip {
      color: #909399;
      font-size: 12px;
      margin-top: 10px;
    }
  }
}
</style> 