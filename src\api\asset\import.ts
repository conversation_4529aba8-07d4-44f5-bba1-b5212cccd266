import request from '@/utils/request'
import type { ImportPreviewData, ImportHistoryData, ImportDetail } from '@/types/asset/import'

/**
 * 下载资产导入模板
 */
export const downloadImportTemplate = (importType: string) => {
  return request.get({
    url: `/api/asset/import/template/${importType}`,
    responseType: 'blob'
  })
}

/**
 * 上传文件预览
 * @param file 上传的文件
 */
export const previewImportFile = (file: File) => {
  const formData = new FormData()
  formData.append('file', file)
  
  return request.post<ImportPreviewResult>({
    url: '/api/asset/import/preview',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 确认导入数据
 * @param data 确认导入的数据
 */
export const confirmImport = (data: any[]) => {
  return request.post<ImportResult>({
    url: '/api/asset/import/confirm',
    data
  })
}

/**
 * 获取导入历史记录
 * @param params 查询参数
 */
export const getImportHistory = (params?: any) => {
  return request.get<ImportRecord[]>({
    url: '/api/asset/import/history',
    params
  })
}

/**
 * 获取导入历史详情
 * @param id 历史记录ID
 */
export const getImportDetail = (id: string) => {
  return request.get<ImportResult>({
    url: `/api/asset/import/history/${id}/detail`
  })
}

/**
 * 导出资产台账
 * @param params 导出参数
 */
export const exportAssetLedger = (params?: any) => {
  return request.get({
    url: '/api/asset/import/export',
    params,
    responseType: 'blob'
  })
}

/**
 * 上传资产导入文件
 * @param file 文件对象
 * @returns 文件ID
 */
export function uploadImportFile(file: File) {
  const formData = new FormData()
  formData.append('file', file)
  
  return request({
    url: '/api/asset/import/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 获取导入预览数据
 * @param fileId 文件ID
 * @param importType 导入类型
 * @returns 预览数据
 */
export function getImportPreview(fileId: string, importType: string) {
  return request<ImportPreviewData>({
    url: '/api/asset/import/preview',
    method: 'get',
    params: {
      fileId,
      importType
    }
  })
}

/**
 * 确认导入
 * @param fileId 文件ID
 * @param importType 导入类型
 * @returns 导入ID
 */
export function confirmImport(fileId: string, importType: string) {
  return request({
    url: '/api/asset/import/confirm',
    method: 'post',
    data: {
      fileId,
      importType
    }
  })
}

/**
 * 获取导入历史记录
 * @param params 查询参数
 * @returns 导入历史记录
 */
export function getImportHistory(params: {
  pageNum: number;
  pageSize: number;
  keyword?: string;
}) {
  return request<ImportHistoryData>({
    url: '/api/asset/import/history',
    method: 'get',
    params
  })
}

/**
 * 获取导入详情
 * @param id 导入记录ID
 * @returns 导入详情
 */
export function getImportDetail(id: string) {
  return request<ImportDetail>({
    url: '/api/asset/import/detail',
    method: 'get',
    params: { id }
  })
}

/**
 * 获取导入模板
 * @param importType 导入类型
 * @returns 模板文件URL
 */
export function getImportTemplate(importType: string) {
  return request({
    url: '/api/asset/import/template',
    method: 'get',
    params: { importType }
  })
}

/**
 * 获取错误文件
 * @param errorFileId 错误文件ID
 * @returns 错误文件URL
 */
export function getErrorFile(errorFileId: string) {
  return request({
    url: '/api/asset/import/error-file',
    method: 'get',
    params: { errorFileId }
  })
}

/**
 * 下载错误数据
 * @param id 导入记录ID
 */
export function downloadErrorData(id: string) {
  return request({
    url: `/api/asset/import/error/${id}`,
    method: 'get',
    responseType: 'blob'
  })
}

/**
 * 导出资产数据
 * @param params 导出参数
 * @returns 导出的文件
 */
export function exportAssetData(params: any) {
  return request({
    url: '/api/asset/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

/**
 * 下载错误数据文件
 * @param errorFileId 错误文件ID
 */
export function downloadErrorFile(errorFileId: string) {
  return request({
    url: `/api/asset/import/error/${errorFileId}`,
    method: 'get',
    responseType: 'blob'
  })
} 