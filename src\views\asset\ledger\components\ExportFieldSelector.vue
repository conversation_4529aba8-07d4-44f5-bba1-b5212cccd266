<template>
  <div class="export-field-selector">
    <el-checkbox
      v-model="selectAll"
      :indeterminate="isIndeterminate"
      @change="handleCheckAllChange"
    >
      全选/取消
    </el-checkbox>
    
    <el-divider />
    
    <div class="field-list">
      <el-checkbox-group v-model="selectedFields" @change="handleCheckedChange">
        <div class="field-group">
          <div v-for="(field, index) in fields" :key="index" class="field-item">
            <el-checkbox :label="field.value">{{ field.label }}</el-checkbox>
          </div>
        </div>
      </el-checkbox-group>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { ExportField } from '@/types/asset'

// 定义属性
const props = defineProps<{
  fields: ExportField[]
}>()

// 定义事件
const emit = defineEmits(['update:fields', 'change'])

// 选中的字段值
const selectedFields = ref<string[]>([])

// 是否全选
const selectAll = ref(false)

// 是否部分选中
const isIndeterminate = ref(false)

// 初始化选中字段
const initSelectedFields = () => {
  selectedFields.value = props.fields
    .filter(field => field.checked)
    .map(field => field.value)
  
  updateSelectAllStatus()
}

// 更新全选状态
const updateSelectAllStatus = () => {
  const checkedCount = selectedFields.value.length
  const totalCount = props.fields.length
  
  selectAll.value = checkedCount === totalCount
  isIndeterminate.value = checkedCount > 0 && checkedCount < totalCount
}

// 处理全选/取消全选
const handleCheckAllChange = (val: boolean) => {
  selectedFields.value = val 
    ? props.fields.map(field => field.value)
    : []
  
  updateFieldsCheckedStatus()
  isIndeterminate.value = false
}

// 处理字段选择变化
const handleCheckedChange = (value: string[]) => {
  updateSelectAllStatus()
  updateFieldsCheckedStatus()
}

// 更新字段选中状态
const updateFieldsCheckedStatus = () => {
  const updatedFields = props.fields.map(field => ({
    ...field,
    checked: selectedFields.value.includes(field.value)
  }))
  
  emit('update:fields', updatedFields)
  emit('change', updatedFields)
}

// 监听字段变化
watch(() => props.fields, () => {
  initSelectedFields()
}, { deep: true })

// 初始化
initSelectedFields()
</script>

<style lang="scss" scoped>
.export-field-selector {
  padding: 10px;
  
  .field-list {
    max-height: 300px;
    overflow-y: auto;
    
    .field-group {
      display: flex;
      flex-wrap: wrap;
      
      .field-item {
        width: 33.333%;
        padding: 5px 0;
        
        @media (max-width: 768px) {
          width: 50%;
        }
        
        @media (max-width: 576px) {
          width: 100%;
        }
      }
    }
  }
}
</style> 