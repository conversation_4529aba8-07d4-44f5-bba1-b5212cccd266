<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getMaintenanceList, getMaintenanceStatistics, exportMaintenanceReport } from '@/api/asset/maintenance'
import { MaintenanceStatus, MaintenancePriority, MaintenanceType } from '@/types/asset/maintenance'
import * as echarts from 'echarts'

// 路由
const router = useRouter()
const route = useRoute()

// 页面状态
const loading = ref(false)
const activeTab = ref('request')

// 审批对话框
const approveDialogVisible = ref(false)
const rejectDialogVisible = ref(false)
const acceptDialogVisible = ref(false)
const currentItem = ref<MaintenanceItem | null>(null)
const approveForm = reactive({
  comments: '',
  assignedTo: '',
  plannedStartTime: '',
  plannedEndTime: '',
})
const rejectForm = reactive({
  comments: '',
})
const acceptForm = reactive({
  comments: '',
  acceptanceResult: true
})

// 搜索表单
const searchForm = reactive({
  pageNum: 1,
  pageSize: 10,
  keyword: '',
  assetType: '',
  departmentId: '',
  status: '',
  maintenanceType: '',
  priority: '',
  timeRange: [] as string[],
  requestUser: ''
})

// 重置搜索
const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    if (key !== 'pageNum' && key !== 'pageSize') {
      searchForm[key] = key === 'timeRange' ? [] : ''
    }
  })
  fetchMaintenanceList()
}

// 查询列表
const fetchMaintenanceList = () => {
  loading.value = true
  
  // 模拟数据，实际开发时使用API
  setTimeout(() => {
    generateMockData()
    loading.value = false
  }, 500)
  
  // 实际API调用
  // getMaintenanceList(searchForm)
  //   .then(res => {
  //     maintenanceList.value = res.data.list
  //     total.value = res.data.total
  //   })
  //   .catch(err => {
  //     ElMessage.error('获取维修列表失败')
  //     console.error(err)
  //   })
  //   .finally(() => {
  //     loading.value = false
  //   })
}

// 维修项目接口
interface MaintenanceItem {
  id: string;
  requestNo: string;
  assetName: string;
  assetCode: string;
  assetType: string;
  maintenanceType: string;
  department: string;
  location: string;
  faultDescription: string;
  priority: string;
  requestUser: string;
  requestTime: string;
  status: string;
  assignedTo?: string;
  plannedEndTime?: string;
  actualEndTime?: string;
  maintenanceResult?: string;
  totalCost?: number;
}

// 部门统计项
interface DepartmentStat {
  name: string;
  count: number;
  cost: number;
}

// 月度统计项
interface MonthStat {
  month: string;
  count: number;
  cost: number;
}

// 维修列表
const maintenanceList = ref<MaintenanceItem[]>([])
const total = ref(0)

// 维修统计
const statistics = reactive({
  total: 0,
  pending: 0,
  processing: 0,
  completed: 0,
  acceptanceRate: 0,
  averageResponseTime: 0,
  averageCompletionTime: 0,
  totalCost: 0,
  byType: {} as Record<string, number>,
  byDepartment: [] as DepartmentStat[],
  byMonth: [] as MonthStat[]
})

// 图表引用
const typeChartRef = ref<any>(null)
const departmentChartRef = ref<any>(null)
const monthChartRef = ref<any>(null)

// 生成模拟数据
const generateMockData = () => {
  const statusOptions = ['维修申请', '已派工', '维修中', '维修完成', '已验收', '已驳回']
  const priorityOptions = ['低', '中', '高', '紧急']
  const typeOptions = ['故障维修', '预防性维修', '日常维护', '校准', '大修']
  const assetTypes = ['办公设备', '生产设备', '运输设备', '电子设备', '通讯设备']
  const departments = ['行政部', '生产部', '财务部', '市场部', '人力资源部', '研发部']
  const locations = ['一号水厂', '二号水厂', '三号水厂', '总部大楼', '泵站']
  const faultReasons = [
    '设备无法启动', '异常噪音', '漏水', '控制面板错误', '功率下降',
    '电路故障', '机械卡顿', '过热', '压力异常', '信号丢失'
  ]
  
  const data: MaintenanceItem[] = []
  for (let i = 0; i < 50; i++) {
    const status = statusOptions[Math.floor(Math.random() * statusOptions.length)]
    const assignedTo = status !== '维修申请' ? `维修员${Math.floor(Math.random() * 10) + 1}` : undefined
    const plannedEndTime = assignedTo ? `2023-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}` : undefined
    const actualEndTime = status === '维修完成' || status === '已验收' ? `2023-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}` : undefined
    const maintenanceResult = actualEndTime ? '已修复，恢复正常运行' : undefined
    const totalCost = actualEndTime ? Math.floor(Math.random() * 10000) + 200 : undefined
    
    data.push({
      id: `maintenance-${i}`,
      requestNo: `WX${String(10000 + i).padStart(5, '0')}`,
      assetName: `${assetTypes[Math.floor(Math.random() * assetTypes.length)]}-${i + 1}`,
      assetCode: `ZC${String(20000 + i).padStart(6, '0')}`,
      assetType: assetTypes[Math.floor(Math.random() * assetTypes.length)],
      maintenanceType: typeOptions[Math.floor(Math.random() * typeOptions.length)],
      department: departments[Math.floor(Math.random() * departments.length)],
      location: locations[Math.floor(Math.random() * locations.length)],
      faultDescription: faultReasons[Math.floor(Math.random() * faultReasons.length)],
      priority: priorityOptions[Math.floor(Math.random() * priorityOptions.length)],
      requestUser: `申请人${i + 1}`,
      requestTime: `2023-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`,
      status,
      assignedTo,
      plannedEndTime,
      actualEndTime,
      maintenanceResult,
      totalCost
    })
  }
  
  maintenanceList.value = data
  total.value = data.length
  
  // 生成统计数据
  statistics.total = data.length
  statistics.pending = data.filter(item => item.status === '维修申请').length
  statistics.processing = data.filter(item => ['已派工', '维修中'].includes(item.status)).length
  statistics.completed = data.filter(item => ['维修完成', '已验收'].includes(item.status)).length
  statistics.acceptanceRate = Math.round(data.filter(item => item.status === '已验收').length / Math.max(1, statistics.completed) * 100)
  statistics.averageResponseTime = Math.round(Math.random() * 48) + 1 // 模拟平均响应时间（小时）
  statistics.averageCompletionTime = Math.round(Math.random() * 72) + 24 // 模拟平均完成时间（小时）
  statistics.totalCost = data.reduce((sum, item) => sum + (item.totalCost || 0), 0)
  
  // 按类型统计
  const typeStats: Record<string, number> = {}
  typeOptions.forEach(type => {
    typeStats[type] = data.filter(item => item.maintenanceType === type).length
  })
  statistics.byType = typeStats
  
  // 按部门统计
  const deptStats: DepartmentStat[] = []
  departments.forEach(dept => {
    const deptItems = data.filter(item => item.department === dept)
    deptStats.push({
      name: dept,
      count: deptItems.length,
      cost: deptItems.reduce((sum, item) => sum + (item.totalCost || 0), 0)
    })
  })
  statistics.byDepartment = deptStats
  
  // 按月统计
  const monthStats: MonthStat[] = []
  for (let i = 1; i <= 12; i++) {
    const month = `2023-${String(i).padStart(2, '0')}`
    const monthItems = data.filter(item => item.requestTime.startsWith(month))
    monthStats.push({
      month: `${i}月`,
      count: monthItems.length,
      cost: monthItems.reduce((sum, item) => sum + (item.totalCost || 0), 0)
    })
  }
  statistics.byMonth = monthStats
  
  // 初始化图表
  nextTick(() => {
    initCharts()
  })
}

// 初始化图表
const initCharts = () => {
  // 按类型统计图表
  const typeChart = echarts.init(document.getElementById('type-chart'))
  typeChartRef.value = typeChart
  
  const typeNames = Object.keys(statistics.byType)
  const typeCounts = typeNames.map(type => statistics.byType[type])
  
  const typeOption = {
    title: {
      text: '按维修类型统计',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 10,
      data: typeNames
    },
    series: [
      {
        name: '维修类型',
        type: 'pie',
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: typeNames.map((name, index) => ({
          value: typeCounts[index],
          name
        }))
      }
    ]
  }
  
  typeChart.setOption(typeOption)
  
  // 按部门统计图表
  const departmentChart = echarts.init(document.getElementById('department-chart'))
  departmentChartRef.value = departmentChart
  
  const deptNames = statistics.byDepartment.map(item => item.name)
  const deptCounts = statistics.byDepartment.map(item => item.count)
  const deptCosts = statistics.byDepartment.map(item => item.cost)
  
  const deptOption = {
    title: {
      text: '按部门统计',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['维修数量', '维修费用(元)'],
      top: 30
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: deptNames
    },
    yAxis: [
      {
        type: 'value',
        name: '维修数量',
        position: 'left'
      },
      {
        type: 'value',
        name: '维修费用',
        position: 'right',
        axisLabel: {
          formatter: '{value} 元'
        }
      }
    ],
    series: [
      {
        name: '维修数量',
        type: 'bar',
        data: deptCounts
      },
      {
        name: '维修费用(元)',
        type: 'line',
        yAxisIndex: 1,
        data: deptCosts
      }
    ]
  }
  
  departmentChart.setOption(deptOption)
  
  // 按月统计图表
  const monthChart = echarts.init(document.getElementById('month-chart'))
  monthChartRef.value = monthChart
  
  const months = statistics.byMonth.map(item => item.month)
  const monthCounts = statistics.byMonth.map(item => item.count)
  const monthCosts = statistics.byMonth.map(item => item.cost)
  
  const monthOption = {
    title: {
      text: '月度维修统计',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      }
    },
    legend: {
      data: ['维修数量', '维修费用'],
      top: 30
    },
    xAxis: [
      {
        type: 'category',
        data: months,
        axisPointer: {
          type: 'shadow'
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '维修数量',
        min: 0,
        position: 'left'
      },
      {
        type: 'value',
        name: '维修费用',
        min: 0,
        position: 'right',
        axisLabel: {
          formatter: '{value} 元'
        }
      }
    ],
    series: [
      {
        name: '维修数量',
        type: 'bar',
        data: monthCounts
      },
      {
        name: '维修费用',
        type: 'line',
        yAxisIndex: 1,
        data: monthCosts
      }
    ]
  }
  
  monthChart.setOption(monthOption)
  
  // 窗口变化时重绘图表
  window.addEventListener('resize', () => {
    typeChart.resize()
    departmentChart.resize()
    monthChart.resize()
  })
}

// 导出报表
const exportReport = () => {
  ElMessageBox.confirm('确定要导出维修报表吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const doc = document.createElement('a')
    
    // 构建 Excel CSV 内容
    let csvContent = '工单编号,资产名称,资产编码,资产类型,维修类型,部门,位置,优先级,申请人,申请时间,状态,维修人员,计划结束时间,实际完成时间,维修费用\n'
    
    maintenanceList.value.forEach(item => {
      const row = [
        item.requestNo,
        item.assetName,
        item.assetCode,
        item.assetType,
        item.maintenanceType,
        item.department,
        item.location,
        item.priority,
        item.requestUser,
        item.requestTime,
        item.status,
        item.assignedTo || '',
        item.plannedEndTime || '',
        item.actualEndTime || '',
        item.totalCost || '0'
      ]
      
      // 处理CSV中的特殊字符
      const sanitizedRow = row.map(cell => {
        const cellStr = String(cell)
        // 如果单元格内容包含逗号、双引号或换行符，则用双引号包裹并处理内部的双引号
        if (cellStr.includes(',') || cellStr.includes('"') || cellStr.includes('\n')) {
          return `"${cellStr.replace(/"/g, '""')}"`
        }
        return cellStr
      })
      
      csvContent += sanitizedRow.join(',') + '\n'
    })
    
    // 统计分析
    csvContent += '\n\n统计分析\n'
    csvContent += `总工单数,${statistics.total}\n`
    csvContent += `待处理工单数,${statistics.pending}\n`
    csvContent += `处理中工单数,${statistics.processing}\n`
    csvContent += `已完成工单数,${statistics.completed}\n`
    csvContent += `验收通过率,${statistics.acceptanceRate}%\n`
    csvContent += `平均响应时间,${statistics.averageResponseTime}小时\n`
    csvContent += `平均完成时间,${statistics.averageCompletionTime}小时\n`
    csvContent += `总费用,¥${statistics.totalCost.toLocaleString()}\n`
    
    // 按类型统计
    csvContent += '\n按维修类型统计\n'
    csvContent += '维修类型,数量\n'
    Object.entries(statistics.byType).forEach(([type, count]) => {
      csvContent += `${type},${count}\n`
    })
    
    // 按部门统计
    csvContent += '\n按部门统计\n'
    csvContent += '部门名称,维修数量,维修费用\n'
    statistics.byDepartment.forEach(dept => {
      csvContent += `${dept.name},${dept.count},¥${dept.cost.toLocaleString()}\n`
    })
    
    // 按月统计
    csvContent += '\n按月份统计\n'
    csvContent += '月份,维修数量,维修费用\n'
    statistics.byMonth.forEach(month => {
      csvContent += `${month.month},${month.count},¥${month.cost.toLocaleString()}\n`
    })
    
    // 添加生成信息
    csvContent += `\n\n报表生成时间,${new Date().toLocaleString()}\n`
    
    // 创建 Blob 对象 (使用 UTF-8 with BOM，以便 Excel 正确识别中文)
    const BOM = '\uFEFF' // UTF-8 BOM
    const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8' })
    
    // 创建下载链接
    doc.href = URL.createObjectURL(blob)
    doc.download = `维修报表-${new Date().toLocaleDateString()}.csv`
    
    // 模拟点击下载
    doc.click()
    
    // 释放 URL 对象
    URL.revokeObjectURL(doc.href)
    
    ElMessage.success('报表导出成功')
  }).catch(() => {})
}

// 跳转到详情页
const viewDetail = (id) => {
  router.push({
    name: 'MaintenanceDetail',
    params: { id }
  })
}

// 跳转到新增页
const addMaintenance = () => {
  router.push({
    name: 'MaintenanceAdd'
  })
}

// 打开审批对话框
const openApproveDialog = (row: MaintenanceItem) => {
  currentItem.value = row
  approveForm.comments = ''
  approveForm.assignedTo = ''
  approveForm.plannedStartTime = ''
  approveForm.plannedEndTime = ''
  approveDialogVisible.value = true
}

// 打开驳回对话框
const openRejectDialog = (row: MaintenanceItem) => {
  currentItem.value = row
  rejectForm.comments = ''
  rejectDialogVisible.value = true
}

// 打开验收对话框
const openAcceptDialog = (row: MaintenanceItem) => {
  currentItem.value = row
  acceptForm.comments = ''
  acceptForm.acceptanceResult = true
  acceptDialogVisible.value = true
}

// 审批通过
const handleApprove = () => {
  if (!approveForm.comments) {
    ElMessage.warning('请填写审批意见')
    return
  }
  
  if (!approveForm.assignedTo) {
    ElMessage.warning('请指派维修人员')
    return
  }
  
  if (!approveForm.plannedStartTime || !approveForm.plannedEndTime) {
    ElMessage.warning('请设置计划开始和结束时间')
    return
  }
  
  loading.value = true
  
  // 模拟API调用
  setTimeout(() => {
    if (currentItem.value) {
      const index = maintenanceList.value.findIndex(item => item.id === currentItem.value!.id)
      if (index !== -1) {
        maintenanceList.value[index].status = '已派工'
        maintenanceList.value[index].assignedTo = approveForm.assignedTo
        maintenanceList.value[index].plannedEndTime = approveForm.plannedEndTime
      }
    }
    
    approveDialogVisible.value = false
    ElMessage.success('审批通过，已派工')
    loading.value = false
  }, 1000)
  
  // 实际API调用
  // try {
  //   await approveMaintenance(currentItem.value!.id, true, approveForm.comments)
  //   await assignMaintenance(
  //     currentItem.value!.id, 
  //     approveForm.assignedTo, 
  //     approveForm.plannedStartTime, 
  //     approveForm.plannedEndTime
  //   )
  //   ElMessage.success('审批通过，已派工')
  //   fetchMaintenanceList()
  // } catch (error) {
  //   console.error('审批失败', error)
  //   ElMessage.error('审批失败')
  // } finally {
  //   approveDialogVisible.value = false
  //   loading.value = false
  // }
}

// 驳回申请
const handleReject = () => {
  if (!rejectForm.comments) {
    ElMessage.warning('请填写驳回原因')
    return
  }
  
  loading.value = true
  
  // 模拟API调用
  setTimeout(() => {
    if (currentItem.value) {
      const index = maintenanceList.value.findIndex(item => item.id === currentItem.value!.id)
      if (index !== -1) {
        maintenanceList.value[index].status = '已驳回'
      }
    }
    
    rejectDialogVisible.value = false
    ElMessage.success('已驳回申请')
    loading.value = false
  }, 1000)
  
  // 实际API调用
  // try {
  //   await approveMaintenance(currentItem.value!.id, false, rejectForm.comments)
  //   ElMessage.success('已驳回申请')
  //   fetchMaintenanceList()
  // } catch (error) {
  //   console.error('驳回失败', error)
  //   ElMessage.error('驳回失败')
  // } finally {
  //   rejectDialogVisible.value = false
  //   loading.value = false
  // }
}

// 验收维修
const handleAccept = () => {
  if (!acceptForm.comments) {
    ElMessage.warning('请填写验收意见')
    return
  }
  
  loading.value = true
  
  // 模拟API调用
  setTimeout(() => {
    if (currentItem.value) {
      const index = maintenanceList.value.findIndex(item => item.id === currentItem.value!.id)
      if (index !== -1) {
        maintenanceList.value[index].status = acceptForm.acceptanceResult ? '已验收' : '维修中'
      }
    }
    
    acceptDialogVisible.value = false
    ElMessage.success(acceptForm.acceptanceResult ? '验收通过' : '验收不通过，已退回')
    loading.value = false
  }, 1000)
  
  // 实际API调用
  // try {
  //   await acceptMaintenance(currentItem.value!.id, acceptForm.acceptanceResult, acceptForm.comments)
  //   ElMessage.success(acceptForm.acceptanceResult ? '验收通过' : '验收不通过，已退回')
  //   fetchMaintenanceList()
  // } catch (error) {
  //   console.error('验收失败', error)
  //   ElMessage.error('验收失败')
  // } finally {
  //   acceptDialogVisible.value = false
  //   loading.value = false
  // }
}

// 页面加载时初始化
onMounted(() => {
  fetchMaintenanceList()
  
  // 如果URL中有tab参数，切换到对应tab
  if (route.query.tab) {
    activeTab.value = route.query.tab as string
  }
})
</script>

<template>
  <div class="maintenance-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>资产维修与维保管理</h3>
          <div class="header-actions">
            <el-button type="primary" @click="addMaintenance">新增维修申请</el-button>
            <el-button type="success" @click="exportReport">导出报表</el-button>
          </div>
        </div>
      </template>

      <!-- 统计概览 -->
      <div class="statistics-overview">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-card shadow="hover" class="stat-card">
              <div class="stat-title">总维修工单</div>
              <div class="stat-value">{{ statistics.total }}</div>
              <div class="stat-footer">
                <span>验收通过率: {{ statistics.acceptanceRate }}%</span>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="stat-card">
              <div class="stat-title">待处理工单</div>
              <div class="stat-value">{{ statistics.pending }}</div>
              <div class="stat-footer">
                <span>平均响应时间: {{ statistics.averageResponseTime }}小时</span>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="stat-card">
              <div class="stat-title">处理中工单</div>
              <div class="stat-value">{{ statistics.processing }}</div>
              <div class="stat-footer">
                <span>平均完成时间: {{ statistics.averageCompletionTime }}小时</span>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="stat-card">
              <div class="stat-title">维修总费用</div>
              <div class="stat-value">¥{{ statistics.totalCost.toLocaleString() }}</div>
              <div class="stat-footer">
                <span>已完成工单: {{ statistics.completed }}</span>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 图表展示 -->
      <div class="chart-container">
        <el-row :gutter="20">
          <el-col :span="8">
            <div id="type-chart" class="chart"></div>
          </el-col>
          <el-col :span="8">
            <div id="department-chart" class="chart"></div>
          </el-col>
          <el-col :span="8">
            <div id="month-chart" class="chart"></div>
          </el-col>
        </el-row>
      </div>
      
      <!-- 内容标签页 -->
      <el-tabs v-model="activeTab" class="maintenance-tabs">
        <el-tab-pane label="维修申请管理" name="request">
          <!-- 搜索表单 -->
          <div class="search-form">
            <el-form :model="searchForm" inline>
              <el-form-item label="关键词">
                <el-input v-model="searchForm.keyword" placeholder="工单号/资产名称" clearable />
              </el-form-item>
              <el-form-item label="资产类型">
                <el-select v-model="searchForm.assetType" placeholder="全部" clearable>
                  <el-option label="办公设备" value="办公设备" />
                  <el-option label="生产设备" value="生产设备" />
                  <el-option label="运输设备" value="运输设备" />
                  <el-option label="电子设备" value="电子设备" />
                  <el-option label="通讯设备" value="通讯设备" />
                </el-select>
              </el-form-item>
              <el-form-item label="状态">
                <el-select v-model="searchForm.status" placeholder="全部" clearable>
                  <el-option label="维修申请" value="维修申请" />
                  <el-option label="已派工" value="已派工" />
                  <el-option label="维修中" value="维修中" />
                  <el-option label="维修完成" value="维修完成" />
                  <el-option label="已验收" value="已验收" />
                  <el-option label="已驳回" value="已驳回" />
                </el-select>
              </el-form-item>
              <el-form-item label="优先级">
                <el-select v-model="searchForm.priority" placeholder="全部" clearable>
                  <el-option label="低" value="低" />
                  <el-option label="中" value="中" />
                  <el-option label="高" value="高" />
                  <el-option label="紧急" value="紧急" />
                </el-select>
              </el-form-item>
              <el-form-item label="申请时间">
                <el-date-picker
                  v-model="searchForm.timeRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="fetchMaintenanceList">查询</el-button>
                <el-button @click="resetSearch">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
          
          <!-- 维修列表 -->
          <el-table
            v-loading="loading"
            :data="maintenanceList"
            style="width: 100%"
            border
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="requestNo" label="维修单号" width="120" />
            <el-table-column prop="assetName" label="资产名称" min-width="120" />
            <el-table-column prop="assetCode" label="资产编码" width="120" />
            <el-table-column prop="assetType" label="资产类型" width="120">
              <template #default="{ row }">
                <el-tag size="small">{{ row.assetType }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="maintenanceType" label="维修类型" width="120">
              <template #default="{ row }">
                <el-tag type="info" size="small">{{ row.maintenanceType }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="department" label="所属部门" width="120" />
            <el-table-column prop="faultDescription" label="故障描述" width="180" show-overflow-tooltip />
            <el-table-column prop="priority" label="优先级" width="80">
              <template #default="{ row }">
                <el-tag 
                  :type="row.priority === '紧急' ? 'danger' : row.priority === '高' ? 'warning' : row.priority === '中' ? 'success' : 'info'" 
                  size="small"
                >
                  {{ row.priority }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="requestUser" label="申请人" width="100" />
            <el-table-column prop="requestTime" label="申请时间" width="120" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag 
                  :type="
                    row.status === '维修申请' ? 'warning' : 
                    row.status === '已派工' || row.status === '维修中' ? 'primary' : 
                    row.status === '维修完成' || row.status === '已验收' ? 'success' : 
                    'danger'
                  " 
                  size="small"
                >
                  {{ row.status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="220" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" link @click="viewDetail(row.id)">详情</el-button>
                <el-button 
                  v-if="row.status === '维修申请'" 
                  type="success" 
                  link
                  @click="openApproveDialog(row)"
                >
                  审批
                </el-button>
                <el-button 
                  v-if="row.status === '维修申请'" 
                  type="danger" 
                  link
                  @click="openRejectDialog(row)"
                >
                  驳回
                </el-button>
                <el-button 
                  v-if="row.status === '维修完成'" 
                  type="success" 
                  link
                  @click="openAcceptDialog(row)"
                >
                  验收
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <!-- 分页 -->
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="searchForm.pageNum"
              v-model:page-size="searchForm.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              @size-change="fetchMaintenanceList"
              @current-change="fetchMaintenanceList"
            />
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="维保计划" name="plan">
          <div class="maintenance-plan">
            <el-alert
              title="维保计划功能正在建设中..."
              type="info"
              :closable="false"
              show-icon
            />
            <p class="plan-intro">
              维保计划功能将支持：
              <ul>
                <li>按设备类型批量创建维保计划</li>
                <li>支持多种周期类型（日常、每周、每月、每季度、每年、自定义）</li>
                <li>提前通知功能，短信/系统消息提醒</li>
                <li>维保任务汇总和统计</li>
                <li>与资产台账的无缝集成</li>
              </ul>
            </p>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="使用帮助" name="help">
          <div class="help-content">
            <h3>资产维修管理流程</h3>
            
            <el-steps :active="5" finish-status="success" process-status="finish" simple>
              <el-step title="提交申请" />
              <el-step title="审批派工" />
              <el-step title="维修执行" />
              <el-step title="完成维修" />
              <el-step title="验收结算" />
            </el-steps>
            
            <el-divider content-position="left">流程说明</el-divider>
            
            <el-row :gutter="20">
              <el-col :span="24">
                <el-collapse accordion>
                  <el-collapse-item title="1. 维修申请" name="1">
                    <div>
                      <p>任何资产使用人在发现资产故障时，可点击页面右上角的"新增维修申请"按钮进行报修。</p>
                      <p>申请时需要选择具体资产，填写故障描述、发生时间、优先级等信息，并可上传相关附件（如故障照片）。</p>
                      <p>提交后会自动通知相关负责人进行审批。</p>
                    </div>
                  </el-collapse-item>
                  
                  <el-collapse-item title="2. 审批派工" name="2">
                    <div>
                      <p>资产负责人收到维修申请后，可在系统中进行审批：</p>
                      <ul>
                        <li>如批准申请，需指派维修人员并设置计划维修时间。</li>
                        <li>如不批准，可选择驳回并填写驳回理由。</li>
                      </ul>
                      <p>审批通过后，系统会自动通知被指派的维修人员。</p>
                    </div>
                  </el-collapse-item>
                  
                  <el-collapse-item title="3. 维修执行" name="3">
                    <div>
                      <p>维修人员接到任务后，根据指派进行维修工作：</p>
                      <ul>
                        <li>可在维修详情页添加维修记录，记录维修过程。</li>
                        <li>可登记所用配件和材料。</li>
                        <li>可上传维修照片或其他文档。</li>
                      </ul>
                    </div>
                  </el-collapse-item>
                  
                  <el-collapse-item title="4. 完成维修" name="4">
                    <div>
                      <p>维修人员完成维修后，在系统中提交维修结果：</p>
                      <ul>
                        <li>填写详细的维修内容和结果。</li>
                        <li>登记维修费用（如有）。</li>
                        <li>上传相关文档（如测试报告）。</li>
                      </ul>
                      <p>提交后状态变为"维修完成"，等待验收。</p>
                    </div>
                  </el-collapse-item>
                  
                  <el-collapse-item title="5. 验收结算" name="5">
                    <div>
                      <p>资产使用人或管理员对维修结果进行验收：</p>
                      <ul>
                        <li>验收通过：维修流程结束，相关费用入账。</li>
                        <li>验收不通过：退回维修阶段，需重新进行维修。</li>
                      </ul>
                      <p>验收后系统会自动生成维修报告，可用于资产档案管理。</p>
                    </div>
                  </el-collapse-item>
                </el-collapse>
              </el-col>
            </el-row>
            
            <el-divider content-position="left">使用提示</el-divider>
            
            <el-alert
              title="报修时应尽可能详细描述故障情况，提供清晰的故障照片，有助于快速定位问题。"
              type="info"
              show-icon
              :closable="false"
              style="margin-bottom: 10px"
            />
            
            <el-alert
              title="维修人员在记录维修结果时，应详细说明维修方法和更换配件情况，便于后续资产故障分析。"
              type="warning"
              show-icon
              :closable="false"
              style="margin-bottom: 10px"
            />
            
            <el-alert
              title="系统提供统计分析功能，可以查看资产维修频率、故障类型分布、维修成本等数据，帮助优化资产管理策略。"
              type="success"
              show-icon
              :closable="false"
            />
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>
    
    <!-- 审批对话框 -->
    <el-dialog
      v-model="approveDialogVisible"
      title="审批维修申请"
      width="600px"
      destroy-on-close
    >
      <el-form :model="approveForm" label-width="100px">
        <el-form-item label="当前资产">
          <div v-if="currentItem">{{ currentItem.assetName }} ({{ currentItem.assetCode }})</div>
        </el-form-item>
        
        <el-form-item label="故障描述">
          <div v-if="currentItem">{{ currentItem.faultDescription }}</div>
        </el-form-item>
        
        <el-form-item label="审批意见" required>
          <el-input 
            v-model="approveForm.comments" 
            type="textarea" 
            :rows="3" 
            placeholder="请输入审批意见"
          />
        </el-form-item>
        
        <el-form-item label="指派维修人" required>
          <el-select v-model="approveForm.assignedTo" placeholder="请选择维修人员" style="width: 100%">
            <el-option label="王维修" value="王维修" />
            <el-option label="李工程师" value="李工程师" />
            <el-option label="张技术" value="张技术" />
            <el-option label="赵维护" value="赵维护" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="计划时间" required>
          <el-col :span="11">
            <el-date-picker
              v-model="approveForm.plannedStartTime"
              type="datetime"
              placeholder="开始时间"
              style="width: 100%"
            />
          </el-col>
          <el-col :span="2" class="text-center">
            <span class="text-gray-500">至</span>
          </el-col>
          <el-col :span="11">
            <el-date-picker
              v-model="approveForm.plannedEndTime"
              type="datetime"
              placeholder="结束时间"
              style="width: 100%"
            />
          </el-col>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="approveDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleApprove" :loading="loading">确认审批</el-button>
      </template>
    </el-dialog>
    
    <!-- 驳回对话框 -->
    <el-dialog
      v-model="rejectDialogVisible"
      title="驳回维修申请"
      width="500px"
      destroy-on-close
    >
      <el-form :model="rejectForm" label-width="100px">
        <el-form-item label="当前资产">
          <div v-if="currentItem">{{ currentItem.assetName }} ({{ currentItem.assetCode }})</div>
        </el-form-item>
        
        <el-form-item label="故障描述">
          <div v-if="currentItem">{{ currentItem.faultDescription }}</div>
        </el-form-item>
        
        <el-form-item label="驳回原因" required>
          <el-input 
            v-model="rejectForm.comments" 
            type="textarea" 
            :rows="3" 
            placeholder="请输入驳回原因"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="rejectDialogVisible = false">取消</el-button>
        <el-button type="danger" @click="handleReject" :loading="loading">确认驳回</el-button>
      </template>
    </el-dialog>
    
    <!-- 验收对话框 -->
    <el-dialog
      v-model="acceptDialogVisible"
      title="验收维修"
      width="600px"
      destroy-on-close
    >
      <el-form :model="acceptForm" label-width="100px">
        <el-form-item label="当前资产">
          <div v-if="currentItem">{{ currentItem.assetName }} ({{ currentItem.assetCode }})</div>
        </el-form-item>
        
        <el-form-item label="维修结果">
          <div v-if="currentItem">{{ currentItem.maintenanceResult || '暂无记录' }}</div>
        </el-form-item>
        
        <el-form-item label="验收结果" required>
          <el-radio-group v-model="acceptForm.acceptanceResult">
            <el-radio :label="true">通过</el-radio>
            <el-radio :label="false">不通过</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="验收意见" required>
          <el-input 
            v-model="acceptForm.comments" 
            type="textarea" 
            :rows="3" 
            placeholder="请输入验收意见"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="acceptDialogVisible = false">取消</el-button>
        <el-button 
          :type="acceptForm.acceptanceResult ? 'primary' : 'warning'" 
          @click="handleAccept" 
          :loading="loading"
        >
          {{ acceptForm.acceptanceResult ? '确认验收' : '验收不通过' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.maintenance-container {
  padding: 20px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
      margin: 0;
      font-size: 18px;
    }

    .header-actions {
      display: flex;
      gap: 10px;
    }
  }

  .statistics-overview {
    margin-bottom: 20px;

    .stat-card {
      text-align: center;
      padding: 10px;

      .stat-title {
        font-size: 14px;
        color: #606266;
        margin-bottom: 5px;
      }

      .stat-value {
        font-size: 24px;
        font-weight: bold;
        color: #303133;
        margin-bottom: 5px;
      }

      .stat-footer {
        font-size: 12px;
        color: #909399;
      }
    }
  }

  .chart-container {
    margin-bottom: 20px;

    .chart {
      height: 300px;
    }
  }
  
  .maintenance-tabs {
    margin-top: 20px;
  }

  .search-form {
    margin-bottom: 20px;
    padding: 16px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
  
  .maintenance-plan {
    padding: 20px;
    
    .plan-intro {
      margin-top: 20px;
      
      ul {
        margin-top: 10px;
        padding-left: 20px;
        
        li {
          margin-bottom: 8px;
          line-height: 1.5;
        }
      }
    }
  }

  .help-content {
    padding: 20px;
  }
}
</style> 