/**
 * 资产导入预览数据项
 */
export interface ImportPreviewItem {
  rowIndex: number;              // 行索引
  assetCode?: string;            // 资产编码
  assetName: string;             // 资产名称
  assetType: string;             // 资产类型
  assetCategory: string;         // 资产分类
  department: string;            // 所属部门
  purchaseDate: string;          // 购置日期
  originValue: number;           // 原值
  status: string;                // 资产状态
  location: string;              // 存放地点
  responsible: string;           // 责任人
  serialNumber?: string;         // 序列号
  manufacturer?: string;         // 制造商
  specModel?: string;            // 规格型号
  usefulLife?: number;           // 使用年限
  errorMessage?: string;         // 错误信息
  isValid: boolean;              // 是否有效
}

/**
 * 导入预览数据
 */
export interface ImportPreviewData {
  total: number; // 总记录数
  success: number; // 成功记录数
  error: number; // 错误记录数
  previewList: any[]; // 预览数据列表
  errorList: ImportError[]; // 错误数据列表
}

/**
 * 导入历史记录项
 */
export interface ImportHistoryItem {
  id: string; // 导入记录ID
  importType: ImportType; // 导入类型
  fileName: string; // 文件名
  totalCount: number; // 总记录数
  successCount: number; // 成功记录数
  errorCount: number; // 错误记录数
  status: ImportStatus; // 状态
  errorFileId?: string; // 错误文件ID
  operator: string; // 操作人
  createTime: string; // 创建时间
}

/**
 * 导入历史记录分页数据
 */
export interface ImportHistoryData {
  total: number;
  list: ImportHistoryItem[];
}

/**
 * 导入错误详情项
 */
export interface ImportErrorItem {
  rowIndex: number;              // 行索引
  assetName: string;             // 资产名称
  errorMessage: string;          // 错误信息
}

/**
 * 导入详情
 */
export interface ImportDetail {
  id: string; // 导入记录ID
  importType: ImportType; // 导入类型
  fileName: string; // 文件名
  fileSize: number; // 文件大小(KB)
  totalCount: number; // 总记录数
  successCount: number; // 成功记录数
  errorCount: number; // 错误记录数
  status: ImportStatus; // 状态
  errorFileId?: string; // 错误文件ID
  operator: string; // 操作人
  createTime: string; // 创建时间
  duration: number; // 处理耗时(秒)
  successList: any[]; // 成功数据列表
  errorList: ImportError[]; // 错误数据列表
}

/**
 * 导入类型枚举
 */
export enum ImportType {
  FIXED_ASSET = 'fixed',         // 固定资产
  CONSUMABLE = 'consumable',     // 耗材资产
  INTANGIBLE = 'intangible'      // 无形资产
}

/**
 * 导入资产数据项接口
 */
export interface ImportAssetItem {
  id: string
  assetCode: string
  assetName: string
  assetType: string
  assetCategory: string
  department: string
  purchaseDate: string
  price: number
  status: string
  hasError: boolean
  errorMsg?: string
}

/**
 * 导入预览数据接口
 */
export interface ImportPreviewData {
  fileId: string
  importType: string
  total: number
  valid: number
  invalid: number
  list: ImportAssetItem[]
}

/**
 * 导入历史记录项接口
 */
export interface ImportHistoryItem {
  id: string
  importType: string
  fileName: string
  totalCount: number
  successCount: number
  failCount: number
  status: string
  operatorName: string
  importTime: string
  errorFileId?: string
}

/**
 * 导入历史数据接口
 */
export interface ImportHistoryData {
  total: number
  list: ImportHistoryItem[]
}

/**
 * 导入详情错误项接口
 */
export interface ImportErrorItem {
  id: string
  rowNum: number
  assetCode: string
  assetName: string
  errorMsg: string
}

/**
 * 导入详情接口
 */
export interface ImportDetail {
  id: string
  importType: string
  fileName: string
  totalCount: number
  successCount: number
  failCount: number
  status: string
  operatorName: string
  importTime: string
  errorList?: ImportErrorItem[]
  errorFileId?: string
}

export interface ImportAsset {
  id: string
  name: string
  assetNo: string
  assetType: string
  department: string
  manager: string
  location: string
  purchaseDate: string
  price: number
  status: string
}

export interface ImportError {
  id: string
  row: number
  field: string
  message: string
}

export interface ImportPreviewData {
  total: number
  successCount: number
  errorCount: number
  assets: ImportAsset[]
  errors: ImportError[]
}

export interface ImportHistory {
  id: string
  importType: string
  fileName: string
  totalCount: number
  successCount: number
  errorCount: number
  status: string
  errorFileId: string | null
  operator: string
  createTime: string
}

export interface ImportHistoryData {
  total: number
  pageNum: number
  pageSize: number
  list: ImportHistory[]
}

export interface ImportDetail {
  id: string
  importType: string
  fileName: string
  totalCount: number
  successCount: number
  errorCount: number
  status: string
  errorFileId: string | null
  operator: string
  createTime: string
  assets: ImportAsset[]
} 