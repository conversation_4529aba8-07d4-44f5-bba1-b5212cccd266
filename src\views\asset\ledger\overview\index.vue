<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import * as echarts from 'echarts'
import { 
  Monitor, 
  Box, 
  Tools, 
  Warning, 
  Money, 
  TrendCharts 
} from '@element-plus/icons-vue'

const router = useRouter()

// 统计数据
const statistics = ref({
  totalCount: 0,
  totalValue: 0,
  maintenanceCount: 0,
  warningCount: 0,
  idleCount: 0,
  scrapCount: 0
})

// 图表实例
let typeChart: echarts.ECharts | null = null
let statusChart: echarts.ECharts | null = null
let trendChart: echarts.ECharts | null = null

// 获取统计数据
const fetchStatistics = async () => {
  try {
    // TODO: 调用API获取数据
    // 模拟数据
    statistics.value = {
      totalCount: 1256,
      totalValue: 12560000,
      maintenanceCount: 15,
      warningCount: 8,
      idleCount: 25,
      scrapCount: 12
    }
  } catch (error) {
    console.error('获取统计数据失败', error)
  }
}

// 初始化资产类型分布图表
const initTypeChart = () => {
  const chartDom = document.getElementById('typeChart')
  if (!chartDom) return
  
  typeChart = echarts.init(chartDom)
  const option = {
    title: {
      text: '资产类型分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: ['生产设备', '办公设备', '运输设备', '电子设备', '其他']
    },
    series: [
      {
        name: '资产类型',
        type: 'pie',
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '20',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 485, name: '生产设备' },
          { value: 310, name: '办公设备' },
          { value: 234, name: '运输设备' },
          { value: 135, name: '电子设备' },
          { value: 92, name: '其他' }
        ]
      }
    ]
  }
  
  typeChart.setOption(option)
}

// 初始化资产状态分布图表
const initStatusChart = () => {
  const chartDom = document.getElementById('statusChart')
  if (!chartDom) return
  
  statusChart = echarts.init(chartDom)
  const option = {
    title: {
      text: '资产状态分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['在用', '维修中', '闲置', '报废', '待验收']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '资产数量',
        type: 'bar',
        data: [980, 15, 25, 12, 8],
        itemStyle: {
          color: function(params: any) {
            const colorList = ['#67C23A', '#E6A23C', '#909399', '#F56C6C', '#409EFF']
            return colorList[params.dataIndex]
          }
        }
      }
    ]
  }
  
  statusChart.setOption(option)
}

// 初始化资产趋势图表
const initTrendChart = () => {
  const chartDom = document.getElementById('trendChart')
  if (!chartDom) return
  
  trendChart = echarts.init(chartDom)
  const option = {
    title: {
      text: '资产增长趋势',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['资产数量', '资产总值'],
      bottom: 0
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: [
      {
        type: 'value',
        name: '资产数量',
        position: 'left'
      },
      {
        type: 'value',
        name: '资产总值(万元)',
        position: 'right'
      }
    ],
    series: [
      {
        name: '资产数量',
        type: 'line',
        data: [1200, 1220, 1250, 1280, 1300, 1256]
      },
      {
        name: '资产总值',
        type: 'line',
        yAxisIndex: 1,
        data: [1200, 1220, 1250, 1280, 1300, 1256]
      }
    ]
  }
  
  trendChart.setOption(option)
}

// 跳转到资产明细
const goToAssetList = () => {
  router.push('/asset/ledger/list')
}

// 跳转到维修工单
const goToMaintenance = () => {
  router.push('/asset/maintenance/repair')
}

// 页面加载时初始化
onMounted(() => {
  fetchStatistics()
  initTypeChart()
  initStatusChart()
  initTrendChart()
  
  // 监听窗口大小变化，重绘图表
  window.addEventListener('resize', () => {
    typeChart?.resize()
    statusChart?.resize()
    trendChart?.resize()
  })
})
</script>

<template>
  <div class="asset-overview-container">
    <!-- 统计卡片 -->
    <el-row :gutter="20">
      <el-col :span="4">
        <el-card shadow="hover" class="stat-card" @click="goToAssetList">
          <template #header>
            <div class="card-header">
              <el-icon><Monitor /></el-icon>
              <span>资产总数</span>
            </div>
          </template>
          <div class="card-value">{{ statistics.totalCount }}</div>
          <div class="card-footer">台/套</div>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card shadow="hover" class="stat-card">
          <template #header>
            <div class="card-header">
              <el-icon><Money /></el-icon>
              <span>资产总值</span>
            </div>
          </template>
          <div class="card-value">¥{{ (statistics.totalValue / 10000).toFixed(2) }}</div>
          <div class="card-footer">万元</div>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card shadow="hover" class="stat-card" @click="goToMaintenance">
          <template #header>
            <div class="card-header">
              <el-icon><Tools /></el-icon>
              <span>维修中</span>
            </div>
          </template>
          <div class="card-value warning">{{ statistics.maintenanceCount }}</div>
          <div class="card-footer">台/套</div>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card shadow="hover" class="stat-card">
          <template #header>
            <div class="card-header">
              <el-icon><Warning /></el-icon>
              <span>预警资产</span>
            </div>
          </template>
          <div class="card-value danger">{{ statistics.warningCount }}</div>
          <div class="card-footer">台/套</div>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card shadow="hover" class="stat-card">
          <template #header>
            <div class="card-header">
              <el-icon><Box /></el-icon>
              <span>闲置资产</span>
            </div>
          </template>
          <div class="card-value info">{{ statistics.idleCount }}</div>
          <div class="card-footer">台/套</div>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card shadow="hover" class="stat-card">
          <template #header>
            <div class="card-header">
              <el-icon><TrendCharts /></el-icon>
              <span>报废资产</span>
            </div>
          </template>
          <div class="card-value">{{ statistics.scrapCount }}</div>
          <div class="card-footer">台/套</div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="chart-row">
      <el-col :span="8">
        <el-card class="chart-card">
          <div id="typeChart" class="chart"></div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="chart-card">
          <div id="statusChart" class="chart"></div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="chart-card">
          <div id="trendChart" class="chart"></div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<style lang="scss" scoped>
.asset-overview-container {
  padding: 20px;

  .stat-card {
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      transform: translateY(-5px);
    }

    .card-header {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #606266;
    }

    .card-value {
      font-size: 24px;
      font-weight: bold;
      color: #303133;
      margin: 10px 0;
      text-align: center;

      &.warning {
        color: #E6A23C;
      }

      &.danger {
        color: #F56C6C;
      }

      &.info {
        color: #909399;
      }
    }

    .card-footer {
      font-size: 12px;
      color: #909399;
      text-align: center;
    }
  }

  .chart-row {
    margin-top: 20px;
  }

  .chart-card {
    .chart {
      height: 300px;
    }
  }
}
</style> 