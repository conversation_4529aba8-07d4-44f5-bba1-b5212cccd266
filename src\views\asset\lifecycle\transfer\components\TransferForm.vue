<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { TransferForm as ITransferForm, AttachmentInfo } from '@/types/asset/lifecycle'
import { createTransfer } from '@/api/asset/lifecycle'
import AssetSelector from '@/components/AssetSelector/index.vue'
import FileUpload from '@/components/FileUpload/index.vue'
import UnitTreeSelect from '@/components/UnitTreeSelect/index.vue'

// 表单引用
const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive<ITransferForm>({
  title: '',
  sourceUnitId: 0,
  sourceUnitName: '',
  sourceDepartmentId: undefined,
  sourceDepartmentName: '',
  targetUnitId: 0,
  targetUnitName: '',
  targetDepartmentId: undefined,
  targetDepartmentName: '',
  transferReason: '',
  planTransferDate: '',
  assetList: [],
  remark: '',
  attachments: []
})

// 选择器引用
const assetSelectorRef = ref()

// 表单验证规则
const formRules = reactive<FormRules>({
  title: [
    { required: true, message: '请输入调拨标题', trigger: 'blur' },
    { min: 3, max: 100, message: '长度在 3 到 100 个字符', trigger: 'blur' }
  ],
  sourceUnitId: [
    { required: true, message: '请选择原单位', trigger: 'change' },
    { type: 'number', min: 1, message: '请选择有效的原单位', trigger: 'change' }
  ],
  targetUnitId: [
    { required: true, message: '请选择目标单位', trigger: 'change' },
    { type: 'number', min: 1, message: '请选择有效的目标单位', trigger: 'change' }
  ],
  transferReason: [
    { required: true, message: '请输入调拨原因', trigger: 'blur' },
    { min: 2, max: 200, message: '长度在 2 到 200 个字符', trigger: 'blur' }
  ],
  planTransferDate: [
    { required: true, message: '请选择计划调拨日期', trigger: 'change' }
  ],
  assetList: [
    { required: true, message: '请选择需要调拨的资产', trigger: 'change' },
    { type: 'array', min: 1, message: '至少选择一项资产', trigger: 'change' }
  ]
})

// 移除资产
const handleRemoveAsset = (index: number) => {
  formData.assetList.splice(index, 1)
}

// 源单位选择处理
const handleSourceUnitChange = (unitId: number) => {
  // 这里可以通过 unitId 获取单位名称
  formData.sourceUnitName = '水投集团' // 实际项目中应当根据 ID 获取名称
  // 清空源部门
  formData.sourceDepartmentId = undefined
  formData.sourceDepartmentName = ''
}

// 目标单位选择处理
const handleTargetUnitChange = (unitId: number) => {
  // 这里可以通过 unitId 获取单位名称
  if (unitId === 11) {
    formData.targetUnitName = '第1水厂'
  } else if (unitId === 12) {
    formData.targetUnitName = '第2水厂'
  } else if (unitId === 13) {
    formData.targetUnitName = '第3水厂'
  } else {
    formData.targetUnitName = '水投集团'
  }
  
  // 清空目标部门
  formData.targetDepartmentId = undefined
  formData.targetDepartmentName = ''
}

// 源部门选择处理
const handleSourceDepartmentChange = (departmentId: number) => {
  // 这里可以通过 departmentId 获取部门名称
  if (departmentId === 1001) {
    formData.sourceDepartmentName = '总经办'
  } else if (departmentId === 1002) {
    formData.sourceDepartmentName = '财务部'
  } else if (departmentId === 1003) {
    formData.sourceDepartmentName = '人力资源部'
  } else if (departmentId === 1004) {
    formData.sourceDepartmentName = '信息技术部'
  } else {
    formData.sourceDepartmentName = ''
  }
}

// 目标部门选择处理
const handleTargetDepartmentChange = (departmentId: number) => {
  // 这里可以通过 departmentId 获取部门名称
  if (departmentId === 100 + (formData.targetUnitId - 10) * 10 + 1) {
    formData.targetDepartmentName = '运营部'
  } else if (departmentId === 100 + (formData.targetUnitId - 10) * 10 + 2) {
    formData.targetDepartmentName = '技术部'
  } else if (departmentId === 100 + (formData.targetUnitId - 10) * 10 + 3) {
    formData.targetDepartmentName = '行政部'
  } else {
    formData.targetDepartmentName = ''
  }
}

// 重置表单
const resetForm = () => {
  if (!formRef.value) return
  formRef.value.resetFields()
  formData.assetList = []
  formData.remark = ''
  formData.attachments = []
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid, fields) => {
    if (valid) {
      try {
        // 校验源单位和目标单位不能相同
        if (formData.sourceUnitId === formData.targetUnitId) {
          ElMessage.warning('源单位和目标单位不能相同')
          return
        }
        
        // 模拟API调用
        setTimeout(() => {
          // 实际调用接口时使用下面的代码
          // await createTransfer(formData)
          ElMessage.success('调拨申请提交成功')
          emit('submit')
        }, 500)
      } catch (error) {
        console.error('提交调拨申请失败', error)
        ElMessage.error('提交调拨申请失败')
      }
    } else {
      console.log('验证失败', fields)
    }
  })
}

// 事件发射
const emit = defineEmits(['submit'])

// 提供重置方法给父组件
defineExpose({
  resetForm
})
</script>

<template>
  <div class="transfer-form">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      class="form-container"
    >
      <el-form-item label="调拨标题" prop="title">
        <el-input v-model="formData.title" placeholder="请输入调拨标题" />
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="原单位" prop="sourceUnitId">
            <UnitTreeSelect
              v-model="formData.sourceUnitId"
              placeholder="请选择原单位"
              @change="handleSourceUnitChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="原部门" prop="sourceDepartmentId">
            <UnitTreeSelect
              v-model="formData.sourceDepartmentId"
              placeholder="请选择原部门（可选）"
              :show-department="true"
              @change="handleSourceDepartmentChange"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="目标单位" prop="targetUnitId">
            <UnitTreeSelect
              v-model="formData.targetUnitId"
              placeholder="请选择目标单位"
              @change="handleTargetUnitChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="目标部门" prop="targetDepartmentId">
            <UnitTreeSelect
              v-model="formData.targetDepartmentId"
              placeholder="请选择目标部门（可选）"
              :show-department="true"
              @change="handleTargetDepartmentChange"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="调拨原因" prop="transferReason">
        <el-input
          v-model="formData.transferReason"
          type="textarea"
          :rows="3"
          placeholder="请输入调拨原因"
        />
      </el-form-item>

      <el-form-item label="计划调拨日期" prop="planTransferDate">
        <el-date-picker
          v-model="formData.planTransferDate"
          type="date"
          placeholder="请选择计划调拨日期"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="调拨资产" prop="assetList">
        <div class="asset-selector-wrapper">
          <AssetSelector
            ref="assetSelectorRef"
            v-model="formData.assetList"
            :filter-by-status="[1]" 
            title="选择调拨资产"
          />
          <!-- 假设状态 1 表示"在用" -->
        </div>
      </el-form-item>

      <!-- 已选资产列表 -->
      <el-form-item v-if="formData.assetList.length > 0" label="调拨清单">
        <el-table :data="formData.assetList" border style="width: 100%" max-height="400">
          <el-table-column type="index" width="50" />
          <el-table-column prop="assetCode" label="资产编号" width="120" />
          <el-table-column prop="assetName" label="资产名称" min-width="150" />
          <el-table-column prop="assetType.name" label="资产类型" width="120" />
          <el-table-column prop="model" label="型号" width="120" />
          <el-table-column prop="specification" label="规格" width="120" />
          <el-table-column prop="unitName" label="所属单位" width="120" />
          <el-table-column prop="departmentName" label="所属部门" width="120" />
          <el-table-column prop="statusName" label="状态" width="100">
            <template #default="{ row }">
              <el-tag
                :type="
                  row.statusId === 1 
                    ? 'success' 
                    : row.statusId === 2 
                    ? 'warning' 
                    : row.statusId === 3 
                    ? 'info' 
                    : row.statusId === 4 
                    ? 'danger' 
                    : 'primary'
                "
              >
                {{ row.statusName }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="90" fixed="right">
            <template #default="{ $index }">
              <el-button link type="danger" @click="handleRemoveAsset($index)">移除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>

      <el-form-item label="备注说明">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注说明（选填）"
        />
      </el-form-item>

      <el-form-item label="附件">
        <FileUpload v-model="formData.attachments" />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<style lang="scss" scoped>
.transfer-form {
  padding: 20px;

  .form-container {
    max-width: 1200px;
  }

  .asset-selector-wrapper {
    display: flex;
    align-items: center;
    gap: 10px;
  }
}
</style> 